# Copyright:	Public domain.
# Filename:	ASCENT_GUIDANCE.agc
# Purpose: 	Part of the source code for Luminary 1A build 099.
#		It is part of the source code for the Lunar Module's (LM)
#		Apollo Guidance Computer (AGC), for Apollo 11.
# Assembler:	yaYUL
# Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>.
# Website:	www.ibiblio.org/apollo.
# Pages:	843-856
# Mod history:	2009-05-23 HG	Transcribed from page images.
#		2009-06-05 RSB	Fixed a couple of typos.
#		2009-06-07 RSB	Corrected a typo.
#
# This source code has been transcribed or otherwise adapted from
# digitized images of a hardcopy from the MIT Museum.  The digitization
# was performed by <PERSON>, and arranged for by <PERSON> of
# the Museum.  Many thanks to both.  The images (with suitable reduction
# in storage size and consequent reduction in image quality as well) are
# available online at www.ibiblio.org/apollo.  If for some reason you
# find that the images are illegible, contact <NAME_EMAIL>
# about getting access to the (much) higher-quality images which <PERSON> actually created.
#
# Notations on the hardcopy document read, in part:
#
#	Assemble revision 001 of AGC program LMY99 by NASA 2021112-061
#	16:27 JULY 14, 1969

# Page 843
		BANK	34
		SETLOC	ASCFILT
		BANK

		EBANK=	DVCNTR

		COUNT*	$$/ASENT

ATMAG		TC	PHASCHNG
		OCT	00035
		TC	INTPRET
		BON
			FLRCS
			ASCENT
		DLOAD	DSU
			ABDVCONV
			MINABDV
		BMN	CLEAR
			ASCTERM4
			SURFFLAG
		CLEAR	SLOAD
			RENDWFLG
			BIT3H
		DDV	EXIT
			ABDVCONV
		DXCH	MPAC
		DXCH	1/DV3
		DXCH	1/DV2
		DXCH	1/DV1
		DXCH	1/DV0
		TC	INTPRET
		DLOAD	DAD
			1/DV0
			1/DV1
		DAD	DAD
			1/DV2
			1/DV3
		DMP	DMP
			VE
			2SEC(9)
		SL3	PDDL
			TBUP
		SR1	DAD
		DSU
			6SEC(18)
		STODL	TBUP
			VE
		SR1	DDV
			TBUP
		STCALL	AT
# Page 844
			ASCENT
BIT3H		OCT	4

# Page 845
		BANK	30
		SETLOC	ASENT
		BANK
		COUNT*	$$/ASENT

ASCENT		VLOAD	ABVAL
			R
		STOVL	/R/MAG
			ZAXIS1
		DOT	SL1
			V		# Z.V = ZDOT*2(-8).
		STOVL	ZDOT		# ZDOT*2(-7)
			ZAXIS1
		VXV	VSL1
			UNIT/R/		# Z X UR = LAXIS*2(-2)
		STORE	LAXIS		# LAXIS*2(-1)
		DOT	SL1
			V		# L.V = YDOT*2(-8).
		STCALL	YDOT		# YDOT * 2(-7)
			YCOMP
		VLOAD
			GDT1/2		# LOAD GDT1/2*2(-7) M/CS.
		V/SC	DOT
			2SEC(18)
			UNIT/R/		# G.UR*2(9) = GR*2(9).
		PDVL	VXV		# STORE IN PDL(0)
			UNIT/R/		# LOAD UNIT/R/ *2(-1)
			V		# UR*2(-1) X V*2(-7) = H/R*2(-8).
		VSQ	DDV		# H(2)/R(2)*2(-16).
			/R/MAG		# H(2)/R(3)*2(9).
		SL1	DAD
		STADR
		STODL	GEFF		# GEFF*2(10)m/CS/CS.
			ZDOTD
		DSU
			ZDOT
		STORE	DZDOT		# DZDOT = (ZDOTD - ZDOT) * 2(7) M/CS.
		VXSC	PDDL
			ZAXIS1
			YDOTD
		DSU
			YDOT
		STORE	DYDOT		# DYDOT = (YDOTD - YDOT) *2(7) M/CS.
		VXSC	PDDL
			LAXIS
			RDOTD
# Page 846
		DSU
			RDOT
		STORE	DRDOT		# DRDOT = (RDOTD - RDOT) * 2(7) M/CS.
		VXSC	VAD
			UNIT/R/
		VAD	VSL1
		STADR
		STORE	VGVECT		# VG = (DRDOT)R + (DVDOT)L + (DZDOT)Z.
		DLOAD	DMP		# LOAD TGO
			TGO		# TGO GEFF
			GEFF
		VXSC	VSL1
			UNIT/R/		# TGO GEFF UR
		BVSU
			VGVECT		# COMPENSATED FOR GEFF
		STORE	VGVECT		# STORE FOR DOWNLINK
		MXV	VSL1		# GET VGBODY FOR N85 DISPLAY
			XNBPIP
		STOVL	VGBODY
			VGVECT
		ABVAL	BOFF		# MAGNITUDE OF VGVECT
			FLRCS		# IF FLRCS=0,D0 NORMAL GUIDANCE
			MAINENG
		DDV			# USE TGO=VG/AT WITH RCS
			AT/RCS
		STCALL	TGO		# THIS WILL BE USED ON NEXT CYCLE
			ASCTERM2
MAINENG		DDV	PUSH		# VG/VE IN PDL(0)		(2)
			VE
		DMP	BDSU		# 1 - KT VG/VE
			KT1
			NEARONE
		DMP	DMP		# TBUP VG(1-KT VG/VE)/VE	(0)
			TBUP		# 	= TGO
		DSU			# COMPENSATE FOR TAILOFF
			TTO
		STORE	TGO
		SR	DCOMP
			11D
		STODL	TTOGO		# TGO *2(-28) CS
			TGO
		BON	DSU
			IDLEFLAG
			T2TEST
			4SEC(17)	# ( TGO - 4 )*2(-17) CS.
		BMN
			ENGOFF
T2TEST		DLOAD
			TGO
		DSU	BMN		# IF TGO - T2 NEG., GO TO CMPONENT
# Page 847
			T2A
			CMPONENT
		DLOAD	DSU
			TBUP
			TGO
		DDV	CALL		# 1- TGO/TBUP
			TBUP
			LOGSUB
		SL	PUSH		# -L IN PDL(0)			(2)
			5
		BDDV	BDSU		# -TGO/L*2(-17)
			TGO
			TBUP		# TBUP + TGO/L = D12*2(-17)
		PUSH	BON		# STORE IN PDL(2)		(4)
			FLPC		# IF FLPC = 1, GO TO CONST
			NORATES
		DLOAD	DSU
			TGO
			T3
		BPL	SET		# FLPC=1
			RATES
			FLPC
NORATES		DLOAD
			HI6ZEROS
		STORE	PRATE		# B = 0
		STORE	YRATE		# D = 0
		GOTO
			CONST		# GO TO CONST
RATES		DLOAD	DSU
			TGO
			02D		# TGO - D12 = D21*2(-17)
		PUSH	SL1		# IN PDL(4)			(6)
		BDSU	SL3		# (1/2TGO - D21)*2(-13) = E * 2(-13)
			TGO		#				(8)
		PDDL	DMP		# IN PDL(6)
			TGO
			RDOT		# RDOT TGO * 2(-24)
		DAD	DSU		# R + RDOT TGO
			/R/MAG		# R + RDOT TGO - RCO
			RCO		# MPAC = -DR *2(-24).
		PDDL	DMP		# -DR IN PDL(8)			(10)
			DRDOT
			04D		# D21 DRDOT*2(-24)
		DAD	SL2		# (D21 DRDOT-DR)*2(-22)		(8)
		DDV	DDV
			06D		# (D21 DRDOT-DR)/E*2(-9)
			TGO
		STORE	PRATE		# B * 2(8)
		BMN	DLOAD		# B>0 NOT PERMITTED
			CHKBMAG
# Page 848
			HI6ZEROS
		STCALL	PRATE
			PROK
CHKBMAG		SR4	DDV		# B*2(4)
			TBUP		# (B / TAU) * 2(21)
		DSU	BPL
			PRLIMIT		# ( B / TAU ) = 2(21) MAX.
			PROK
		DLOAD	DMP
			PRLIMIT
			TBUP		# B MAX. * 2(4)
		SL4			# BMAX*2(8)
		STORE	PRATE
PROK		DLOAD
			TGO
		DMP	DAD		# YDOT TGO
			YDOT
			Y		# Y + YDOT TGO
		DSU	PDDL		# Y + YDOT TGO - YCO
			YCO		# MPAC = - DY*(-24.) IN PDL(8)	(10)
			DYDOT
		DMP	DAD		# D21 DYDOT - DY		(8)
			04D
		SL2	DDV		# (D21 DYDOT - DY)/E*2(-9)
		DDV	SETPD		# (D21 DYDOT - DY)/E TGO*2(8)
			TGO		#	= D*2(8)
			04
		STORE	YRATE
CONST		DLOAD	DMP		# LOAD B*2(8)
			PRATE		# B D12*2(-9)
			02D
		PDDL	DDV		# D12 B IN PDL(4)	(6)
			DRDOT		# LOAD DRDOT*2(-7)
			00D		# -DRDOT/L*2(-7)
		SR2	DSU		# (-DRDOT/L-D12 B)=A*2(-9)	(4)
		STADR
		STODL	PCONS
			YRATE		# D*2(8)
		DMP	PDDL		# D12 D,EXCH WITH -L IN PDL(0)	(2,2)
		BDDV	SR2		# -DYDOT/L*2(-9)
			DYDOT
		DSU			# (-DYDOT/L-D12 D)=C*2(-9)
			00D
		STORE	YCONS
CMPONENT	SETPD	DLOAD
			00D
 			100CS
 		DMP
			PRATE		# B(T-T0)*2(-9)
		DAD	DDV		# (A+B(T-T0))*2(-9)
# Page 849
			PCONS		# (A+B(T-T0))/TBUP*2(8)
			TBUP
		SL1	DSU
			GEFF		# ATR*2(9)
		STODL	ATR
			100CS
		DMP	DAD
			YRATE
			YCONS		# (C+D(T-T0))*2(-9)
		DDV	SL1
			TBUP
		STORE	ATY		# ATY*2(9)
		VXSC	PDDL		# ATY UY*2(8)		(6)
			LAXIS
			ATR
		VXSC	VAD
			UNIT/R/
		VSL1	PUSH		# AH*2(9) IN PDL(0)	(6)
		ABVAL	PDDL		# AH(2) IN PDL(34)
			AT		# AHMAG IN PDL(6)	(8)
		DSQ	DSU		# (AT(2)-AH(2))*2(18)
			34D		# =ATP2*2(18)
		PDDL	PUSH		#			(12)
			AT
		DSQ	DSU		# (AT(2)KR(2)-AH(2))*2(18)	(10)
			34D		# =ATP3*2(18)
		BMN	DLOAD		# IF ATP3 NEG,GO TO NO-ATP
			NO-ATP		# LOAD ATP2, IF ATP3 POS
			8D
		SQRT	GOTO		# ATP*2(9)
			AIMER
NO-ATP		DLOAD	BDDV		# KR AT/AH = KH		(8)
			6D
		VXSC			# KH AG*2(9)
			00D
		STODL	00D		# STORE NEW AH IN PDL(0)
			HI6ZEROS
AIMER		SIGN
			DZDOT
		STORE	ATP
		VXSC
			ZAXIS1		# ATP ZAXIS *2(8).
		VSL1	VAD		# AT*2(0)
			00D
		STORE	UNFC/2		# WILL BE OVERWRITTEN IF IN VERT. RISE.
		SETPD	BON
			00D
			FLPI
			P12RET
		BON
# Page 850
			FLVR
			CHECKALT
MAINLINE	VLOAD	VCOMP
			UNIT/R/
		STODL	UNWC/2
			TXO
		DSU	BPL
			PIPTIME
			ASCTERM
		BON
			ROTFLAG
			ANG1CHEK
CLRXFLAG	CLEAR	CLEAR
			NOR29FLG	# START r29 IN ASCENT PHASE.
			XOVINFLG	# ALLOW X-AXIS OVERRIDE
ASCTERM		EXIT
		CA	FLAGWRD9
		MASK	FLRCSBIT
		CCS	A
		TCF	ASCTERM3
		TC	INTPRET
		CALL
			FINDCDUW -2
ASCTERM1	EXIT
 +1		CA	FLAGWRD9	# INSURE THAT THE NOUN 63 DISPLAY IS
 		MASK	FLRCSBIT	# BYPASSED IF WE ARE IN THE RCS TRIMMING
		CCS	A		# MODE OF OPERATION
		TCF	ASCTERM3
		CA	FLAGWRD8	# BYPASS DISPLAYS IF ENGINE FAILURE IS
		MASK	FLUNDBIT	# INDICATED.
		CCS	A
		TCF	ASCTERM3
		CAF	V06N63*
		TC	BANKCALL
		CADR	GODSPR
		TCF	ASCTERM3
ASCTERM2	EXIT
ASCTERM3	TCF	ENDOFJOB
ASCTERM4	EXIT
		INHINT
		TC	IBNKCALL	# NO GUIDANCE THIS CYCLE -- HENCE ZERO
		CADR	ZATTEROR	# THE DAP COMMANDED ERRORS.
		TCF	ASCTERM1 +1

CHECKALT	DLOAD	DSU
			/R/MAG
			/LAND/
		DSU	BMN		# IF H LT 25K CHECK Z AXIS ORIENTATION
			25KFT
			CHECKYAW
# Page 851
EXITVR		CLEAR	BON
			FLVR
			ROTFLAG
			MAINLINE
		DLOAD	DAD
			PIPTIME
			10SECS
		STCALL	TXO
			MAINLINE
EXITVR1		CLRGO
			ROTFLAG
			EXITVR

		SETLOC	ASENT1
		BANK
		COUNT*	$$/ASENT

ANG1CHEK	VLOAD	DOT
			UNFC/2
			XNBPIP
		DSU	BPL
			COSTHET1
			OFFROT
		VLOAD	DOT
			XNBPIP
			UNIT/R/
		DSU	BMN
			COSTHET2
			KEEPVR1
OFFROT		CLRGO
			ROTFLAG
			CLRXFLAG

		BANK	7
		SETLOC	ASENT2
		BANK
		COUNT*	$$/ASENT

SETXFLAG	=	CHECKYAW

CHECKYAW	SET
			XOVINFLG	# PROHIBIT X-AXIS OVERRIDE
		DLOAD	VXSC
			ATY
			LAXIS
		PDDL	VXSC
			ATP
			ZAXIS1
		VAD	UNIT
		PUSH	DOT
# Page 852
			YNBPIP
		ABS	DSU
			SIN5DEG
		BPL	DLOAD
			KEEPVR
			RDOT
		DSU	BPL
			40FPS
			EXITVR1
		GOTO
			KEEPVR

		BANK	5
		SETLOC	ASENT3
		BANK
		COUNT*	$$/ASENT

SIN5DEG		2DEC	0.08716 B-2
40FPS		2DEC	0.12192 B-7

		BANK	14
		SETLOC	ASENT4
		BANK
		COUNT*	$$/ASENT

KEEPVR		VLOAD	STADR		# RECALL LOSVEC FROM PUSHLIST
		STORE	UNWC/2
KEEPVR1		VLOAD
			UNIT/R/
		STCALL	UNFC/2
			ASCTERM

ENGOFF		RTB
			LOADTIME
		DSU	DAD
			PIPTIME
			TTOGO
		DCOMP	EXIT
		TC	TPAGREE		# FORCE SIGN AGREEMENT ON MPAC, MPAC +1.
		CAF	EBANK7
		TS	EBANK
		EBANK=	TGO
		INHINT
		CCS	MPAC +1
		TCF	+3		# C(A) = DT - 1 BIT
		TCF	+2		# C(A) = 0
		CAF	ZERO		# C(A) = 0
		AD	BIT1		# C(A) = 1 BIT OR DT.
# Page 853
		TS	ENGOFFDT
		TC	TWIDDLE
		ADRES	ENGOFF1
		TC	PHASCHNG
		OCT	47014
		-GENADR	ENGOFFDT
		EBANK=	TGO
		2CADR	ENGOFF1

		TC	INTPRET
		SET	GOTO
			IDLEFLAG	# DISABLE DELTA-V MONITOR
			T2TEST

ENGOFF1		TC	IBNKCALL	# SHUT OFF THE ENGINE.
		CADR	ENGINOF2

		CAF	PRIO17		# SET UP A JOB FOR THE ASCENT GUIDANCE
		TC	FINDVAC		# POSTBURN LOGIC.
		EBANK=	WHICH
		2CADR	CUTOFF

		TC	PHASCHNG
		OCT 	07024
		OCT	17000
		EBANK=	TGO
		2CADR	CUTOFF

		TCF	TASKOVER

CUTOFF		TC	UPFLAG		# SET FLRCS FLAG.
		ADRES	FLRCS

 -5		CAF	V16N63
 		TC	BANKCALL
		CADR	GOFLASH
		TCF	+3
		TCF	CUTOFF1
		TCF	-5

 +3		TC	POSTJUMP
 		CADR	TERMASC

CUTOFF1		INHINT
		TC	IBNKCALL	# ZERO ATTITUDE ERRORS BEFORE REDUCINT DB.
		CADR	ZATTEROR
		TC	IBNKCALL
		CADR	SETMINDB
		TC	POSTJUMP
		CADR	CUTOFF2
# Page 854

V16N63		VN	1663
		BANK	30
		SETLOC	ASENT5
		BANK
		COUNT*	$$/ASENT

CUTOFF2		TC	PHASCHNG
		OCT	04024

		CAF	V16N85C
		TC	BANKCALL
		CADR	GOFLASH
		TCF	TERMASC
		TCF	+2		# PROCEED
		TCF	CUTOFF2

TERMASC		TC	PHASCHNG
		OCT	04024

		INHINT			# RESTORE DEADBAND DESIRED BY ASTRONAUT.
		TC	IBNKCALL
		CADR	RESTORDB
		TC	DOWNFLAG	# DISALLOW ABORTS AT THIS TIME.
		ADRES	LETABORT
		TCF	GOTOPOOH

V16N85C		VN	1685

		BANK 27
		SETLOC	ASENT1
		BANK
		COUNT* $$/ASENT

YCOMP		VLOAD	DOT
			UNIT/R/
			QAXIS
		SL2	DMP
			RCO
		STORE	Y
		RVQ

		BANK	30
		SETLOC	ASENT
		BANK
# Page 855
100CS		EQUALS	2SEC(18)
T2A		EQUALS	2SEC(17)
4SEC(17)	2DEC	400 B-17
2SEC(17)	2DEC	200 B-17
T3		2DEC	1000 B-17
6SEC(18)	2DEC	600 B-18
BIT4H		OCT	10
2SEC(9)		2DEC	200 B-9
V06N63*		VN	0663
V06N76		VN	0676
V06N33A		VN	0633

		BANK	33
		SETLOC	ASENT6
		BANK
		COUNT*	$$/ASENT

KT1		2DEC	0.5000
PRLIMIT		2DEC	-.0639		# (B/TBUP)MIN=-.1FT.SEC(-3)
MINABDV		2DEC	.0356 B-5	# 10 PERCENT BIGGER THAN GRAVITY
1/DV0		=	MASS1

# Page 856
# THE LOGARITHM SUBROUTINE

		BANK	24
		SETLOC	FLOGSUB
		BANK

# INPUT ..... X IN MPAC
# OUTPUT ..... -LOG(X) IN MPAC

LOGSUB		NORM	BDSU
			MPAC +6
			NEARONE
		EXIT
		TC	POLY
		DEC	6
		2DEC	.**********
		2DEC	-.**********
		2DEC	-.**********
		2DEC	-.**********
		2DEC	-.**********
		2DEC	-.**********
		2DEC	.**********
		2DEC	-.**********

		CAF	ZERO
		TS	MPAC +2
		EXTEND
		DCA	CLOG2/32
		DXCH	MPAC
		DXCH	BUF +1
		CA	MPAC +6
		TC	SHORTMP
		DXCH	MPAC +1
		DXCH	MPAC
		DXCH	BUF +1
		DAS	MPAC
		TC	INTPRET
		DCOMP	RVQ

CLOG2/32	2DEC	.**********


# Copyright:	Public domain.
# Filename:	LAMBERT_AIMPOINT_GUIDANCE.agc
# Purpose: 	Part of the source code for Luminary 1A build 099.
#		It is part of the source code for the Lunar Module's (LM)
#		Apollo Guidance Computer (AGC), for Apollo 11.
# Assembler:	yaYUL
# Contact:	<PERSON> <<EMAIL>>.
# Website:	www.ibiblio.org/apollo.
# Pages:	651-653
# Mod history:	2009-05-18 RSB	Transcribed from Luminary 099
#				page images.
#		2009-06-05 RSB	Corrected 4 typos.
#		2009-06-07 RSB	Fixed a typo.
#
# This source code has been transcribed or otherwise adapted from
# digitized images of a hardcopy from the MIT Museum.  The digitization
# was performed by <PERSON>, and arranged for by <PERSON> of
# the Museum.  Many thanks to both.  The images (with suitable reduction
# in storage size and consequent reduction in image quality as well) are
# available online at www.ibiblio.org/apollo.  If for some reason you
# find that the images are illegible, contact <NAME_EMAIL>
# about getting access to the (much) higher-quality images which <PERSON> actually created.
#
# Notations on the hardcopy document read, in part:
#
#	Assemble revision 001 of AGC program LMY99 by NASA 2021112-061
#	16:27 JULY 14,1969

# Page 651

# GENERAL  LAMBERT AIMPOINT GUIDANCE **
# WRITTEN  BY RAMA M AIYAWAR

# PROGRAM P-31 DESCRIPTION **
#
# 1.	   TO ACCEPT TARGETING PARAMETERS OBTAINED FROM A SOURCE EXTERNAL
#	   TO THE LEM AND COMPUTE THERE FROM THE REQUIRED-VELOCITY AND
#	   OTHER INITIAL CONDITIONS REQUIRED BY LM FOR DESIRED MANEUVER.
#	   THE TARGETING PARAMETERS ARE TIG (TIME OF IGNITION), TARGET
#	   VECTOR (RTARG), AND THE TIME FROM TIG UNTIL THE TARGET IS
#	   REACHED(DELLT4),DESIRED TIME OF FLIGHT FROM  RINIT TO RTARG..

# ASSUMPTIONS **
#
# 1.	   THE TARGET PARAMETERS MAY HAVE BEEN LOADED PRIOR TO THE
#	   EXECUTION OF THIS PROGRAM.
# 2.	   THIS PROGRAM IS APPLICABLE IN EITHER EARTH OR LUNAR ORBIT.
# 3.	   THIS PROGRAM IS DESIGNED FOR ONE-MAN OPERATION, AND SHOULD
#	   BE SELECTED BY THE ASTRONAUT BY DSKY ENTRY  V37 E31.

# SUBROUTINES USED **
#
# MANUPARM, TTG/N35, R02BOTH, MIDGIM, DISPMGA, FLAGDOWN, BANKCALL,
# GOTOPOOH, ENDOFJOB, PHASCHNG, GOFLASHR, GOFLASH.
#
# MANUPARM	  CALCULATES APOGEE, PERIGEE ALTITUDES AND DELTAV DESIRED
#		  FOR THE MANEUVER.
#
# TTG/N35	  CLOCKTASK - UPDATES CLOCK.
#
# MIDGIM	  CALCULATES MIDDLE GIMBAL ANGLE FOR DISPLAY.
#
# R02BOTH	  IMU - STATUS CHECK ROUTINE.

# DISPLAYS USED IN P-31LM **
#
# V06N33	  DISPLAY SOTRED  TIG (IN HRS. MINS. SECS)
# V06N42	  DISPLAY APOGEE, PERIGEE, DELTAV.
# V16N35	  DISPLAY TIME FROM TIG.
# V06N45	  TIME FROM IGNITION AND MIDDLE GIMBAL ANGLE.

# ERASABLE INITIALIZATION REQUIRED **
#
# TIG		  TIME OF IGNITION    DP    (B+28) CS.
#
# DELLT4	  DESIRED TIME OF FLIGHT   DP  (B+28) CS
#		  FROM RINIT TO RTARG .
#
# RTARG		  RADIUS VECTOR OF TARGET POSITION VECTOR
#		  RADIUS VECTOR   SCALED TO  (B+29)METERS IF EARTH ORBIT
# Page 652
#		  RADIUS VECTOR SCALED TO    (B+27)METERS IF MOON  ORBIT

# OUTPUT **
#
# HAPO		  APOGEE ALTITUDE
# HPER		  PERIGEE ALTITUDE
# VGDISP	  MAG.OF DELTAV FOR DISPLAY ,SCALING	  B+7 M/CS EARTH
#		  MAG.OF DELTAV FOR DISPLAY,SCALING	  B+5 M/CS MOON
# MIDGIM	  MIDDLE GIMBAL ANGLE
# XDELVFLG	  RESETS XDELVFLG FOR LAMBERT VG COMPUTATIONS

# ALARMS OR ABORTS  NONE **

# RESTARTS  ARE VIA GROUP 4 **

		SETLOC	GLM
		BANK

		EBANK=	SUBEXIT

		COUNT*	$$/P31
P31		TC	P20FLGON
		CAF	V06N33		# TIG
		TC	VNPOOH
		TC	INTPRET
		CLEAR	DLOAD
			UPDATFLG
			TIG
		STCALL	TDEC1		# INTEGRATE STATE VECTORS TO TIG
			LEMPREC
		VLOAD	SETPD
			RATT
			0D
		STORE	RTIG
		STOVL	RINIT
			VATT
		STORE	VTIG
		STODL	VINIT
			P30ZERO
		PUSH	PDDL		# E4 AND NUMIT = 0
			DELLT4
		DAD	SXA,1
			TIG
			RTX1
		STORE	TPASS4
		SXA,2	CALL
			RTX2
			INITVEL
		VLOAD	PUSH
# Page 653
			DELVEET3
		STORE	DELVSIN
		ABVAL	CLEAR
			XDELVFLG
		STCALL	VGDISP
			GET.LVC
		VLOAD	PDVL
			RTIG
			VIPRIME
		CALL
			PERIAPO1
		CALL
			SHIFTR1
		CALL			# LIMIT DISPLAY TO 9999.9 N. MI.
			MAXCHK
		STODL	HPER
			4D
		CALL
			SHIFTR1
		CALL			# LIMIT DISPLAY TO 9999.9 N. MI.
			MAXCHK
		STORE	HAPO
		EXIT
		CAF	V06N81		# DELVLVC
		TC	VNPOOH
		CAF	V06N42		# HAPO, HPER, VGDISP
		TC	VNPOOH
		TC	INTPRET
REVN1645	SET	CALL		# TRKMKCNT, TTOGO, +MGA
			FINALFLG
			VN1645
		GOTO
			REVN1645


# *** END OF LEMP30S .103 ***

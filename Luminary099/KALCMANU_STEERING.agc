# Copyright:	Public domain.
# Filename:	<PERSON>ALCM<PERSON>U_STEERING.agc
# Purpose: 	Part of the source code for Luminary 1A build 099.
#		It is part of the source code for the Lunar Module's (LM)
#		Apollo Guidance Computer (AGC), for Apollo 11.
# Assembler:	yaYUL
# Contact:	<PERSON> <<EMAIL>>.
# Website:	www.ibiblio.org/apollo.
# Pages:	365-369
# Mod history:	2009-05-17 RSB	Adapted from the corresponding
#				Luminary131 file, using page
#				images from Luminary 1A.
#		2011-01-06 JL	Added missing comment characters.
#
# This source code has been transcribed or otherwise adapted from
# digitized images of a hardcopy from the MIT Museum.  The digitization
# was performed by <PERSON>, and arranged for by <PERSON> of
# the Museum.  Many thanks to both.  The images (with suitable reduction
# in storage size and consequent reduction in image quality as well) are
# available online at www.ibiblio.org/apollo.  If for some reason you
# find that the images are illegible, contact <NAME_EMAIL>
# about getting access to the (much) higher-quality images which <PERSON> actually created.
#
# Notations on the hardcopy document read, in part:
#
#	Assemble revision 001 of AGC program LMY99 by NASA 2021112-061
#	16:27 JULY 14, 1969

# Page 365
# GENERATION OF STEERING COMMANDS FOR DIGITAL AUTOPILOT FREE FALL MANEUVERS
#
# NEW COMMANDS WILL BE GENERATED EVERY ONE SECOND DURING THE MANEUVER

		EBANK=	TTEMP

NEWDELHI	TC	BANKCALL	# CHECK FOR AUTO STABILIZATION
		CADR	ISITAUTO	# ONLY
		CCS	A
		TCF	NOGO -2
NEWANGL		TC	INTPRET
		AXC,1	AXC,2
			MIS		# COMPUTE THE NEW MATRIX FROM S/C TO
			KEL		# STABLE MEMBER AXES
		CALL
			MXM3
		VLOAD	STADR
		STOVL	MIS +12D	# CALCULATE NEW DESIRED CDU ANGLES
		STADR
		STOVL	MIS +6D
		STADR
		STORE	MIS
		AXC,1	CALL
			MIS
			DCMTOCDU	# PICK UP THE NEW CDU ANGLES FROM MATRIX
		RTB
			V1STO2S
		STORE	NCDU		# NEW CDU ANGLES
		BONCLR	EXIT
			CALCMAN2
			MANUSTAT	# TO START MANEUVER
		CAF	TWO		#	   +0 OTHERWISE
INCRDCDU	TS	SPNDX
		INDEX	SPNDX
		CA	BCDU		# INITIAL CDU ANGLES
		EXTEND			# OR PREVIOUS DESIRED CDU ANGLES
		INDEX	SPNDX
		MSU	NCDU
		EXTEND
		SETLOC	KALCMON1
		BANK
		MP	DT/TAU
		CCS	A		# CONVERT TO 2S COMPLEMENT
		AD	ONE
		TCF	+2
		COM
		INDEX	SPNDX
		TS	DELDCDU		# ANGLE INCREMENTS TO BE ADDED TO
		INDEX	SPNDX		# CDUXD, CDUYD, CDUZD EVERY TENTH SECOND
# Page 366
		CA	NCDU		# BY LEM DAP
		INDEX	SPNDX
		XCH	BCDU
		INDEX	SPNDX
		TS	CDUXD
		CCS	SPNDX
		TCF	INCRDCDU	# LOOP FOR THREE AXES

		RELINT

# COMPARE PRESENT TIME WITH TIME TO TERMINATE MANEUVER

TMANUCHK	TC	TIMECHK
		TCF	CONTMANU
		CAF	ONE
MANUSTAL	INHINT			# END MAJOR PART OF MANEUVER WITHIN 1 SEC
		TC	WAITLIST	# UNDER WAITLIST CALL TO MANUSTOP
		EBANK=	TTEMP
		2CADR	MANUSTOP

		RELINT
		TCF	ENDOFJOB

TIMECHK		EXTEND
		DCS	TIME2
		DXCH	TTEMP
		EXTEND
		DCA	TM
		DAS	TTEMP
		CCS	TTEMP
		TC	Q
		TCF	+2
		TCF	2NDRETRN
		CCS	TTEMP +1
		TC	Q
		TCF	MANUOFF
		COM
MANUOFF		AD	ONESEK +1
		EXTEND
		BZMF	2NDRETRN
		INCR	Q
2NDRETRN	INCR	Q
		TC	Q

DT/TAU		DEC	.1

MANUSTAT	EXIT			# INITIALIZATION ROUTINE
		EXTEND			# FOR AUTOMATIC MANEUVERS
		DCA	TIME2
# Page 367
		DAS	TM		# TM+TO	   MANEUVER COMPLETION TIME
		EXTEND
		DCS	ONESEK
		DAS	TM		# (TM+TO)-1
		INHINT
		CAF	TWO
RATEBIAS	TS	KSPNDX
		DOUBLE
		TS	KDPNDX
		INDEX	A
		CA	BRATE
		INDEX	KSPNDX		# STORE MANEUVER RATE IN
		TS	OMEGAPD		# OMEGAPD, OMEGAQD, OMEGARD
		EXTEND
		BZMF	+2		# COMPUTE ATTITUDE ERROR
		COM			# OFFSET = (WX)ABS(WX)/2AJX
		EXTEND			# WHERE AJX= 2-JET ACCELERATION
		MP	BIASCALE	# = -1/16
		EXTEND
		INDEX	KDPNDX
		MP	BRATE
		EXTEND
		INDEX	KSPNDX
		DV	1JACC		# =AJX	$ 90 DEG/SEC-SEC
		INDEX	KSPNDX
		TS	DELPEROR	#     $ 180 DEG
		CCS	KSPNDX
		TCF	RATEBIAS

		CA	TIME1
		AD	ONESEK +1
		XCH	NEXTIME
		TCF	INCRDCDU -1

ONESEK		DEC	0
		DEC	100

BIASCALE	OCT	75777		# = -1/16

CONTMANU	CS	TIME1		# RESET FOR NEXT DCDU UPDATE
		AD	NEXTIME
		CCS	A
		AD	ONE
		TCF	MANUCALL
		AD	NEGMAX
		COM
MANUCALL	INHINT			# CALL FOR NEXT UPDATE VIA WAITLIST
		TC	WAITLIST
		EBANK=	TTEMP
		2CADR	UPDTCALL
# Page 368
		CAF	ONESEK +1	# INCREMENT TIME FOR NEXT UPDATE
		ADS	NEXTIME
		TCF	ENDOFJOB

UPDTCALL	CAF	PRIO26		# SATELLITE PROGRAM TO CALL FOR UPDATE
		TC	FINDVAC		# OF STEERING COMMANDS
		EBANK=	TTEMP
		2CADR	NEWDELHI

		TC	TASKOVER

# Page 369
# ROUTINE FOR TERMINATING AUTOMATIC MANEUVERS

MANUSTOP	CAF	ZERO		# ZERO MANEUVER RATES
		TS	DELDCDU2
		TS	OMEGARD
		TS	DELREROR
		TS	DELDCDU1
		TS	OMEGAQD
		TS	DELQEROR
		CA	CPSI		# SET DESIRED GIMBAL ANGLES TO
		TS	CDUZD		# DESIRED FINAL GIMBAL ANGLES
		CA	CTHETA
		TS	CDUYD
ENDROLL		CA	CPHI		# NO FINAL YAW
		TS	CDUXD
		CAF	ZERO
		TS	OMEGAPD		# I.E. MANEUVER DID NOT GO THRU
		TS	DELDCDU		# GIMBAL LOCK ORIGINALLY
		TS	DELPEROR
GOODMANU	CA	ATTPRIO		# RESTORE USERS PRIO
		TS	NEWPRIO

		CA	ZERO		# ZERO ATTCADR
		DXCH	ATTCADR

		TC	SPVAC		# RETURN TO USER

		TC	TASKOVER

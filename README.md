# Apollo-11

[![NASA][1]][2]
[![SWH]][SWH_URL]
[![Comanche]][ComancheMilestone]
[![Luminary]][LuminaryMilestone]

🌐
[Azerbaijani][AZ],
[bahasa Indonesia][ID],
[Català][CA],
[<PERSON><PERSON>š<PERSON>][CZ],
[Dansk][DA],
[Deutsch][DE],
[English][EN],
[Español][ES],
[Français][FR],
[Galego][GL],
[Italiano][IT],
[Kurdî][KU],
[Lietuvių][LT],
[Mongolian][MN],
[Nederlands][NL],
[Norsk][NO],
[Polski][PL],
[Português][PT_BR],
[Română][RO],
[Svenska][SV],
[tiếng Việt][VI],
[Türkçe][TR],
[Ελληνικά][GR],
[Беларуская мова][BE],
[Русский][RU],
[Українська][UK],
[العربية][AR],
[فارسی][FA],
[नेपाली भाषा][NE]
[हिंदी][HI_IN],
[অসমীয়া][AS_IN],
[বাংলা][BD_BN],
[မြန်မာ][MM],
[한국어][KO_KR],
[日本語][JA],
[正體中文][ZH_TW],
[简体中文][ZH_CN]

[AR]:Translations/README.ar.md
[AS_IN]:Translations/README.as_in.md
[AZ]:Translations/README.az.md
[BD_BN]:Translations/README.bd_bn.md
[BE]:Translations/README.be.md
[CA]:Translations/README.ca.md
[CZ]:Translations/README.cz.md
[DA]:Translations/README.da.md
[DE]:Translations/README.de.md
[EN]:README.md
[ES]:Translations/README.es.md
[FA]:Translations/README.fa.md
[FR]:Translations/README.fr.md
[GL]:Translations/README.gl.md
[GR]:Translations/README.gr.md
[HI_IN]:Translations/README.hi_in.md
[ID]:Translations/README.id.md
[IT]:Translations/README.it.md
[JA]:Translations/README.ja.md
[KO_KR]:Translations/README.ko_kr.md
[KU]:Translations/README.ku.md
[LT]:Translations/README.lt.md
[MM]:Translations/README.mm.md
[MN]:Translations/README.mn.md
[NE]:Translations/README.ne.md
[NL]:Translations/README.nl.md
[NO]:Translations/README.no.md
[PL]:Translations/README.pl.md
[PT_BR]:Translations/README.pt_br.md
[RO]:Translations/README.ro.md
[RU]:Translations/README.ru.md
[SV]:Translations/README.sv.md
[TR]:Translations/README.tr.md
[UK]:Translations/README.uk.md
[VI]:Translations/README.vi.md
[ZH_CN]:Translations/README.zh_cn.md
[ZH_TW]:Translations/README.zh_tw.md

Original Apollo 11 guidance computer (AGC) source code for Command Module (Comanche055) and Lunar Module (Luminary099). Digitized by the folks at [Virtual AGC][3] and [MIT Museum][4]. The goal is to be a repo for the original Apollo 11 source code. As such, PRs are welcome for any issues identified between the transcriptions in this repository and the original source scans for [Luminary 099][5] and [Comanche 055][6], as well as any files I may have missed.

## Contributing

Please read [CONTRIBUTING.md][7] before opening a pull request.

## Compiling

If you are interested in compiling the original source code, check
out [Virtual AGC][8].

## Attribution

&nbsp;         | &nbsp;
:------------- | :-----
Copyright      | Public domain
Comanche055    | Part of the source code for Colossus 2A, the Command Module's (CM) Apollo Guidance Computer (AGC) for Apollo 11<br>`Assemble revision 055 of AGC program Comanche by NASA`<br>`2021113-051. 10:28 APR. 1, 1969`
Luminary099    | Part of the source code for Luminary 1A, the Lunar Module's (LM) Apollo Guidance Computer (AGC) for Apollo 11<br>`Assemble revision 001 of AGC program LMY99 by NASA`<br>`2021112-061. 16:27 JUL. 14, 1969`
Assembler      | yaYUL
Contact        | Ron Burkey <<EMAIL>>
Website        | www.ibiblio.org/apollo
Digitalisation | This source code has been transcribed or otherwise adapted from digitized images of a hardcopy from the MIT Museum. The digitization was performed by Paul Fjeld, and arranged for by Deborah Douglas of the Museum. Many thanks to both.

### Contract and Approvals

*Derived from [CONTRACT_AND_APPROVALS.agc]*

This AGC program shall also be referred to as Colossus 2A.

This program is intended for use in the Command Module as specified in report `R-577`. This program was prepared under DSR project `55-23870`, sponsored by the Manned Spacecraft Center of The National Aeronautics and Space Administration through contract `NAS 9-4065` with the Instrumentation Laboratory, Massachusetts Institute of Technology, Cambridge, Mass.

Submitted by         | Role | Date
:------------------- | :--- | :---
Margaret H. Hamilton | Colossus Programming Leader<br>Apollo Guidance and Navigation | 28 Mar 69

Approved by       | Role | Date
:---------------- | :--- | :---
Daniel J. Lickly  | Director, Mission Program Development<br>Apollo Guidance and Navigation Program | 28 Mar 69
Fred H. Martin    | Colossus Project Manager<br>Apollo Guidance and Navigation Program | 28 Mar 69
Norman E. Sears   | Director, Mission Development<br>Apollo Guidance and Navigation Program | 28 Mar 69
Richard H. Battin | Director, Mission Development<br>Apollo Guidance and Navigation Program | 28 Mar 69
David G. Hoag     | Director<br>Apollo Guidance and Navigation Program | 28 Mar 69
Ralph R. Ragan    | Deputy Director<br>Instrumentation Laboratory | 28 Mar 69

[CONTRACT_AND_APPROVALS.agc]:https://github.com/chrislgarry/Apollo-11/blob/master/Comanche055/CONTRACT_AND_APPROVALS.agc
[1]:https://flat.badgen.net/badge/NASA/Mission%20Overview/0B3D91
[2]:https://www.nasa.gov/mission_pages/apollo/missions/apollo11.html
[3]:http://www.ibiblio.org/apollo/
[4]:http://web.mit.edu/museum/
[5]:http://www.ibiblio.org/apollo/ScansForConversion/Luminary099/
[6]:http://www.ibiblio.org/apollo/ScansForConversion/Comanche055/
[7]:https://github.com/chrislgarry/Apollo-11/blob/master/CONTRIBUTING.md
[8]:https://github.com/rburkey2005/virtualagc
[SWH]:https://flat.badgen.net/badge/Software%20Heritage/Archive/0B3D91
[SWH_URL]:https://archive.softwareheritage.org/browse/origin/https://github.com/chrislgarry/Apollo-11/
[Comanche]:https://flat.badgen.net/github/milestones/chrislgarry/Apollo-11/1
[ComancheMilestone]:https://github.com/chrislgarry/Apollo-11/milestone/1
[Luminary]:https://flat.badgen.net/github/milestones/chrislgarry/Apollo-11/2
[LuminaryMilestone]:https://github.com/chrislgarry/Apollo-11/milestone/2

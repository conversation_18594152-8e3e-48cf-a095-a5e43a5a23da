# योगदान

🌐
[Azerbaijani][AZ],
[bahasa Indonesia][ID],
[Català][CA]،
[Čeština][CZ],
[Dansk][DA],
[Deutsch][DE],
[English][EN],
[Español][ES],
[Français][FR],
[Galego][GL],
[Italiano][IT],
[Kurdi][KU],
[Kurdî][KU],
[Lietuvių][LT],
[Mongolia][MN],
[Nederlands][NL],
[Norsk][NO],
[Polski][PL],
[Português][PT_BR],
[Svenska][SV],
[tiếng Việt][VI],
[Türkçe][TR],
[Ελληνικά][GR],
[Українська][UK]،
[العربية][AR],
[हिन्दी][HI_IN],
[한국어][KO_KR],
[日本語][JA],
[正體中文][ZH_TW],
[简体中文][ZH_CN]

[AR]:CONTRIBUTING.ar.md
[AZ]:CONTRIBUTING.az.md
[CA]:CONTRIBUTING.ca.md
[CZ]:CONTRIBUTING.cz.md
[DA]:CONTRIBUTING.da.md
[DE]:CONTRIBUTING.de.md
[EN]:../CONTRIBUTING.md
[ES]:CONTRIBUTING.es.md
[FR]:CONTRIBUTING.fr.md
[GL]:CONTRIBUTING.gl.md
[GR]:CONTRIBUTING.gr.md
[HI_IN]:CONTRIBUTING.hi_in.md
[ID]:CONTRIBUTING.id.md
[IT]:CONTRIBUTING.it.md
[JA]:CONTRIBUTING.ja.md
[KO_KR]:CONTRIBUTING.ko_kr.md
[KU]:CONTRIBUTING.ku.md
[LT]:CONTRIBUTING.lt.md
[MN]:CONTRIBUTING.mn.md
[NL]:CONTRIBUTING.nl.md
[NO]:CONTRIBUTING.no.md
[PL]:CONTRIBUTING.pl.md
[PT_BR]:CONTRIBUTING.pt_br.md
[SV]:CONTRIBUTING.sv.md
[TR]:CONTRIBUTING.tr.md
[UK]:CONTRIBUTING.uk.md
[VI]:CONTRIBUTING.vi.md
[ZH_CN]:CONTRIBUTING.zh_cn.md
[ZH_TW]:CONTRIBUTING.zh_tw.md

इस रिपॉजिटरी में स्रोत कोड को पेपर प्रिंटआउट से मैन्युअल रूप से डिजिटाइज़ किया गया था, इसलिए गलती से टाइपो और अन्य विसंगतियों को पेश किया गया है। निम्नलिखित स्कैन किए गए प्रिंटआउट के अनुरूप बनाने के लिए कोड को संशोधित किया जाएगा:

- [Comanche के लिए AGC प्रिंटआउट][8]
- [Luminary के लिए AGC प्रिंटआउट][9]

निम्नलिखित वेबसाइट का उपयोग कॉमंच और ल्यूमिनरी दोनों के स्कैन किए गए प्रिंटआउट को आसानी से नेविगेट करने के लिए किया जा सकता है: https://28gpc.csb.app/

## उपयोगी एक्सटेंशन

GitHub में अंतर्निहित AGC असेंबली भाषा के लिए सिंटैक्स समर्थन है। दुर्भाग्य से आपका कोड संपादक नहीं होगा, हालांकि एजीसी भाषा एक्सटेंशन हैं जो निम्नलिखित संपादकों के लिए सिंटैक्स हाइलाइटिंग प्रदान करते हैं::

- [Atom][Atom]†
- [CodeBlocks][CodeBlocks]
- [Eclipse][Eclipse]
- [Kate][Kate]
- [ProgrammersNotepad][ProgrammersNotepad]
- [Sublime Text 3][Sublime Text]†
- [TextPad][TextPad]
- [Vim][Vim]
- [Visual Studio Code][VisualStudioCode]†
- [jEdit][jEdit]

† स्वचालित स्वरूपण का समर्थन करता है

[Atom]:https://github.com/Alhadis/language-agc
[CodeBlocks]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/CodeBlocks
[Eclipse]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/Eclipse
[Kate]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/Kate
[ProgrammersNotepad]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/ProgrammersNotepad
[Sublime Text]:https://github.com/jimlawton/AGC-Assembly
[TextPad]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/TextPad
[Vim]:https://github.com/wsdjeg/vim-assembly
[VisualStudioCode]:https://github.com/wopian/agc-assembly
[jEdit]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/jEdit

## का प्रारूपण

**टिप्पणी:** GitHub और ऊपर चिह्नित एक्सटेंशन सुनिश्चित करेंगे कि आप स्वचालित रूप से सही स्वरूपण का उपयोग कर रहे हैं।

- टैब इंडेंटेशन का प्रयोग करें
- 8 की टैब चौड़ाई का प्रयोग करें
- पिछली सफेद जगह ट्रिम करें

## मैं क्या जाँच करूँ?

इस भंडार में स्कैन और स्रोत कोड के बीच कोई भी विसंगतियां।

### टिप्पणियाँ

प्रूफ़िंग करते समय आपको जिन सामान्य मुद्दों पर ध्यान देना चाहिए उनमें शामिल हैं, लेकिन इन्हीं तक सीमित नहीं:

### टंकण त्रुटियाँ

कुछ जगहों पर, मूल डेवलपर्स ने टिप्पणी लिखते समय टाइपोग्राफिक त्रुटियां कीं। इनमें से कुछ को प्रारंभिक डिजिटलीकरण के दौरान गलती से ठीक कर दिया गया था, हालांकि डिजिटलीकरण ने टाइपोग्राफिक त्रुटियों को भी पेश किया है जो स्कैन में मौजूद नहीं थे।

उदाहरण के लिए, यदि डिजीटल टिप्पणियों में `SPACECRAFT` शामिल है, लेकिन `SPAECRAFT` स्कैन में मुद्रित किया गया था, तो डिजिटलीकरण को `SPAECRAFT` (लापता `C`) में सही किया जाना चाहिए।

इसी तरह, यदि किसी शब्द में डिजिटाइजेशन में टाइपो है लेकिन स्कैन में सही वर्तनी है तो टाइपो को सही किया जाना चाहिए।

### खाली स्थान

टिप्पणियों में दो वर्णों के बीच रिक्त स्थान स्कैन से मेल खाना चाहिए। ज्यादातर मामलों में (चर्चा देखें [#316][10]):

- नए शब्दों के लिए सिंगल स्पेस।
- नए वाक्यों के लिए डबल स्पेस।
- इंडेंटेशन के लिए ट्रिपल स्पेस।

स्कैन के सभी पृष्ठ इस सामान्यीकरण का पालन नहीं करते हैं, यदि स्कैन में दोहरे स्थान के बजाय केवल एक ही स्थान है, तो एकल स्थान का उपयोग करें।

### कतार टूट जाती है

- कॉलम 1 में `R0000` के *साथ* लाइन ब्रेक स्कैन से बिल्कुल मेल खाना चाहिए।
- कॉलम 1 में `R0000` के *बिना* लाइन ब्रेक में एक पंक्ति में केवल 1 या 2 खाली लाइनें होनी चाहिए।
  - यदि 2 से अधिक रिक्त रेखाएँ विराम हैं, तो अतिरिक्त रेखा विरामों को हटा दें।
    - कॉलम 1 में `R0000` वाली पंक्तियों की गणना इसमें नहीं की जाती है।
  - स्रोत छवियों में, ये कॉलम 8 में एक अमुद्रित अंक द्वारा बनाए गए थे। ए 2 ने एक डबल स्पेस (एकल रिक्त रेखा) को मजबूर किया और एक 3 ने ट्रिपल स्पेस (डबल रिक्त रेखा) को मजबूर कर दिया। मान 4-8 परिभाषित किए गए थे लेकिन कभी उपयोग नहीं किए गए। [#159][7] में इसके बारे में और पढ़ें।

उदाहरण के लिए निम्नलिखित:

```plain
R0819   SUBROUTINE TO SKIP...
R0820



 0821   LAMPTEST  CS  IMODES33
```

बन जाना चाहिए:

```plain
R0819   SUBROUTINE TO SKIP...
R0820


 0820   LAMPTEST  CS  IMODES33
```

## टिप्पणी

पीआर करने से पहले, कृपया सुनिश्चित करें कि आपके परिवर्तन स्कैन के अनुरूप हैं!

[0]:https://github.com/chrislgarry/Apollo-11/pull/new/master
[1]:http://www.ibiblio.org/apollo/ScansForConversion/Luminary099/
[2]:http://www.ibiblio.org/apollo/ScansForConversion/Comanche055/
[6]:https://github.com/wopian/agc-assembly#user-settings
[7]:https://github.com/chrislgarry/Apollo-11/issues/159
[8]:http://www.ibiblio.org/apollo/ScansForConversion/Comanche055/
[9]:http://www.ibiblio.org/apollo/ScansForConversion/Luminary099/
[10]:https://github.com/chrislgarry/Apollo-11/pull/316#pullrequestreview-102892741

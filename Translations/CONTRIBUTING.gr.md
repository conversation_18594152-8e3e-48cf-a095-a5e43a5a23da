# Συνεισφορά

🌐
[Azerbaijani][AZ],
[bahasa Indonesia][ID],
[Català][CA]،
[Čeština][CZ],
[Dansk][DA],
[Deutsch][DE],
[English][EN],
[Español][ES],
[Français][FR],
[Galego][GL],
[Italiano][IT],
[Kurdi][KU],
[Kurdî][KU],
[Lietuvių][LT],
[Mongolia][MN],
[Nederlands][NL],
[Norsk][NO],
[Polski][PL],
[Português][PT_BR],
[Svenska][SV],
[tiếng Việt][VI],
[Türkçe][TR],
[Ελληνικά][GR],
[Українська][UK]،
[العربية][AR],
[हिन्दी][HI_IN],
[한국어][KO_KR],
[日本語][JA],
[正體中文][ZH_TW],
[简体中文][ZH_CN]

[AR]:CONTRIBUTING.ar.md
[AZ]:CONTRIBUTING.az.md
[CA]:CONTRIBUTING.ca.md
[CZ]:CONTRIBUTING.cz.md
[DA]:CONTRIBUTING.da.md
[DE]:CONTRIBUTING.de.md
[EN]:../CONTRIBUTING.md
[ES]:CONTRIBUTING.es.md
[FR]:CONTRIBUTING.fr.md
[GL]:CONTRIBUTING.gl.md
[GR]:CONTRIBUTING.gr.md
[HI_IN]:CONTRIBUTING.hi_in.md
[ID]:CONTRIBUTING.id.md
[IT]:CONTRIBUTING.it.md
[JA]:CONTRIBUTING.ja.md
[KO_KR]:CONTRIBUTING.ko_kr.md
[KU]:CONTRIBUTING.ku.md
[LT]:CONTRIBUTING.lt.md
[MN]:CONTRIBUTING.mn.md
[NL]:CONTRIBUTING.nl.md
[NO]:CONTRIBUTING.no.md
[PL]:CONTRIBUTING.pl.md
[PT_BR]:CONTRIBUTING.pt_br.md
[SV]:CONTRIBUTING.sv.md
[TR]:CONTRIBUTING.tr.md
[UK]:CONTRIBUTING.uk.md
[VI]:CONTRIBUTING.vi.md
[ZH_CN]:CONTRIBUTING.zh_cn.md
[ZH_TW]:CONTRIBUTING.zh_tw.md

Ο πηγαίος κώδικας σε αυτό το αποθετήριο ψηφιοποιήθηκε χειροκίνητα (με μη αυτόματο τρόπο) από εκτυπώσεις σε χαρτί, έτσι τυχαία λάθη και άλλες αποκλίσεις μπορεί να έχουν εισαχθεί κατά λάθος. Ο κώδικας πρέπει να τροποποιείται ώστε πάντα να είναι συνεπής με τις παρακάτω σαρωμένες εκτυπώσεις:

- [AGC printouts for Comanche][8]
- [AGC printouts for Luminary][9]

Ο παρακάτω ιστότοπος μπορεί να χρησιμοποιηθεί για εύκολη πλοήγηση στις σαρωμένες εκτυπώσεις τόσο για το Comanche όσο και για το Luminary: https://28gpc.csb.app/

## Χρήσιμες Επεκτάσεις

Το GitHub διαθέτει υποστήριξη συντακτικού για τη γλώσσα assembly του AGC. Δυστυχώς, ο επεξεργαστής κώδικά σας μπορεί να μην την υποστηρίζει, ωστόσο, υπάρχουν επεκτάσεις για τη γλώσσα assembly AGC που παρέχουν επισήμανση σύνταξης για τους ακόλουθους επεξεργαστές κώδικα:

- [Atom][Atom]†
- [CodeBlocks][CodeBlocks]
- [Eclipse][Eclipse]
- [Kate][Kate]
- [ProgrammersNotepad][ProgrammersNotepad]
- [Sublime Text 3][Sublime Text]†
- [TextPad][TextPad]
- [Vim][Vim]
- [Visual Studio Code][VisualStudioCode]†
- [jEdit][jEdit]

† Υποστηρίζει αυτόματη μορφοποίηση

[Atom]:https://github.com/Alhadis/language-agc
[CodeBlocks]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/CodeBlocks
[Eclipse]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/Eclipse
[Kate]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/Kate
[ProgrammersNotepad]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/ProgrammersNotepad
[Sublime Text]:https://github.com/jimlawton/AGC-Assembly
[TextPad]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/TextPad
[Vim]:https://github.com/wsdjeg/vim-assembly
[VisualStudioCode]:https://github.com/wopian/agc-assembly
[jEdit]:https://github.com/virtualagc/virtualagc/tree/master/Contributed/SyntaxHighlight/jEdit

## Mορφοποίηση

Σημείωση: Το GitHub και οι επεκτάσεις που σημειώνονται παραπάνω θα διασφαλίσουν ότι χρησιμοποιείτε τη σωστή μορφοποίηση αυτόματα.

- Χρήση εσοχής με tab
- 8 χαρακτήες κενού για εσοχή tab
- Περικοπή τελικών κενών χαρακτήρων γραμμής

## Τι να ελέγξω;

Τυχόν αποκλίσεις μεταξύ των σαρώσεων και του πηγαίου κώδικα σε αυτό το αποθετήριο.

### Σχόλια

Τα σχόλια στον μεταγραμμένο κώδικα **ΠΡΕΠΕΙ** να ταιριάζουν **ακριβώς** με τις σαρώσεις.

Συχνά προβλήματα που θα μπορούσατε να ψάξετε κατά τη διόρθωση περιλαμβάνουν αλλά δεν περιορίζονται σε:

#### Τυπογραφικά λάθη

Σε μερικά σημεία, οι αρχικοί προγραμματιστές έκαναν τυπογραφικά λάθη κατά τη σύνταξη σχολίων. Μερικά από αυτά διορθώθηκαν κατά λάθος κατά την αρχική ψηφιοποίηση, ωστόσο η ψηφιοποίηση έχει επίσης εισαγάγει τυπογραφικά λάθη που δεν υπήρχαν στις σαρώσεις.

Για παράδειγμα, εάν τα ψηφιοποιημένα σχόλια περιείχαν `SPACECRAFT`, αλλά `SPAECRAFT` ήταν γραμμένο στις σαρώσεις, τότε η ψηφιοποίηση **ΠΡΕΠΕΙ** να διορθωθεί σε `SPAECRAFT` (λείπει το `C`).

Αντίστοιχα, αν μια λέξη έχει κάποιο τυπογραφικό λάθος στην ψηφιοποίηση αλλά είναι γραμμένη σωστά στις σαρώσεις τότε αυτό το τυπογραφικό λάθος **ΠΡΕΠΕΙ** να διορθωθεί.

#### Κενά

Τα κενά μεταξύ χαρακτήρων στα σχόλια **ΘΑ ΕΠΡΕΠΕ** να ταιριάζουν με τις σαρώσεις. Τις περισσότερες φορές (δείτε τη συζήτηση στο [#316[10], αυτό σημαίνει:

- Ένα κενό για νέες λέξεις.
- Δύο κενά για νέες προτάσεις.
- Τρία κενά για εσοχές.

Αυτή η γενίκευση δεν ακολουθείτε από όλες τις σαρωμένες σελίδες, αν μια σάρωση έχει ένα κενό αντί για δύο, χρησιμοποιήστε ένα.

### Αλλαγές γραμμής

- Οι αλλαγές γραμμής με `R0000` στη στήλη 1 πρέπει να ταιριάζουν ακριβώς με τις σαρώσεις.
- Οι αλλαγές γραμμής *χωρίς* `R0000` στη στήλη 1 πρέπει να περιέχουν μόνο 1 ή 2 κενές γραμμές στη σειρά.
  - Εάν υπάρχουν περισσότερες από 2 αλλαγές γραμμών, αφαιρέστε τις επιπλέον αλλαγές γραμμής.
    - Οι γραμμές με `R0000` στη στήλη 1 δεν υπολογίζονται σε αυτό.
  - Στις εικόνες προέλευσης, αυτές δημιουργήθηκαν από ένα μη τυπωμένο ψηφίο στη στήλη 8. Ένα 2 ανάγκαζε ένα διπλό διάστημα (μονή κενή γραμμή) και ένα 3 ανάγκαζε ένα τριπλό διάστημα (διπλή κενή γραμμή). Οι τιμές 4-8 ορίστηκαν αλλά δεν χρησιμοποιήθηκαν ποτέ. Διαβάστε περισσότερα σχετικά, στο [#159][7]

Για παράδειγμα το παρακάτω:

```plain
R0819   SUBROUTINE TO SKIP...
R0820



 0821   LAMPTEST  CS  IMODES33
```

Πρέπει να γίνει:

```plain
R0819   SUBROUTINE TO SKIP...
R0820


 0820   LAMPTEST  CS  IMODES33
```

## Σημείωση

Πριν κάνετε ένα PR, παρακαλώ βεβαιωθείτε ότι οι αλλαγές σας είναι σύμφωνες με τις σαρώσεις!

[0]:https://github.com/chrislgarry/Apollo-11/pull/new/master
[1]:http://www.ibiblio.org/apollo/ScansForConversion/Luminary099/
[2]:http://www.ibiblio.org/apollo/ScansForConversion/Comanche055/
[6]:https://github.com/wopian/agc-assembly#user-settings
[7]:https://github.com/chrislgarry/Apollo-11/issues/159
[8]:http://www.ibiblio.org/apollo/ScansForConversion/Comanche055/
[9]:http://www.ibiblio.org/apollo/ScansForConversion/Luminary099/
[10]:https://github.com/chrislgarry/Apollo-11/pull/316#pullrequestreview-102892741

# 阿波罗 11 号

[![NASA][1]][2]
[![SWH]][SWH_URL]
[![Comanche]][ComancheMilestone]
[![Luminary]][LuminaryMilestone]

🌐
[Azerbaijani][AZ],
[bahasa Indonesia][ID],
[Català][CA],
[Češ<PERSON>][CZ],
[Dansk][DA],
[Deutsch][DE],
[English][EN],
[Español][ES],
[Français][FR],
[Galego][GL],
[Italiano][IT],
[Kurdî][KU],
[Lietuvių][LT],
[Mongolian][MN],
[Nederlands][NL],
[Norsk][NO],
[Polski][PL],
[Português][PT_BR],
[Română][RO],
[Svenska][SV],
[tiếng Việt][VI],
[Türkçe][TR],
[Ελληνικά][GR],
[Беларуская мова][BE],
[Русский][RU],
[Українська][UK],
[العربية][AR],
[فارسی][FA],
[नेपाली भाषा][NE]
[हिंदी][HI_IN],
[অসমীয়া][AS_IN],
[বাংলা][BD_BN],
[မြန်မာ][MM],
[한국어][KO_KR],
[日本語][JA],
[正體中文][ZH_TW],
[简体中文][ZH_CN]

[AR]:README.ar.md
[AS_IN]:README.as_in.md
[AZ]:README.az.md
[BD_BN]:README.bd_bn.md
[BE]:README.be.md
[CA]:README.ca.md
[CZ]:README.cz.md
[DA]:README.da.md
[DE]:README.de.md
[EN]:../README.md
[ES]:README.es.md
[FA]:README.fa.md
[FR]:README.fr.md
[GL]:README.gl.md
[GR]:README.gr.md
[HI_IN]:README.hi_in.md
[ID]:README.id.md
[IT]:README.it.md
[JA]:README.ja.md
[KO_KR]:README.ko_kr.md
[KU]:README.ku.md
[LT]:README.lt.md
[MM]:README.mm.md
[MN]:README.mn.md
[NE]:README.ne.md
[NL]:README.nl.md
[NO]:README.no.md
[PL]:README.pl.md
[PT_BR]:README.pt_br.md
[RO]:README.ro.md
[RU]:README.ru.md
[SV]:README.sv.md
[TR]:README.tr.md
[UK]:README.uk.md
[VI]:README.vi.md
[ZH_CN]:README.zh_cn.md
[ZH_TW]:README.zh_tw.md

阿波罗 11 号制导计算机（AGC）中指令模块（Comanche055）和登月模块（Luminary099）原始代码。由 [虚拟 AGC][3] 和 [MIT 科学博物馆][4] 的伙计们完成电子化。本仓库存在的目的是存储阿波罗 11 号原始代码。当然，倘若在本仓库或原始代码扫描件 [Luminary 099][5] 及 [Comanche 055][6] 发现问题，或者任何我可能漏掉的文件，欢迎提交 PR。

## 贡献

在提交 pull request 之前，请先阅读 [CONTRIBUTING.zh_cn.md][7]。

## 编译

如果您对编译原始代码有兴趣的话，请查阅 [虚拟 AGC][8] 的文档。

## 归属

&nbsp;      | &nbsp;
:---------- | :-----
版权         | 公共领域。
Comanche055 | 阿波罗 11 号制导计算机（AGC）中的指令模块（CM）, Colossus 2A 的部分源代码<br>`Assemble revision 055 of AGC program Comanche by NASA`<br>`2021113-051. 10:28 APR. 1, 1969`
Luminary099 | 阿波罗 11 号制导计算机（AGC）中的登月模块（LM）, Luminary 1A 的部分源代码<br>`Assemble revision 001 of AGC program LMY99 by NASA`<br>`2021112-061. 16:27 JUL. 14, 1969`
汇编程序     | yaYUL
联系人       | Ron Burkey <<EMAIL>>
网站         | www.ibiblio.org/apollo
数字化       | 该源代码从 MIT 科学博物馆的印刷本电子化图片抄录、改编而来。电子化工作由 Paul Fjeld 完成，并由该馆的 Deborah Douglas 进一步整理。由衷地感激两位。

### 协议与许可

*派生于 [CONTRACT_AND_APPROVALS.agc]*

本 AGC 程序亦可称为：Colossus 2A

根据第 R-577 报告，本程序被用于指令模块。本程序是在 DSR 项目 55-23870 下编写的。该项目由（美国）国家航空航天局载人航天中心与位于马萨诸塞州剑桥的麻省理工学院 The Instrumentation Laboratory 实验室签属的第 NAS 9-4065 号合同进行资助。

提交者                | Role | 日期
:------------------- | :--- | :--
Margaret H. Hamilton | Colossus 程序设计负责人<br>阿波罗制导 |  1969 年 3 月 28 日

核准人             | Role | 日期
:---------------- | :--- | :--
Daniel J. Lickly  | 负责人、任务程序开发<br>阿波罗制导程序 | 1969 年 3 月 28 日
Fred H. Martin    | Colossus 项目经理<br>阿波罗制导程序 |  1969 年 3 月 28 日
Norman E. Sears   | 负责人、任务开发<br>阿波罗制导程序 | 1969 年 3 月 28 日
Richard H. Battin | 负责人、任务开发<br>阿波罗制导程序 | 1969 年 3 月 28 日
David G. Hoag     | 负责人<br>阿波罗制导程序 | 1969 年 3 月 28 日
Ralph R. Ragan    | 副负责人<br>Instrumentation Laboratory | 1969 年 3 月 28 日

[CONTRACT_AND_APPROVALS.agc]:https://github.com/chrislgarry/Apollo-11/blob/master/Comanche055/CONTRACT_AND_APPROVALS.agc
[1]:https://flat.badgen.net/badge/NASA/Mission%20Overview/0B3D91
[2]:https://www.nasa.gov/mission_pages/apollo/missions/apollo11.html
[3]:http://www.ibiblio.org/apollo/
[4]:http://web.mit.edu/museum/
[5]:http://www.ibiblio.org/apollo/ScansForConversion/Luminary099/
[6]:http://www.ibiblio.org/apollo/ScansForConversion/Comanche055/
[7]:https://github.com/chrislgarry/Apollo-11/blob/master/Translations/CONTRIBUTING.zh_cn.md
[8]:https://github.com/rburkey2005/virtualagc
[SWH]:https://flat.badgen.net/badge/Software%20Heritage/Archive/0B3D91
[SWH_URL]:https://archive.softwareheritage.org/browse/origin/https://github.com/chrislgarry/Apollo-11/
[Comanche]:https://flat.badgen.net/github/milestones/chrislgarry/Apollo-11/1
[ComancheMilestone]:https://github.com/chrislgarry/Apollo-11/milestone/1
[Luminary]:https://flat.badgen.net/github/milestones/chrislgarry/Apollo-11/2
[LuminaryMilestone]:https://github.com/chrislgarry/Apollo-11/milestone/2

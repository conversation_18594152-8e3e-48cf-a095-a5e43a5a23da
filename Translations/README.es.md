# Apolo 11

[![NASA][1]][2]
[![SWH]][SWH_URL]
[![Comanche]][ComancheMilestone]
[![Luminary]][LuminaryMilestone]

🌐
[Azerbaijani][AZ],
[bahasa Indonesia][ID],
[Català][CA],
[Češ<PERSON>][CZ],
[Dansk][DA],
[Deutsch][DE],
[English][EN],
[Español][ES],
[Français][FR],
[Galego][GL],
[Italiano][IT],
[Kurdî][KU],
[Lietuvių][LT],
[Mongolian][MN],
[Nederlands][NL],
[Norsk][NO],
[Polski][PL],
[Português][PT_BR],
[Română][RO],
[Svenska][SV],
[tiếng Việt][VI],
[Türkçe][TR],
[Ελληνικά][GR],
[Беларуская мова][BE],
[Русский][RU],
[Українська][UK],
[العربية][AR],
[فارسی][FA],
[नेपाली भाषा][NE]
[हिंदी][HI_IN],
[অসমীয়া][AS_IN],
[বাংলা][BD_BN],
[မြန်မာ][MM],
[한국어][KO_KR],
[日本語][JA],
[正體中文][ZH_TW],
[简体中文][ZH_CN]

[AR]:README.ar.md
[AS_IN]:README.as_in.md
[AZ]:README.az.md
[BD_BN]:README.bd_bn.md
[BE]:README.be.md
[CA]:README.ca.md
[CZ]:README.cz.md
[DA]:README.da.md
[DE]:README.de.md
[EN]:../README.md
[ES]:README.es.md
[FA]:README.fa.md
[FR]:README.fr.md
[GL]:README.gl.md
[GR]:README.gr.md
[HI_IN]:README.hi_in.md
[ID]:README.id.md
[IT]:README.it.md
[JA]:README.ja.md
[KO_KR]:README.ko_kr.md
[KU]:README.ku.md
[LT]:README.lt.md
[MM]:README.mm.md
[MN]:README.mn.md
[NE]:README.ne.md
[NL]:README.nl.md
[NO]:README.no.md
[PL]:README.pl.md
[PT_BR]:README.pt_br.md
[RO]:README.ro.md
[RU]:README.ru.md
[SV]:README.sv.md
[TR]:README.tr.md
[UK]:README.uk.md
[VI]:README.vi.md
[ZH_CN]:README.zh_cn.md
[ZH_TW]:README.zh_tw.md

Código fuente original del ordenador guía del Apolo 11 (AGC) para el módulo de comando (Comanche055) y del módulo lunar (Luminary099). Digitalizado por los chicos de [Virtual AGC][3] y [el museo del MIT][4]. El objetivo es ser un repositorio del código fuente original del Apolo 11. De este modo, serán bienvenidos los PRs relacionados con las transcripciones en este repositorio y los escaneos del código fuente original para el [Luminary 099][5] y el [Comanche 055][6], así como cualquier archivo que se me haya olvidado.

## ¿Cómo contribuir?

Por favor, lea [CONTRIBUTING.es.md][7] antes de abrir un pull request.

## Compilar

Si está interesado en compilar el código fuente original, eche un vistazo a [Virtual AGC][8].

## Atribuciones

&nbsp;            | &nbsp;
:---------------- | :-----
Derechos de autor | Dominio público
Comanche055       | Parte del código fuente del Colossus 2A, el ordenador guía Apolo (AGC) del Módulo de Comando (CM) para el Apolo 11<br>`Assemble revision 055 of AGC program Comanche by NASA`<br>`2021113-051. 10:28 APR. 1, 1969`
Luminary099       | Parte del código fuente del Luminary 1A, el ordenador guía Apolo (AGC) del Módulo Lunar (LM) para el Apolo 11<br>`Assemble revision 001 of AGC program LMY99 by NASA`<br>`2021112-061. 16:27 JUL. 14, 1969`
Ensamblador       | yaYUL
Contacto          | Ron Burkey <<EMAIL>>
Sitio Web         | www.ibiblio.org/apollo
Digitalización    | Este código fuente ha sido transcrito o adaptado desde imágenes digitalizadas de una copia en papel presente en el museo del MIT. Los empleados del museo Paul Fjeld y Deborah Douglas realizaron la digitalización y la gestión de las imágenes, respectivamente. Muchas gracias a ambos.

### Contratos y aprobaciones

*Derivado de [CONTRACT_AND_APPROVALS.agc]*

Este programa debe también llamarse Colossus 2A.

Este programa fue creado para ser usado en el Modulo de Comando tal y como se especifica en el informe `R-577`. Este programa se preparó en el proyecto `55-23870`, patrocinado por el Centro de Naves Tripuladas del Centro Nacional de Aeronáutica y Administración Espacial a través del contrato `NAS 9-4065` del Laboratorio de Instrumentación del Instituto de Tecnología de Massachussets, Cambridge, Mass.

Enviado por          | Rol | Fecha
:------------------- | :-- | :----
Margaret H. Hamilton | Líder de programación de Colossus <br> Apollo Guidance and Navigation | 28 mar 69

Aprobado por      | Rol | Fecha
:---------------- | :-- | :----
Daniel J. Lickly  | Director, Desarrollo del programa de misión <br> Programa de orientación y navegación Apollo | 28 mar 69
Fred H. Martin    | Colossus Project Manager <br> Programa de orientación y navegación Apollo | 28 mar 69
Norman E. Sears   | Director de Desarrollo de Misión <br> Programa de Orientación y Navegación Apollo | 28 mar 69
Richard H. Battin | Director de Desarrollo de Misión <br> Programa de Orientación y Navegación Apollo | 28 mar 69
David G. Hoag     | Director del Programa de Orientación y Navegación Apollo | 28 mar 69
Ralph R. Ragan    | Subdirector del Laboratorio de Instrumentación | 28 mar 69

[CONTRACT_AND_APPROVALS.agc]:https://github.com/chrislgarry/Apollo-11/blob/master/Comanche055/CONTRACT_AND_APPROVALS.agc
[1]:https://flat.badgen.net/badge/NASA/Mission%20Overview/0B3D91
[2]:https://www.nasa.gov/mission_pages/apollo/missions/apollo11.html
[3]:http://www.ibiblio.org/apollo/
[4]:http://web.mit.edu/museum/
[5]:http://www.ibiblio.org/apollo/ScansForConversion/Luminary099/
[6]:http://www.ibiblio.org/apollo/ScansForConversion/Comanche055/
[7]:https://github.com/chrislgarry/Apollo-11/blob/master/Translations/CONTRIBUTING.es.md
[8]:https://github.com/rburkey2005/virtualagc
[SWH]:https://flat.badgen.net/badge/Software%20Heritage/Archive/0B3D91
[SWH_URL]:https://archive.softwareheritage.org/browse/origin/https://github.com/chrislgarry/Apollo-11/
[Comanche]:https://flat.badgen.net/github/milestones/chrislgarry/Apollo-11/1
[ComancheMilestone]:https://github.com/chrislgarry/Apollo-11/milestone/1
[Luminary]:https://flat.badgen.net/github/milestones/chrislgarry/Apollo-11/2
[LuminaryMilestone]:https://github.com/chrislgarry/Apollo-11/milestone/2

# Copyright:	Public domain.
# Filename:	P20-P25.agc
# Purpose:	Part of the source code for Colossus 2A, AKA Comanche 055.
#		It is part of the source code for the Command Module's (CM)
#		Apollo Guidance Computer (AGC), for Apollo 11.
# Assembler:	yaYUL
# Contact:	<PERSON> <<EMAIL>>.
# Website:	www.ibiblio.org/apollo.
# Pages:	562-534
# Mod history:	2009-05-10 RSB	Adapted from the Colossus249/ file
#				of the same name, using Comanche055 page
#				images.
#		2009-05-20 RSB	Corrections:  P2OS -> P20S, STO -> STQ,
#				GOTOPOOH -> GOTOPOOH, a duplicated EXTEND
#				was fixed, P23.10 -> R23.10,
#				S22B0X44 -> S22BOX44, S22SUBSCL -> 22SUBSCL,
#				S22DPP -> S22DS<PERSON>, changed some instructions in
#				P23.152.
#
# This source code has been transcribed or otherwise adapted from digitized
# images of a hardcopy from the MIT Museum.  The digitization was performed
# by <PERSON>, and arranged for by <PERSON> of the Museum.  Many
# thanks to both.  The images (with suitable reduction in storage size and
# consequent reduction in image quality as well) are available online at
# www.ibiblio.org/apollo.  If for some reason you find that the images are
# illegible, contact <NAME_EMAIL> about getting access to the
# (much) higher-quality images which <PERSON> actually created.
#
# Notations on the hardcopy document read, in part:
#
#	Assemble revision 055 of AGC program Comanche by NASA
#	2021113-051.  10:28 APR. 1, 1969
#
#	This AGC program shall also be referred to as
#			Colossus 2A

# Page 562
# RENDEZVOUS NAVIGATION PROGRAM 20
#
# PROGRAM DESCRIPTION
#
# 	MOD NO -- 1
# 	MOD BY -- N. BRODEUR
#
# FUNCTIONAL DESCRIPTION
#
#	TO CONTROL THE CSM ATTITUDE AND OPTICS TO ACQUIRE THE LEM IN THE S+T
#	FIELD AND TO POINT THE CSM TRANSPONDER AT THE LEM.  TO UPDATE EITHER THE
#	LEM OR CSM STATE VECTOR (AS SPECIFIED BY THE ASTRONAUT BY THE DSKY
#	ENTRY) ON THE BASIS OF OPTICAL TRACKING DATA (REQUESTED BY DSKY)
#
# CALLING SEQUENCE --
#
# 	ASTRONAUT REQUEST THROUGH DSKY V37E20E
#
# SUBROUTINES CALLED
#
#	R02BOTH	(IMU STATUS CHECK)				BANKCALL
#	FLAGUP				2PHCHNG			LOADTIME
#	R61CSM (PREFERRED TRACKING ATTITUDE)			FLAGDOWN
#	R52 (AUTO OPTICS POSITIONING ROUT)			SETINTG
#	R22 (REND TRACK DATA PROC ROUT)				PRIOCHNG
#	ENDOFJOB			INTEGRV			GRP2PC
#	INTPRET				MKRLEES			FINDVAC
#
# NORMAL EXIT MODES --
#
# 	P20 MAY BE TERMINATED IN TWO WAYS -- ASTRONAUT SELECTION OF IDLING
#	PROGRAM (P00) BY KEYING V37E00E OR BY KEYING IN V56E
#
# ALARM OR ABORT EXIT MODES --
#
# 	NONE DIRECTLY FROM P20
#
# OUTPUT
#
# 	TRKMKCNT = NO OF RENDEZVOUS TRACKING MARKS TAKEN (COUNTER)
#	VHFCNT = NO OF VHF RANGING MARKS INCORPORATED (COUNTER)
#
# FLAGS SET + RESET
#
#	RNDVZFLG,VEHUPFLG,UPDATFLG,TRACKFLG,TARG1FLG
#	HOLDFLAG,WBODY,WBODY1,WBODY2,DELCDUX,DELCDUY,DELCDUZ
#	STIKFLAG,PRFTRKAT,VINTFLAG,DIM0FLAG,R60FLAG,R61CNTR

		BANK	33
		SETLOC	P20S
		BANK

		EBANK=	ESTROKER
		COUNT*	$$/P20

PROG20		TC	BANKCALL
		CADR	R02BOTH		# IMU STATUS CHECK
					# BLOCKING OF UPLINK IS DONE BY UPLINK PRG
		CAF	ZERO
		TS	TRKMKCNT	# ZERO REND TRACKING MARK COUNTER
		TS	VHFCNT		# ZERO REND VHF RNG MRK COUNTER
		TC	UPFLAG		# SET PREF TRACK ATT FLAG
		ADRES	PRFTRKAT	# BIT 10 FLAG 5
		TC	DOWNFLAG	# LEM TO BE UPDATED.  VEHUPFLG RESET.
		ADRES	VEHUPFLG	# BIT 8 FLAG 1
# Page 563
		TC	UPFLAG		# SET TRACKFLAG
		ADRES	TRACKFLG	# BIT 5 FLAG 1
		TC	UPFLAG		# SET UPDATFLG
		ADRES	UPDATFLG	# BIT 7 FLAG 1
		TC	UPFLAG		# SET RNDVZFLG
		ADRES	RNDVZFLG	# BIT 7 FLAG 0
		TC	2PHSCHNG
		OCT	4
		OCT	05022
		OCT	26000
		TC	INTPRET
		RTB
			LOADTIME
		STCALL	MARKTIME
			SETINTG		# SET INTEGRATION FLAGS
		BOFF	SET
			RENDWFLG
			P20.1
			DIM0FLAG	# SET TO INTEGRATE THE W MATRIX
P20.1		BON	CLEAR
			VEHUPFLG
			P20.2
			VINTFLAG	# SET FOR LM INTEGRATION
P20.2		CALL
			INTEGRV
		CALL
			GRP2PC		# GROUP 2 PHASE CHANGE
		CALL
			SETINTG		# SET INTEGRATION FLAGS
		BOFF	CLEAR
			VEHUPFLG
			P20.3
			VINTFLAG	# SET FOR LM INTEGRATION
P20.3		CALL
			INTEGRV
		EXIT
		CAF	PRIO26
		TC	FINDVAC
		EBANK=	MRKBUF2
		2CADR	R22

		TC	2PHSCHNG
		OCT	00072
		OCT	00111
PIKUP20		CAF	PRIO14		# ALLOW HIGHER PRIO THAN LAMBERT
		TC	PRIOCHNG
		CAF	BIT5		# IS TRACK FLAG SET
		MASK	STATE	+1
		EXTEND
		BZF	ENDOFJOB	# NO
# Page 564
		CAF	BIT13
		MASK	STATE	+3	# IS REFSMFLG SET
		EXTEND
		BZF	ENDOFJOB
		CAF	ZERO
		TS	R61CNTR		# INITIALIZE R61 COUNTER
		TC	BANKCALL
		CADR	R61CSM
		EBANK=	QMIN
		CAF	EBANK5
		TS	EBANK
		TC	UPFLAG		# SET TARGET FLAG TO LEM
		ADRES	TARG1FLG	# BIT 10 FLAG 1
P20R52JB	TC	INTPRET
		CALL
			R52		# SET UP AUTO OPTICS JOB
		EXIT
		TC	BANKCALL
		CADR	MKRLEES
		CAF	ONE		# HOLD PRESENT ATTITUDE
		TS	HOLDFLAG
		TC	ENDOFJOB
OCT203		OCT	00203
FIRST3		EQUALS	FURST3

# Page 565
# ORBITAL NAVIGATION PROGRAM 22

		BANK	31
		SETLOC	P20S1
		BANK

		EBANK=	LANDMARK
		COUNT*	$$/P22

PROG22		TC	DOWNFLAG	# RESET RNDVZFLG BIT 7 FLAG 0
		ADRES	RNDVZFLG
		TC	BANKCALL
		CADR	R02BOTH		# IMU STATUS CHECK
		TC	INTPRET		# COMPUTE ANGLE BETWEEN Y AND VXR SM
		RTB
			LOADTIME
		STCALL	TDEC1
			CSMCONIC	# INTEGRATE TO PRESENT TIME
		VLOAD	VXV		# CROSS PRODUCT BETWEEN V AND R
			VATT
			RATT
		UNIT	DOT
			REFSMMAT +6
		ABS
		SL1	ARCCOS
		STORE	+MGA
		CLEAR	EXIT
			RENDWFLG
		CAF	V06N45B
		TC	BANKCALL
		CADR	GOFLASHR
		TC	GOTOPOOH	# TERM P22
		TC	PROG22A		# PROC
		TC	-5		# ENTER
		CAF	THREE
		TC	BLANKET		# BLANK OUT R1 + R2
		TC	ENDOFJOB
PROG22A		CS	PRIO7		# RESULT=70777  SET OFFSET NO.=0
		MASK	LANDMARK
		TS	LANDMARK
		TC	INTPRET
		CLEAR
			P22MKFLG
		SET	BOFF
			ERADFLAG
			CMOONFLG
			PROG22B		# EARTH
		SET			# MOON
			LUNAFLAG
		DLOAD	CALL		# MPAC=V05N70,MPAC+1=NONZERO FOR N70
			V05N7022
# Page 566
			S22N7071
		GOTO
			CALLR52
PROG22B		CLEAR	SET		# EARTH ORBIT
			LUNAFLAG
			KNOWNFLG
		CALL			# GET LAT/LONG/ALT FROM ASTRO
			P22SUBRB
CALLR52		EXIT
		TC	2PHSCHNG
		OCT	00004
		OCT	05022
		OCT	13000
		CAF	FIVE
		TS	MARKINDX	# SET MARK INDEX=5 FOR R52
		TC	UPFLAG
		ADRES	TARG2FLG	# SET FOR SIGHTING LMK
		TC	DOWNFLAG
		ADRES	TARG1FLG	# CLEAR FOR NON-LEM
		TC	INTPRET
		CALL
			R52
DOV5N71		SLOAD	CALL		# MPAC=V05N71,MPAC+1=0 FOR N71
			V05N7122
			S22N7071
PROG22C		LXC,2	SLOAD*
			MARKSTAT
			QPRET,2
		STCALL	8NN
			S22.1		# ESTABLISH LANDMARK -- COMPUTE ORBITAL
P22OVER		EXIT
		TC	PHASCHNG
		OCT	04022
		TC	PROG22A		# POINT A ON GSOP
V06N45B		VN	0645
V05N7022	VN	00570
V05N7122	VN	00571
		SETLOC	P20S
		BANK

		SETLOC	P20S1
		BANK

S22LSITE	RTB			# CONVERT RLS FROM MOON-FIXED TO BASIC REF
			LOADTIME
		STOVL	6D		# 6-7D= TIME
			RLS
		STODL	0D		# 0-5D= LANDING SITE VECTOR
			HIDPHALF	# MPAC= ANY NON-ZERO FOR MOON
		CALL
# Page 567
			RP-TO-R		# RLS IN BASIC REF B-27 IN MPAC
		VSR2			# LUNAFLAG AND ERADFLAG SET ABOVE
		STORE	ALPHAV		# SCALE RLS B-29 FOR LAT-LONG
		RTB
			LOADTIME	# SET PRESENT TIME IN MPAC FOR LAT-LONG
		CALL
			LAT-LONG
		GOTO
			S22TOFF		# EXIT
OBTAINLL	CALL			# GET LAT/LONG/ALT FROM ASTRO
			P22SUBRB
		GOTO
			S22TOFF		# EXIT
P22SUBRB	STQ	EXIT		# GET LAT/LONG/ALT FROM ASTRO
			S22TOFF +1
		CAF	V06N89B
		TC	BANKCALL
		CADR	GOFLASH
		TC	GOTOPOOH	# TERMINATE
		TC	+2		# PROCEED
		TC	-5		# ENTER OR RECYCLE
		TC	INTPRET
		DLOAD	ABS		# TEST LAT/LONG GREATER THAN 90
			LANDLAT
		BDSU	BMN		# 1/4 REV - LAT
			DP1/4TH
			N89ERRX
		DLOAD	ABS
			LANDLONG
		BDSU	BPL
			DP1/4TH
			+4

N89ERRX		EXIT
		TC	FALTON
		TC	P22SUBRB +2	# LONG GR. THAN 90  REDISPLAY
		CALL
			LLASRDA
		GOTO
			S22TOFF +1	# EXIT
S22N7071	STORE	8KK		# 8KK=V05N71,V05N70 8KK+1=0 N71, NOT 0 N70
		STQ	EXIT
			S22TOFF
S22DSPPA	CA	8KK		# V05N70 OR V05N71
		TC	BANKCALL
		CADR	GOFLASHR
		TC	GOTOPOOH	# V34E TERMINATE
		TC	+5		# V33E PROCEED
		TC	-5		# V32E RECYCLE
		CAF	FIVE		# IMMEDIATE RETURN BLANK OUT R1,R3
		TC	BLANKET
# Page 568
		TC	ENDOFJOB
		CA	LANDMARK
		MASK	PRIO7		# 07000
		TS	CXOFF		# 08000 = OFFSET INDICATOR
		CS	PRIO5		# 5 8-5
		AD	CXOFF
		EXTEND
		BZMF	+2		# OFF=0 THRU 5 OK
		TC	S22DSPP		# OFF=6.7 ILLEGAL REDISPLAY
		TC	DOWNFLAG
		ADRES	KNOWNFLG	# CLEAR KNOWNFLG
		CA	LANDMARK	# MASK A FROM ABCDE
		MASK	13,14,15
		EXTEND
		BZMF	S22DSPP		# A=0,4,5,6,7  ILLEGAL REDISPLAY
		TS	22SUBSCL	# TEMP
		CS	BIT14
		AD	22SUBSCL
		CCS	A
S22DSPP		TC	FALTON		# + A=3 ILLEGAL REDISPLAY
		TC	S22DSPPA	# COMMON ERROR EXIT BACK TO DISPLAY
		TC	+2		# - A=1 KNOWN LMK  CHECK DE
		TC	S22ABDE		# -0 A=2 UNKNOWN LMK, DE MEANINGLESS
		TC 	UPFLAG
		ADRES	KNOWNFLG	# SET KNOWNFLG
		CS	HIGH9		# RESULT= 00077
		MASK	LANDMARK
		TS	22SUBSCL	# 000DE
		CS	BIT1
		AD	22SUBSCL
		EXTEND
		BZMF	S22ABDE		# DE=0,1 OK FOR BOTH N70,N71
		CA	8KK	+1	# =0 FOR N71, NOT =0 FOR N70
		EXTEND
		BZF	S22DSPP		# N71 REDISPLAY  DE MUST= 0 OR 1
		CA	BIT5		# N70 TEST DE= 50-57 OCTAL FOR ADV. ORBIT
		AD	OCT50		# SUM=00070
		MASK	22SUBSCL	# 00D0
		CS	A
		AD	OCT50
		EXTEND
		BZF	DE-GR-50	# D=5 OR DE=50-57, OK FOR N70
		TC	S22DSPP		# DE NOT 50-57  ILLEGAL, REDISPLAY
S22ABDE		TC	INTPRET
		BOFF	SLOAD
			KNOWNFLG	# UNKNOWN LMK, DE MEANINGLESS
			S22TOFF
			22SUBSCL	# =0 GET LLA FROM ASTRO, NOT=0 USE RLS
		BHIZ	GOTO
			OBTAINLL	# GET LAT/LONG/ALT FROM ASTRO  N89
# Page 569
			S22LSITE	# GET LAT/LONG/ALT FROM RLS
DE-GR-50	TC	2PHSCHNG
		OCT	00004
		OCT	05022
		OCT	13000
		CA	FIVE
		TS	MARKINDX
		TC	UPFLAG
		ADRES	TARG2FLG
		TC	DOWNFLAG
		ADRES	TARG1FLG
		TC	INTPRET
		CALL
			ADVORB
		GOTO
			DOV5N71

# Page 570
# PROGRAM NAME:  OPTICS CALIBRATION ROUTINE
# MOD NO:  1
# MOD BY:  TOM KNATT
#
# FUNCTIONAL DESCRIPTION:  TO MEASURE THE EFFECT OF SOLAR RADIATION ON
#	THE SXT TRUNNON ANGLE AND STORE THE MEASURED TRUNNION BIAS FOR P23
#
# CALLING SEQUENCE:	CALL
#				R57
#
# SUBROUTINES CALLED:  DISPLAY ROUTINES
#
# NORMAL EXIT MODES:  VIA EGRESS
#
# ALARMS:  NONE
#
# ABORT MODES:  P23 ABORT IF MARKING SYSTEM OR EXTENDED VERB ACTIVE
#
# INPUT:  NONE REQUIRED, NORMALLY CALLED BY P23
#
# OUTPUT:  TRUNNION BIAS ANGLE: ANGLE DETERMINED WHEN SHAFT LINE OF SIGHT
#	(SLOS) AND LANDMARK LINE OF SIGHT (LLOS) ARE SUPERIMPOSED.  THIS ANGLE
#	MAY NOT BE EXACTLY ZERO BECAUSE OF UNEVEN HEATING OF THE OPTICS, FOR
#	EXAMPLE.
#
# ERASABLE INITIALIZATION REQUIRED:  MRKBUF1, EXTVBACT
#
# DEBRIS:  RUPTREGS USED BY MARKRUPT AND ERASABLES USED BY DISPLAYS.

		BANK	33
		SETLOC	P20S
		BANK
		COUNT*	$$/R57
		EBANK=	MRKBUF1
R57		STQ	EXIT
			EGRESS
		CAF	EBANK7
		TS	EBANK
		CAF	SIX		# BIT2 = MARKING SYSTEM IN USE
		MASK	EXTVBACT	# BIT3 = EXTENDED VERB IN PROGRESS
		CCS	A
		TC	P23ABRT		# SET, THEREFORE ABORT
		CAF	BIT2		# NOT SET
		ADS	EXTVBACT	# SET IT
R57A		TC	UPFLAG		# SET V59FLAG (BIT 12 FLAG 5 TO INDICATE
		ADRES	V59FLAG		# CALIBRATION MARK
		CAF	V59NB
		TC	BANKCALL
		CADR	GOMARKFR
		TC	GOTOPOOH	# TERMINATE
		TC	ENDR57
		TC	ENDR57
		CAF	SEVEN
# Page 571
		TC	BLANKET		# BLANK OUT R1,R2,R3
		TC	ENDOFJOB
# STORE TRUNNION ANGLE (OCDU)
MARKDISP	CAF	V06N87NB
		TC	BANKCALL
		CADR	GOMARKFR
		TC	GOTOPOOH	# TERMINATE
		TC	R57B		# PROCEED
		TC	R57A		# ENTER (RECYCLE)
		CAF	FIVE
		TC	BLANKET		# BLANK OUT R1,R3
		TC	ENDOFJOB
R57B		CA	19.77DEG	# PUT FIXED INTO ERASABLE FOR MSU
		TS	L		# INSTRUCTION COMING UP
		CA	MRKBUF1 +5	# CONTAINS TRUNNION
		EXTEND
		MSU	L		# CONNECTS TRUNBIAS FROM 2'S TO 1'S
		TS	TRUNBIAS
ENDR57		TC	DOWNFLAG	# RESET V59FLAG
		ADRES	V59FLAG		# BIT 12 FLAG 5
		CAF	EBANK5
		TS	EBANK
		CAF	PRIO14
		TC	NOVAC		# THIS JOB CLEARS BIT IN
		EBANK=	MARKSTAT
		2CADR	ENDMARK		# MARKING IN R57 SO R53 CAN TAKE OVER

		TC	INTPRET
		GOTO
			EGRESS
P23ABRT		TC	BAILOUT
		OCT	01211
V06N87NB	VN	0687
V59NB		VN	5900
V51NB		VN	5100
19.77DEG	OCT	61740

# Page 572
# PROGRAM DESCRIPTION
# MOD NO:  1
# MOD BY:  N. BRODEUR
#
# FUNCTIONAL DESCRIPTION
#
# TO PERFORM SIGHTING MARKS IN CONJUNCTION WITH THE RENDEZVOUS NAVIG-
# ATION PROGRAM.  CALLED BY ASTRONAUT VIA EXTENDED VERB.
#
# CALLING SEQUENCE:
#
#	R21 VIA V57
#	R23 VIA V 54
#
# SUBROUTINES CALLED:
#
#	FLAGUP		FLAGDOWN	BANKCALL
#	ENDOFJOB	GOMARK2		GOMARKF
#	INTPRET		GENTRAN		KLEENEX
#	ENDMARK
#
# NORMAL EXIT MODES:
#
# MARKRUPT USED BY SXTMARK HAS BEEN MODIFIED TO STORE MARK IN MRKBUF2
# FOR USE BY R22.  WHEN ASTRONAUT IS FINISHED TAKING MARKS, HE HITS AN
# PROCEED, R21 IS TERMINATED THUS CAUSING THE FINAL MARK TO BE TRANSFRD
# TO MRKBUF2 FOR PROCESSING BY R22
#
# ALARM OR ABORT EXIT MODES:
#
#	NONE
#
# OUTPUT:
#
#	7 REGISTER MRKBUF2 CONTAINING TIME2,TIME1,CDUY,OPTICS X,CDUZ, OPTICS Y,
#	CDUX.
#
# ERASABLE INITIALIZATION REQUIRED
#
# FLAGS SET AND RESET
#	R21MARK		(COMMUNICATION TO MARKRUPT TO STORE MARKS IN MRKBUF1 +2)
#	R23FLG		INDICATES COAS MARKING
#
# DEBRIS

		EBANK=	MRKBUF1
		SETLOC	RENDEZ
		BANK

		COUNT*	$$/R21

R21CSM		TC	UPFLAG		# SET R21MARK
		ADRES	R21MARK		# BIT 14 FLAG 2
R23CSM		CA	NEGONE
		TS	MRKBUF1
		TS	MRKBUF2
		CA	FLAGWRD1
		MASK	R23BIT		# TEST R23FLG
		EXTEND
		BZF	R21C1		# NOT SET REGULAR R21 MARKING
		CAF	V0694		# R23 BACKUP MARKING
		TC	BANKCALL	# DISPLAY SHAFT + TRUNNION
		CADR	GOMARKF
		TC	R21END		# TERM
		TC	+2		# PROC
# Page 573
		TC	-5		# ENTER
R23CSM1		CAF	V53		# PERFORM ALT LOST SIGHT MARK
		TC	BANKCALL
		CADR	GOMARK2
		TC	R21END		# V34: TERMINATE R23
		TCF	R21CSMA		# PROCEED: END BACK UP MARKING (R23)
		CAF	SIX		# TRANSFER MRKBUF1 TO MRKBUF2
		TC	GENTRAN
		ADRES	MRKBUF1
		ADRES	MRKBUF2
		EXTEND
		DCA	TIME2
		DXCH	MRKBUF1		# READ TIME
		CA	CDUY		# READ CDU ANGLES
		TS	MRKBUF1 +2
		CA	CDUZ
		TS	MRKBUF1 +4
		CA	CDUX
		TS	MRKBUF1 +6
		RELINT
		TC	R23CSM1
R21C1		CAF	V51NB
		TC	BANKCALL
		CADR	GOMARK2
		TC	R21END		# V34: TERMINATE R21
		TCF	R21CSMA		# PROCEED: END R21
		TCF	R21C1		# RECYCLE

R21CSMA		CA	MRKBUF1		# IF -1 NO MARK
		AD	ONE
		EXTEND
		BZF	R21END		# ZERO = NO MARK
		CAF	SIX		# MARK THEREFORE TRANSFER IT TO MRKBUF2
R21CSM1		TC	GENTRAN		# TRANSFER MRKBUF1 TO MRKBUF2
		ADRES	MRKBUF1
		ADRES	MRKBUF2
		RELINT
R21END		TC	BANKCALL
		CADR	KLEENEX
		TC	DOWNFLAG	# RESET R21MARK
		ADRES	R21MARK		# BIT 14 FLAG 2
		TC	ENDMARK		# END MARKING AND ENDJOB
V53		VN	5300
V0694		VN	0694

# Page 574
# PREFERRED TRACKING ATTITUDE ROUTINE R61CSM
#
# PROGRAM DESCRIPTION
#	MOD NO:  2
#	MOD BY:  N. BRODEUR
#
# FUNCTIONAL DESCRIPTION:
#
#	TO COMPUTE THE PREFERRED TRACKING ATTITUDE OF THE CSM TO ENABLE OPTICS
#	TRACKING OF THE LM AND TO PERFORM THE MANEUVER TO THE PREFERRED
#	OR X-AXIS TRACKING ATTITUDE.
#
# CALLING SEQUENCE:
#
#	TC	BANKCALL
#	CADR	R61CSM
#
# SUBROUTINE CALLED
#
#	MAKECADR	BANKCALL
#	INTPRET		FLAGUP		FLAGDOWN
#	BANKJUMP	CRS61.1		R60CSM
#	PHASCHNG
#
# NORMAL EXIT MODES:
#
#	NORMAL RETURN TO CALLER + 1
#
# OUTPUT:
#
#	SEE OUTPUT FOR CRS61.1 & ATTITUDE MANEUVER ROUTINE (R60CSM)
#
# ERASABLE INITIALIZATION REQUIRED
#
#	GENRET USED TO SAVE Q FOR RETURN
#	R61CNTR MUST BE PRESET TO ZERO
#
# FLAGS SET + RESET
#
#	3-AXIS FLAG
#
# DEBRIS
#
#	SEE SUBROUTINES

		EBANK=	GENRET
		COUNT*	$$/R61		# ROUTINES -- NAVIGATION -- PREF. TR. 9TT=

R61CSM		CAF	EBANK6		# SWITCH TO EBANK 6
		XCH	EBANK
		TS	SAVBNK		# SAVE EBANK
		TC	MAKECADR
		TS	GENRET
		CCS	R61CNTR		# TEST R61DNTR
		TC	DECRM61		# NOT READY TO DO R61
		TC	+2		# DO R61
		TC	DECRM61 +1
		TC	INTPRET
		CALL
			CRS61.1		# LOS DETERMINATION + VEH ATTITUDE
		EXIT
		INDEX	MPAC
		TC	+1
		TC	R61END		# SUBROUTINE DRIVING DAP	(EXIT R61)
					# OR AUTO MODE NOT SET		(EXIT R61)
					# OR SKIKFLAG SET		(EXIT R61)
R61C1		TC	DOWNFLAG	# RESET 3-AXIS FLAG
		ADRES	3AXISFLG	# BIT 6 FLAG 5
# Page 575
		CS	ONE		# SET R61CNTR NEG. TO INDICATE KALCMANU
		TS	R61CNTR

		TC	UPFLAG		# SET FLAG FOR PRIORITY DISPLAYS FOR R60
		ADRES	PDSPFLAG	# BIT 12 FLAG 4
		TC	BANKCALL
		CADR	R60CSM
		TC	DOWNFLAG	# RESET FLAG FOR PRIORITY DISPLAYS IN R60
		ADRES	PDSPFLAG	# BIT 12 FLAG 4
		TC	PHASCHNG
		OCT	00111
		CAF	ZERO
		TC	DECRM61
R61END		CAF	THREE

DECRM61		TS	R61CNTR

		CAE	GENRET
		LXCH	A		# RETURN IS IN L
		CA	SAVBNK		# RESTORE EBANK
		XCH	EBANK
		LXCH	A		# RETURN IS NOW BACK IN A
		TC	BANKJUMP	# EXIT R61
		BANK	13
		SETLOC	P20S2
		BANK

		EBANK=	MRKBUF1
# Page 576
# BVECTOR PERFORMS COMPUTATIONS FOR
#
# 	DELTAQ, THE MEASURED DEVIATION BASED ON THE DIFFERENCE BETWEEN THE CSM-LEM
# 	STATE VECTOR ESTIMATES AND THE ACTUAL TRACKING MEASUREMENT.
#
# 	US, THE MODIFIED FICTITIOUS STAR DIRECTION VECTOR
#	GEOMETRY VECTORY B ASSOCIATED WITH EACH TRACKING MEASUREMENT.
#
# INPUT
#
#	UM,1/2 UNIT VECTOR ALONG THE CSM-LM LINE-OF-SIGHT (BASIC REF. SYSTEM)
#
#	USTAR,FICTITIOUS STAR DIRECTION (1/2 UNIT VECTOR)
#
#	RCLP,RELATIVE CSM TO LM POSITION VECTOR
#
# OUTPUT
#
#	USTAR,MODIFIED FICTITIOUS STAR DIRECTION (1/2 UNIT VECTOR)
#
#	BVECTOR = 9 DIMENSIONAL BVECTOR (1/2 UNIT VEC.)
#
#	DELTAQ = MEASURED DEVIATION
#
# CALLING SEQUENCE
#
#	L	CALL BVECTORS
#
# NORMAL EXIT
#
#	L+1 OF CALLING SEQUENCE

		COUNT	23/20SUB

BVECTORS	STQ
			EGRESS
		VLOAD	UNIT
			RCLP		# RELATIVE POSITION VECTOR
		STODL	26D		# RCLP UNIT VEC
			36D		# RCLP ABS VALUE
		STOVL	TEMPOR1		# MOVE TO SAFE LOCATION
			USTAR
		VXV	UNIT
			26D		# USTAR = UNIT(US X UCL)
		STCALL	BVECTOR
			GRP2PC		# PHASE CHANGE
		VLOAD
			BVECTOR
		STORE	USTAR
		DOT	SL1
			UM		# USTAR DOT UM
		ACOS	DSU
			DP1/4TH
		NORM	DMP
			X1
			PI/4.0
		DMP	SRR*
			TEMPOR1		# RCLP ABS VALUE
			0 -3,1		# ADJUST SCALING
		STOVL	DELTAQ
			ZEROVECS
		STORE	BVECTOR +6
		STORE	BVECTOR +12D
		GOTO
# Page 577
			EGRESS
PI/4.0		2DEC	.785398164

# Page 578
# GETUM:  DETERMINES THE LINE OF SIGHT UNIT VECTOR UM IN THE BASIC REFERENCE
# COORDINATE SYSTEM FROM THE OPTICS SHAFT AND TRUNNION ANGLES AND THE IMU
# GIMBAL ANGLES.
#
# INPUT
#
#	MARKDATA, BASE ADDRESS OF MARK DATA
#	REFSMMAT, ROTATION MATRIX FROM STABLE MEMBER TO BASIC REF. COORD. SYSTEM
#
# SUBROUTINES CALLED-
#
#	SXTNB -- SEXT. ANGULAR READINGS TO NAV. BASE COOR.
#	NBSM -- TRANSFORM FROM NAV. BASE TO STABLE MEMBER
#
# OUTPUT
#
#	MPAC = LINE OF SIGHT 1/2 UNIT VECTOR IN BASIC REFERENCE SYSTEM
#
# CALLING SEQUENCE
#
#	L	CALL GETUM
#
# NORMAL EXIT
#
#	L+1 OF CALLING SEQUENCE

GETUM		STQ	SETPD
			EGRESS
			0
		LXC,1	VLOAD*
			MARKDATA	# CONTAINS ADDRESS OF MARK DATA
			1,1
		STODL*	MARKDOWN +1	# TRANSFER DATA FROM WORKING STORAGE
			0,1		# TO MARKDOWN ARRAY FOR DOWNLINK
		STORE	MARKDOWN
		AXT,2
			2
		XSU,2	SXA,2
			X1		# X1 = MARKDATA
			S1		# S1 = MARKDATA(ADR) +2
		CALL
			SXTNB		# SEXT. ANGULAR READINGS TO NAV. BASE COOR.
		CALL
			NBSM		# TRANSFORM FROM NAV. BASE TO STABLE MEM.
		VXM	VSL1
			REFSMMAT
		GOTO			# MPAC = (UM) LINE OF SIGHT VECTOR
			EGRESS		# EXIT

# Page 579
# RENDEZVOUS TRACKING DATA PROCESSING ROUTINE (R22)
#
# PURPOSE	(1) TO PROCESS RENDEZVOUS SIGHTING MARK DATA TO UPDATE THE STATE VECTOR OF EITHER THE CSM OR LM AS
#		DEFINED BY THE RENDEZVOUS NAVIGATION PROGRAM (P20).
#
# ASSUMPTIONS	(1) THIS ROUTINE IS MANUALLY SELECTED BY THE ASTRONAUT BY V55E WHENEVER RENDEZVOUS SIGHTING MARKS
#		ARE DESIRED.  ITS SELECTION, HOWEVER, IS LIMITED TO PERIODS WHEN THE CMC IS HOLDING FOR A V/N FLASHING
#		DATA DISPLAY.  THIS ROUTINE RETURNS TO THE ORIGINAL PROGRAM AT THE INTERRUPTED DISPLAY.

		BANK	34
		SETLOC	P20S3
		BANK

		COUNT	34/R22

R22		CAF	PRIO26
		TS	PHSPRDT2
		TC	PRIOCHNG
		CA	NEG3
		TS	MRKBUF2
		TC	INTPRET
		RTB
			LOADTIME
		STORE	VHFTIME		# PRESENT TIME
REND1		CALL
			GRP2PC
		CALL
			WAITONE
REND1A		EXIT
		CA	MRKBUF2
		EXTEND
		BZF	REND2
		EXTEND
		BZMF	REND3A
REND2		CAF	SIX
		TC	GENTRAN
		ADRES	MRKBUF2
		ADRES	MARKTIME	# MARKTIME MUST BE CONTIGUOUS WITH VTEMP
		CAF	NEG3		# NEG VALUE TO INDICATE VALUES USED
		TS	MRKBUF2
		RELINT
		TC	INTPRET
		CLEAR	CALL
			SOURCFLG	# 0 = OPTICS DATA
			GRP2PC
		SSP	GOTO
			MARKDATA
		ECADR	VTEMP -2
			REND4
REND3A		TC	INTPRET
REND3		CALL
# Page 580
			GRP2PC
		CALL
			WAITONE
		BOFF
			VHFRFLAG
			REND1A
		RTB
			LOADTIME	# PRESENT TIME
		DSU	DSU
			60SECDP		# 1 MIN
			VHFTIME		# LAST READING OF RADAR
		BMN	CALL
			REND1A
			RANGERD		# READ RADAR RANGE
		DLOAD
			MARKTIME
		STORE	VHFTIME		# FOR DOWNLINK
REND4		CALL
			SETINTG		# SET INTEGRV FLAGS
		BON	CALL
			VEHUPFLG
			CSMUPP		# BRANCH IF CSM UPDATE
			INTEGRV
		CALL
			GRP2PC		# PHASE CHANGE
		CALL
			SETINTG		# SET INTEGRV FLAGS
		CLEAR
			VINTFLAG	# SET INTEGRATION VEHICLE TO LM
REND5		BOFF	SET
			RENDWFLG
			REND5A		# DO NOT INTEGRATE W IF FLAG = 0
			DIM0FLAG
REND5A		CALL
			INTEGRV
		CALL
			SHIFTNDX	# SET EARTH MOON SCALING INDEX
		CALL
			CMPOS		# SET CSM POSITION
		SET	CALL
			INCORFLG	# SET FOR 1ST PASS
			LMPOS		# SET LM POSITION
		CLEAR	BON
			ORBWFLAG	# CLEAR FOR ORBITAL AND CISLUNAR
			RENDWFLG
			REND6
		DLOAD
			WRENDPOS
		STCALL	0		# 0 = WRENDPOS	1 = WRENDVEL
			INITIALW	# INTIIALIZE W MATRIX
# Page 581
		DLOAD
			ZEROVECS
		STORE	VHFCNT		# ZERO OUT VHFCNT AND TRKMKCNT
REND6		SET
			RENDWFLG
		VLOAD	VSU
			LEMPOS
			CSMPOS
		STORE	RCLP		# LM - CSM
REND7		UNIT	BON
			SOURCFLG
			REND14		# BRANCH IF DATA IS RADAR
		STORE	UCL
		BOFF	CALL
			INCORFLG
			REND9
			GETUM		# CALCULATE UM LINE OF SIGHT
		STOVL	UM
			UCL
		VXV	BOV
			UM		# UCL X UM
			REND8
REND8		UNIT	BOV
			REND3		# BRANCH IF OVERFLOW IGNORE MARK
		STORE	USTAR
REND9		CALL
			BVECTORS
		BON	VLOAD
			VEHUPFLG
			REND9A
			BVECTOR
		VCOMP
		STORE	BVECTOR
REND9A		CALL
			GRP2PC
		BON
			R23FLG
			REND15		# BRANCH IF BACKUP OPTICS (R23 WORKING)
		DLOAD	DAD
			SXTVAR
			IMUVAR
REND10		STOVL	VARIANCE	# TEMP STORAGE FOR VARIANCE CALC.
			RCLP
		ABVAL	NORM
			X1
		DSQ	DMP
			VARIANCE
		XAD,1	CALL
			X1
			SHIFTNDX	# GET EARTH MOON SCALING INDEX
# Page 582
		XAD,1	XAD,1
			X2
			X2
		SR*	TLOAD
			0 -2,1		# ADJUST SCALING TO B-40
			MPAC
		STORE	VARIANCE
		SLOAD	SR
			INTVAR		# INTEGRATION VARIANCE SCALED B-15
			25D		# SCALE IT B-40
		TAD	RTB
			VARIANCE
			TPMODE
		STORE	VARIANCE
		BOFF	TAD
			SOURCFLG	# BRANCH IF NOT VHF RADAR
			REND10A
			RVARMIN		# VHF RADAR MIN. VARIANCE
		BPL	TLOAD
			REND10A
			RVARMIN
		ABS			# MIN. VALUE WAS STORED AS NEG.
		STORE	VARIANCE	# STORE MIN. VALUE
REND10A		CLEAR	CALL
			DMENFLG		# CLEAR FOR 6 X 6 W MATRIX
			INCORP1		# CALCULATE UPDATE
		CALL
			GRP2PC
		BOFF	CALL
			INCORFLG
			REND12
			SHIFTNDX	# GET EARTH MOON SCALING INDEX
		VLOAD	ABVAL
			DELTAX +6
		SR*
			0,2
		STOVL	N49DISP +2
			DELTAX
		ABVAL	SR*
			0,2
		STORE	N49DISP
		SLOAD
			RMAX
		SR	DSU
			10D
			N49DISP
		BMN	SLOAD
			RENDISP		# BRANCH IF POS UP. GREATER THAN MAX.
			VMAX
		DSU	BMN
# Page 583
			N49DISP +2
			RENDISP		# BRANCH IF VEL. UPDATE GREATER THAN MAX.
REND12		CALL
			INCORP2		# INCORPORATE UPDATE VALUES INTO STATE VEC
		BON	BOFF
			SOURCFLG
			REND16		# BRANCH IF DATA IS RADAR
			INCORFLG
			REND17
		CALL
			SHIFTNDX	# GET EARTH MOON SCALING INDEX
		BON	CALL
			VEHUPFLG
			REND18		# BRANCH IF CSM UPDATE
			LMPOS		# GET LM POSITION
REND13		CALL
			GRP2PC		# PHASE CHANGE
		VLOAD	VSU
			LEMPOS
			CSMPOS
		STORE	RCLP		# LM - CSM
		CLRGO
			INCORFLG
			REND7		# BRANCH FOR 2ND PASS THIS OPTICS MARK
CSMUPP		CLEAR	CALL
			VINTFLAG	# SET INTEGRATION VEHICLE EQ LM
			INTEGRV
		CALL
			GRP2PC		# PHASE CHANGE
		CALL
			SETINTG		# SET FLAGS FOR INTEGRATION
		GOTO
			REND5
REND14		STOVL	BVECTOR		# VHF RADAR BVECTOR
			ZEROVECS
		STORE	BVECTOR +6
		STOVL	BVECTOR +12D
			RCLP
		UNIT	DLOAD
			VHFRANGE	# VHFRANGE SCALED B-27
		BON	SR2
			MOONTHIS
			+1
		DSU	SET
			36D		# ABVAL (RCLP)
			INCORFLG
		STORE	DELTAQ
		BOFF	VLOAD
			VEHUPFLG
			REND14A
# Page 584
			BVECTOR
		VCOMP
		STORE	BVECTOR
REND14A		CALL
			GRP2PC
		DLOAD	GOTO
			RVAR
			REND10
REND15		SLOAD	DAD		# GET ALT LOS VARIANCE
			ALTVAR		# BACKUP OPTICS
			IMUVAR		# IMU VARIANCE
		GOTO
			REND10
REND16		LXA,1	INCR,1
			VHFCNT		# VHF RADAR UPDATE COUNT
		DEC	1
		SXA,1	GOTO
			VHFCNT		# UPDATE COUNT
			REND1
REND17		LXA,1	INCR,1
			TRKMKCNT	# OPTICS MARK COUNT
		DEC	1
		SXA,1	GOTO
			TRKMKCNT	# UPDATE COUNT
			REND3
REND18		CALL
			CMPOS		# GET CSM POSITION
		GOTO
			REND13
CMPOS		VLOAD	VSR*
			DELTACSM
			7,2
		VAD
			RCVCSM
		STORE	CSMPOS		# CSM POSITION SCALED B-27 OR B-29
		RVQ
LMPOS		VLOAD	VSR*
			DELTALEM
			7,2
		VAD
			RCVLEM
		STORE	LEMPOS		# LM POSITION SCALED B-27 OR B-29
		RVQ
RENDISP		EXIT
		CA	FLAGWRD9
		MASK	SOURCBIT
		EXTEND
		BZF	+3
		CA	BIT2
		TC	+2
# Page 585
		CA	BIT1
		TS	N49DISP +4
		CAF	ZERO		# SET TEMPOR1 > ZERO TO INDICATE
		TS	TEMPOR1		# V06 N49 DISPLAY HASN'T BEEN ANSWERED
		TC	PHASCHNG
		OCT	04022
		CAF	PRIO27		# SET UP DISPLAY JOB WITH HIGHER PRIORITY
		TC	NOVAC
		EBANK=	MRKBUF1		# THAN PRESENT JOB
		2CADR	RENDISP2

RENDISP7	TC	INTPRET
		STORE	MPAC
		SLOAD	BZE
			TEMPOR1
			RENDISP7 +1	# DISPLAY HAS NOT BEEN ANSWERED YET
		BMN	GOTO
			REND12		# NEG INDICATES PROCEED
			RENDISP3	# POS INDICATES RECYCLE
RENDISP2	CAF	V06N49
		TC	BANKCALL
		CADR	PRIODSP
		TC	GOTOV56		# TERM EXIT P20 VIA V56
		CS	ONE		# NEG INDICATES PROCEED RENDISP7 JOB
		TS	TEMPOR1		# POS INDICATES RECYCLE RENDISP7 JOB
		TC	ENDOFJOB	# GO COMPLETE ABOVE JOB
RENDISP3	BON
			SOURCFLG
			REND1		# DATA WAS RADAR GO LOOK FOR OPTICS NEXT
		EXIT
		EBANK=	MRKBUF1
		INHINT
		CAF	BUFBANK
		TS	BBANK
		CA	NEGONE
		TS	MRKBUF1		# ERASE MARK ONE BUFFER
		TS	MRKBUF2		# ERASE MARK TWO BUFFER
		RELINT
RENDISP4	TC	INTPRET
		GOTO
			REND3
SXTVAR		2DEC	0.04 E-6 B+16	# SXT ERROR VARIANCE = .04 (MR)SQ

IMUVAR		2DEC	0.04 E-6 B+16	# IMU ERROR VARIANCE = .04 (MR)SQ

V06N49		VN	0649
		EBANK=	MRKBUF1
BUFBANK		BBCON	RENDISP3
		BANK	31
		SETLOC	R22S1
# Page 586
		BANK
SETINTG		STQ	CALL
			EGRESS
			INTSTALL	# RESERVE INTEGRATION
		DLOAD	SET
			MARKTIME
			STATEFLG
		STORE	TDEC1		# MARKTIME
		CLEAR	CLEAR
			INTYPFLG	# PRECISION INTEGRATION
			DIM0FLAG
		SET	CLRGO
			VINTFLAG	# SET VEHICLE EQ. CSM
			D6OR9FLG	# SET W MATRIX DIM. EQ 6
			EGRESS		# EXIT
CNTCHK		STQ
			POINTEX
CONTCHK		BOFF
			REFSMFLG	# BRANCH TO END OF JOB IF REFSMMAT NO GOOD
			ENDPLAC
		SLOAD	BMN
			R61CNTR
			WAITONE1
		BON	BOFF		# IS TRACK FLAG SET
			UPDATFLG
			POINTEX
			TRACKFLG
			ENDPLAC
		EXIT
REDOR22		TC	PHASCHNG
		OCT	00132
		CAF	PRIO26
		TC	PRIOCHNG
		TC	WAITONE +3
WAITONE		STQ
			POINTEX
WAITONE1	EXIT
		CAF	4SECS		# WAIT 4 SECS.
		TC	BANKCALL
		CADR	DELAYJOB
		TC	INTPRET
		GOTO
			CONTCHK		# CHECK AGAIN NOW
RANGERD		EXIT
		INHINT
		CS	OCT17
		EXTEND
		WAND 	CHAN13		# ZERO OUT BITS 1-4 OF CHANNEL 13
		CAF	OCT11
		EXTEND
# Page 587
		WOR	CHAN13		# GENERAGE SHIFT PULSES TO RADR, SET R. BIT
		RELINT
		EXTEND
		DCA	TIME2
		DXCH	MARKTIME	# READ PRESENT TIME
		TC	DOWNFLAG
		ADRES	SKIPVHF
		TC	BANKCALL
		CADR	RADSTALL	# WAIT FOR RANGE COMPLETE
		TC	LIGHTON		# BAD DATA GOOD BIT
		TC	TRFAILOF	# TURN TRACKER LIGHT OFF
RANGERD1	CCS	RM		# 15 BIT UNSIGNED RANGE
		TC	RANGERD4	# GR + 0
		TC	LIGHTON	+4	# = + 0
		TC	RANGERD3	# L - 0
		TC	RANGERD3	# = - 0
RANGERD4	TC	INTPRET
		SLOAD	DMP
			RM
			CONVRNGE	# CONVERT RANGE TO METERS B-27
RANGERD2	STORE	VHFRANGE
		SET	RVQ
			SOURCFLG	# SOURCE OF DATA TO VHF RADAR
RANGERD3	CA	RM
		MASK	POSMAX
		TS	MPAC		# MASK OUT NEG. SIGN BIT
		TC	INTPRET
		DMP
			CONVRNGE	# CONVERT FROM NM TO METERS AND SCALE B-27
		DAD	GOTO
			RANGEB14	# VALUE IN METERS OF SIGN BIT SCALED B-27
			RANGERD2
LIGHTON		CA	VHFRANGE
		EXTEND
		BZF	+2
		TC	TRFAILON
		TC	INTPRET
		DLOAD
			MARKTIME
		STORE	VHFTIME
		GOTO
			REND1
RANGEB14	2DEC	303431.7 B-27	# 16384 X 18.52 SCALED B-27

OCT17		OCT	00017
OC40200		OCT	40200
CONVRNGE	2DEC	18.52 B-13	# VHF INPUT RANGE CONV. FROM .01 NM TO M

VHFREAD		EXTEND
		ROR	SUPERBNK	# MUST SAVE SBANK BECAUSE OF RUPT
# Page 588
		TS	BANKRUPT	# EXITS VIA TASKOVER BADEND OR GOODEND
		CS	ZERO
		TS	RUPTAGN
		EXTEND
		QXCH	QRUPT
		CS	STATE	+2
		MASK	SKIPVBIT	# SKIPVHF FLAG
		EXTEND
		BZF	TASKOVER	# BRANCH IF VHF RESTART BIT SET
		CAF	UPDATBIT
		MASK	STATE +1	# UPDATEFLG
		EXTEND
		BZF	BYPASS1
		CS	STATE	+4
		MASK	PDSPFBIT	# PDSPFLAG
		EXTEND
		BZF	BYPASS1
		CA	RNRAD
		TS	RM		# SAVE RANGE
		CAF	BIT2
		EXTEND
		RAND	CHAN33		# READ DATA GOOD BIT
		EXTEND
		BZF	VHFGOOD		# BRANCH IF DATA GOOD BIT EQUALS GOOD
BYPASS		TS	VHFRANGE	# STORE NON ZERO VALUE
		CAF	TWO
		TC	POSTJUMP
		CADR	BADEND
BYPASS1		CAF	ZERO
		TC	BYPASS
VHFGOOD		CAF	TWO
		TC	POSTJUMP
		CADR	GOODEND
SHIFTNDX	AXT,2	BON
			0
			VEHUPFLG
			SHIFTA		# VEHICLE IS CSM
		BON	RVQ
			LMOONFLG
			+1
		INCR,2	RVQ
		DEC	-2
SHIFTA		BON	RVQ
			CMOONFLG
			+1		# MOON ORB.
		INCR,2	RVQ
		DEC	-2
INITIALW	AXT,1	SSP
			36D
			S1
# Page 589
			6
		VLOAD
			ZEROVECS
INITA		STORE	W +36D,1	# CLEAR 0 - 35
		TIX,1	AXT,1
			INITA
			36D
INITB		STORE	W +90D,1	# CLEAR 54 - 89
		TIX,1	SLOAD
			INITB
			0		# POSITION VALUE
		STORE	W		# INITIALIZE DIAGONAL W POSITION
		STORE	W +8D
		STORE	W +16D
		SLOAD
			1		# VELOCITY VALUE
		STORE	W +72D		# INITIALIZE DIAGONAL W VELOCITY
		STORE	W +80D
		STORE	W +88D
		RVQ
# Page 590
# CRS61.1	R/10/68
#
# TO COMPUTE THE PREFERRED TRACKING ATTITUDE OF THE CSM WHICH ENABLES
# OPTICS TRACKING OF THE LEM AND LM TRACKING OF THE CSM RADAR TRANSPONDER
# AND TO COMPUTE THE X-AXIS TRACKING ATTITUDE OF THE CSM WHICH ENABLES
# COAS TRACKING OF THE LM.
#
# TO PERFORM THE MANEUVER TO THE SELECTED TRACKING ATTITUDE IF THE
# MANEUVER IS LESS THAN 10 DEGREES BUT TO CALL R60 IF THE MANEUVER IS
# GREATER THAN 10 DEGREES BUT TO CALL R60 IF THE MANEUVER IS
# GREATER THAN 10 DEGREES OR IF THE R60 FLAG IS SET.
#
#	(1)	EXTRAPOLATE LM AND CSM STATE VECTORS TO PRESENT TIME USING
#		CONIC EQUATIONS.
#
#	(2)	CALCULATE LOS FROM CSM TO LM = RL - RC.
#
#	(3)	THE PRERFERRED TRACKING ATTITUDE IS DEFINED AS FOLLOWS:
#		THE TRACK AXIS (I) IS ALIGNED ALONG THE LOS TO THE LM.  THE
#		TRACK AXIS (I) IS DEFINED AS:
#
#		UNIT(I)=UNIT(Z  )COS55  &  UNIT(X  )SIN55
#		     -       -SC                -SC
#
#	(4)	COMPUTE DESIRED CDU ANGLES, USING VECPOINT.
#
# (Sorry, I don't know where (5) and (6) are. --- RSB 2009.)
#	(7)	FORM DIFFERENCE BETWEEN DESIRED AND ACTUAL CDUS.
#		IF ANY OF THE THREE ANGLE DIFFERENCES EXCEEDS 10 DEGREES,
#		GROSS MANEUVER IS REQUIRED.  SIGNAL R61 (SET MPAC=1) TO
#		OPERATE KALCMANU AND EXIT CRS61.1.
#		IF ALL DIFFERENCES ARE LESS THAN 10 DEGREES, CONTINUE.
#
#	(8)	CALCULATE ORTHOGONAL LOS RATE IN REF COORDS AS
#
#		OMEGATH = (UNITLOS(B1) X UNITDV(B1))(ABSDV(B7)/ABSLOS(B29))
#
#		CONVERSION FACTOR OF 100/2PI (B4) REV CSEC PER RAD SEC IS
#		APPLIED TO YIELD UNITS OF REVS/SEC.  SCALE IS CARRIED AS
#		B+1+1+7-29+4+1 PLUS RESULTS OF NORMALIZING ABSDV, ABSLOS.
#		THE EXTRA B+1 RESULTS FROM RESCALING ABSDV B8 AFTER NORM
#		TO AVOID OVFLOW ON DIVIDE.
#
#		UNITLOS = UNIT( RL - RC ) B1.
#		UNITDY  = UNIT( VL - VC ) B1.
#		ABSLOS  = LENGTH OF LOS, METERS B29.
#		ABSDV   = LENGTH OF DV, METERS/CSEC B7.
#
#	(9)	OBTAIN RATE IN SM COORDS.
#
#		OMETATHSM = (REFSMMAT)(OMEGATH).
#
#	(10)	OBTAIN GIMBAL ANGLE INCREMENTS FOR 0.1 SECOND.
#
#		DTHETASM = (0.1)(OMEGATHSM)
#
#	(11)	OBTAIN DELCDUX,Y,Z USING SUBR SMCDURES.
# Page 591
#		INPUT CONSISTS OF
#
#		(A)	VECTOR OF ANGULAR INCREMENTS, DTHETASM, STORED
#			IN V(DTHETASM).
#		(B)	SIN,COS CDUX,Y,Z FROM SUBR CDUTRIG.
#
#		TRANSFER OUTPUT OF SMCDURES FROM V(DCDU) TO VAC14D.
#
#	(12)	CALCULATE ANG LOS RATE IN BODY(NB) COORDS USING SUBR SMNB.
#
#		OMEGANB = (SMNB)(OMETATHSM)
#
#		SUBR SMNB REQUIRES OMEGATHSM IN V(VAC32D) AND ACTUAL CDUS
#		(Y,X,Z ORDER) IN V(VAC20D) WITH S1 OF VAC = BASE ADDRESS
#		OF CDUS (FIXLOC + 20D).
#
#	(13)	CALCULATE ANG LOS RATE IN CONTROL COORDS AS FOLLOWS
#
#		WBODY = (MBDYTCTL)(OMEGANB)	UNITS=REVS/SEC(B0) (?).
#
#			       ( 0.5       0              0      )   BODY TO
#		MBDYTCTL(B1) = (  0   COS(7.25)B1   -SIN(7.25)B1 ) = CONTROL
#			       (  0   SIN(7.25)B1    COS(7.25)B1 )   AXES
#								     CONVERSION
#								     MATRIX
#
#	(14)	RESCALE WBODY TO UNITS OF 460 DEG/SEC BY APPLYING FACTOR
#		OF 0.8 TO REVS/SEC.
#
#	(15)	ADDRESS LIVE AUTOPILOT REGISTERS IN BASIC (UNDER INHINT).
#
#		TRANSFER DESIRED CDUS, SCALED 180 DEGREES, FROM T(SAVEDCDU)
#		TO V(CDUXD).
#
#		TRANSFER DELCDUS, SCALED 180 DEG, FROM V(VAC14D)
#		TO V(DELCDUX).
#
#		TRANSFER OMEGA CONTROL, SCALED 450 DEG/SEC, FROM V(MPAC)
#		TO V(WBODY).
#
#		RELINT, SET MPAC=0, EXIT CRS61.1.
#
# CALL:		L	CALL	CRS61.1
#
# RETURNS:	ALL TO L+1.
#
#		(1)	S(MPAC)=0.  NORMAL EXIT.  3 SETS OF INPUTS FED TO DAP.
#		(2)	S(MPAC)=1.  CALCULATED DESIRED CDUS,SP, SET IN T(CPHI)
#			FOR KALCMANU.  ABS(ACDU - DCDU) EXCEEDS 10 DEGREES.
#		(3)	S(MPAC)=2.  GNCS AUTO MODE NOT SELECTED (BIT10=1).
#		(4)	S(MPAC)=3.  DAP HOLD FLAG (HOLDFLAG) NOT EQUAL -1.
# Page 592
#
# INPUT:	(1)	TIME2,TIME1.  COMPUTER CLOCK TIME,DP, CENTISEC B28.
#		(2)	CDUX,Y,Z.  PRESENT CDU ANGLES,SP,2S COMPL HALF-REVS B0.
#		(3)	M(REFSMMAT), STABLE MEMBER COORDS B1.
#
# OUTPUT:	NORMAL.  EXIT WITH S(MPAC) = 0.
#
#		(1)	CDUXD,CDUYD,CDUZD, DESIRED OUTER, INNER, MIDDLE CDU ANGLES,
#			DP, IS COMPL, SCALED 180 DEGREES (HALF-REVS B0).
#		(2)	DELCDUX,DELCDUY,DELCDUZ.  0.1 SEC DCU ANGULAR INCREMENTS,
#			DP, IS COMPL, SCALED 180 DEG.
#		(3)	WBODY,WBODY1,WBODY2.  LOS ANGULAR RATE IN CONTROL COORDS,
#			DP, IS COMPL, SCALED 450 DEG/SEC.
#
#		SPECIAL.  EXIT WITH S(MPAC) = 1.
#
#		(1)	CPHI,CTHETA,CPSI.  DESIRED OUTER, INNER, MIDDLE CDU ANGLES,
#			SP, 2'S COMPL, SCALED 180 DEGREES.
#
# EXTERNAL SUBROUTINES USED	(B)=BASIC
#
#	(1) CALCGA		(5) LOADTIME(B)		(9) SMNB
#	(2) CDUTRIG		(6) MATMOVE
#	(3) CSMCONIC		(7) RCDUS(B)
#	(4) LEMCONIC		(8) SMCDURES
#
# ERASABLE
#
#	(1) S(Q611),EBANK7	CRS61.1 EXIT.
#	(2) S(Q6111),EBANK7	CALCDCDU EXIT.
#	(3) T(SAVEDCDU),E6	SP VECTOR OF CDUDS.
#	(4) V(SAVEPOS),E7	CSM POS VEC AND D(SAVEPOS)= LENGTH OF LOS.
#	(5) V(SAVEVEL),E7	CSM VEL VEC.
#
# FLAGWDS:	HOLDFLAG.  USED, NOT SET.
#
# MISC:	(1) ERASABLE ITEMP1 USED TO TEMP STORE EBANK UNDER INHINT.
#	(2) ERASABLE P21TIME USED AS TEMP STORE DURING CRS61.1
#	(3) ERAS DTHETASM USED AS TEMP STORE DURING EARLY CRS61.1
#
# DEBRIS -- CURRENT VAC AREA, CRS61.1 ERASABLES, ITEMP1, P21TIME

		BANK	24
		SETLOC	P20S4
		BANK

		EBANK=	CDUXD
		COUNT*	$/CRS61

CRS61.1		STQ	SETPD
			Q611
			0
		RTB
# Page 593
			LOADTIME	# LOAD CLOCK TIME2,1 INTO MPAC.

STORT		STCALL	P21TIME		# STORE CLOCK TIME FOR SUBR R63
			R63		# SUBR TO CALC DCDU (T=PRESENT,PASS1)
		TLOAD
			THETAD		# SAVE DCDU(T) FROM CALCDCDU FOR STEP4.
		STORE	SAVEDCDU

		EXIT
		TC	STEP2CK
AUTOCK		CAF	PRIO30
		EXTEND
		RXOR	CHAN31
		MASK	FURST3
		EXTEND			# AUTO MODE SELECTED (BITS 15-13=011)
		BZF	DAPCK		#	YES -- CONTINUE.
		TC	ASET

DAPCK		CS	FLAGWRD1	# IS STIKFLAG SET (I.E., IS SOMEONE ON RHC)
		MASK	STIKBIT
		CCS	A
		TC	STEP3CK
ASET		CAF	ZERO
		TS	MPAC
		TC	INTPRET		# EXIT CRS61.1
		GOTO
			Q611

STEP2CK		TC	BANKCALL
		CADR	UPACTOFF

		CAF	TWO		# SET TEMPORARY INDEX DTHETASM = 2
CDULOOP		TS	DTHETASM
		INDEX	DTHETASM
		CA	CDUX		# SET A = ACTUAL CDU (ACDU).
		EXTEND
		INDEX	DTHETASM	# SET INDEX TO ACCESS DESIRED CDU (DCDU).
		MSU	THETAD		# A = DIFF = ACDU - DCDU.
		TS	MPAC		# RETURN TO INTERPRETER FOR 10 DEGREE CK.
		TC	INTPRET		# (DP APPROX SP OK FOR ROUGH CHECK)
		ABS	DSU
			DEGREE10	# IS (ACDU - DCDU) MORE THAN 10 DEGREES.
		BPL	EXIT		# NO -- OK, CONTINUE CHECKING OTHER ANGLES.
			STKTEST		# TEST STICK FLAG
		CCS	DTHETASM	# HAVE ALL 3 ANGLE DIFFS BEEN CHECKED.
		TC	CDULOOP		# NO -- DIM COUNT, CHECK NEXT ANGLE DIFF.
		TC	AUTOCK
STKTEST		EXIT
		CS	FLAGWRD1
		MASK	STIKBIT
# Page 594
		CCS	A
		TC	MANUEXIS	# STIKFLAG IS NOT SET (DO R63)
		CAF	BIT3
		EXTEND			# STIKFLG IS SET
		WOR	DSALMOUT	# TURN ON UPACTY LIGHT

		TC	ASET		# EXIT AND SET R61CNTR
STEP3CK		TC	INTPRET
		SETPD
			0		# *
					# NOW HAVE DCDUS STORED IN T(SAVEDCDU).
					# GO CALC OTHER DAP INPUTS (DELCDU,WBODY)
CRS61.2		VLOAD	VSU
			DCDU
			SAVEVEL		# DV = VL - VC
		UNIT	VCOMP		# V(MPAC)=-UNITDV. VAC36D=ABSDV.
		VXV	VXSC		# (-UNITDV)CROSS(UNITLOS).
			SAVEPOS
			RVCS/RDS	# (UNITLOS B1)(UNITDV B1)(CONST B4)=CROSS.
		PUSH			# HOLD CROSS IN PUSHLIST0.  SCALED B6.
		DLOAD	NORM		# OBTAIN ABS VALUE OF LOS.
			P21TIME		# P21TIME IS TEMP STORE FOR ABSLOS.
			X1
		PUSH			# NORM ABSLOS(DENOM) AND HOLD IN PUSH1.

		DLOAD	NORM
			36D		# NORM ABS VALUE OF DV(NUM).
			S1

		XSU,1	SR1		# X1 = X1(N DENOM)-S1(N NUM).
			S1		# SR1 TO AVOID OFLOW ON DOV.
		DDV	VXSC		# ABSDV(MPAC)/ABSLOS(PUSH1) = QUOT.
		SXA,1			# QUOT(MPAC) X CROSS(PUSH0)
			Q6111		# SAVE SCALE OF RESULT (R-15,1X).
					# X1= NORM OF QUOT. QTUOT SCALE B7-B29=B-22
					# CROSS IS SCALED B6.  NEED SL1 TO RECOVER
					# SR1 SO THAT -22+6+1=-15.  MPAC NOW HOLDS
					# ORTHO LOS RATE (OMEGA TH, B-15,X1).
		MXV	VSL1		# OBTAIN RATE IN SM COORDS (OMEGTHSM) AND
			REFSMMAT	# ADJUST FOR REFSMMAT SCALE OF B1.
		STORE	20D		# OMEGTHSM = VAC20D
					# DELTA THETA SM = OMEGTHSM * .1B-3.
		VXSC
			TENTH
		STORE	DTHETASM	# STORE SM INCREM ANGLES FOR SMCDURES.
		CALL
			CDUTRIG		# OBTAIN SIN,COSCDUS FOR SMCDURES.
		SETPD	CALL		# SMCDURES USES PUSH
			0
			SMCDURES	# OBTAIN DELCDU IN V(DCDU).
# Page 595
		LXA,1			# RELOAD X1
			Q6111
		VLOAD	VSL*		# RECOVER SCALE.
			DCDU		# (B-15,X1) + TENTH(B-3) + HALFREVS(B1)
			0 -17D,1	# EQUALS B-17D,1 TO OBTAIN HALFREVS B0.
		STORE	14D		# HOLD DELS IN V(VAC14D) FOR AUTOPILOT.

		CALL			# COMPUTES SINES AND COSINES FOR *SMNB*
			CDUTRIG
		VLOAD	CALL		# LOAD VECTOR AND CALL TRANSFORMATION
			20D		# VECTOR FOR TRG*SMNB INTO MPAC
			*SMNB*		# OBTAIN ANG. RATE REFERRED TO NB (BODY)
		MXV
			MBDYTCTL	# CONVERT RATE(OMEGA) TO CONTROL COORDS.
		VXSC			# MULT. BY 0.8 TO RESCALE REVS TO 450 DEG.
			POINT8		# RECOVER SCALE.
		LXA,1	VSL*		# RELOAD X1 TO RECOVER NORMALIZ.
			Q6111		# (B-15,X1) + MBDYTCTL(B1) = B-14D,1 TO
			0 -14D,1	# OBTAIN REVS SCALED AT 450 DEGREES.

CRS61.2A	EXIT
		INHINT
		CAF	ZERO		# TRANSFER DESIRED GIMBAL ANGLES
		TS	CDUXD	+1	# FROM T(SAVEDCDU) TO V(CDUXD).
		TS	CDUYD	+1
		TS	CDUZD	+1
		CA	SAVEDCDU
		TS	CDUXD
		CA	SAVEDCDU +1
		TS	CDUYD
		CA	SAVEDCDU +2
		TS	CDUZD

		EXTEND			# TRANSFER OMEGA CONTROL (ANG. LOS RATE)
		DCA	MPAC		# FROM V(MPAC) TO V(WBODY)
		DXCH	WBODY
		EXTEND
		DCA	MPAC	+3
		DXCH	WBODY1
		EXTEND
		DCA	MPAC	+5
		DXCH	WBODY2

		EXTEND			# TRANSFER CDU INCREMENTS
		INDEX	FIXLOC		# FROM V(VAC14D) TO V(DELCDUX)
		DCA	14D
		DXCH	DELCDUX
		EXTEND
		INDEX	FIXLOC
		DCA	16D
# Page 596
		DXCH	DELCDUY
		EXTEND
		INDEX	FIXLOC
		DCA	18D
		DXCH	DELCDUZ
		CS	ONE		# NOW DAP VARIABLES LOADED.  SET HOLDFLAG.
		TS	HOLDFLAG	# TO -1.
		RELINT
		TC	ASET
MANUEXIS	TC	INTPRET
MANUEXIT	TLOAD			# ENTER FROM STEP2.  ACDU-DCDU EXCEEDS
			SAVEDCDU	# 10 DEG. STORE DCDU(T) IN CPHI,CTHETA,
		STORE	CPHI		# CPSI FOR KALCMANU.
		SLOAD	GOTO		# SPECIAL RETURN (MPAC+0 = 1)
			LOONE		# OCTAL 00001
			Q611

R63		STQ	DLOAD		# SUBR TO CALC DCDUS(T)
			Q6111
			P21TIME
		STCALL	TDEC1
			CSMCONIC
HOLDATT		VLOAD			# HOLD EXTRAPOLATED CSM POSITION AND
			RATT		# VELOCITY
		STOVL	SAVEPOS
			VATT
		STORE	SAVEVEL
CALCLEM		DLOAD			# EXTRAPOLATE LEM STATE VECTOR TO SAVE
			P21TIME		# TIME AS CSM USING LEMCONIC
		STCALL	TDEC1
			LEMCONIC
		VLOAD
			VATT
		STOVL	DCDU		# STORE VATT IN DCDU TEMPORARILY
			RATT		# LOS = RL RC
		VSU	UNIT
			SAVEPOS
		STORE	SAVEPOS		# SAVE UNITLOS FOR CRS61.2 RATE CALC.
		MXV	VSL1
			REFSMMAT	# CONVERT TO STABLE MEMBER
		STODL	POINTVSM
			36D		# HOLD ABS VAL OF LOS (VAC 36D)
		STORE	P21TIME		# IN D(P21TIME) FOR CRS61.2 RATE CALC.
		VLOAD
			UNITX
		STCALL	SCAXIS		# TRACK AXIS UNIT VECTOR
			VECPOINT	# FOR +X-AXIS TRACKING ATTITUDE
		STORE 	CPHIX		# STORE ANGLES FOR N96 DISPLAY
		VLOAD
			PRFUNIT
# Page 597
		STCALL	SCAXIS
			VECPOINT
		STORE	PRAXIS		# STORE ANGLES FOR N95 DISPLAY
		BOFF
			PRFTRKAT
			CRSTOR1
CRSTOR		STORE	THETAD		# STORE ANGLES FOR N18 DISPLAY
		GOTO
			Q6111
CRSTOR1		VLOAD
			UNITX
		STORE	SCAXIS
		TLOAD	GOTO
			CPHIX
			CRSTOR
PRFUNIT		2DEC	.40957602	# 55 DEG TRACK AXIS UNIT VECTOR

		2DEC	0.0		# FOR USE WITH VECPOINT

		2DEC	.28678822

DEGREE10	DEC	.05556		# 10 DEG IN REVS		STEP2
RVCS/RDS	2DEC	15.915494 B-4	# 100/2PI REV-CSEC/RAD-SEC.

TENTH		2DEC	.1 B+3		# .1 B-3 (TO SCALE ANG. RATE TO .1 INREMS)

MAT1B1		2DEC	1.0 B-1

MBDYTCTL	2DEC	.5		# 		7.25 DEG NEGATIVE

		2DEC	0		#		X-AXIS ROTATION MATRIX

		2DEC	0		#		CONVERTS BODY TO CTL

		2DEC	0		#		AXES.  CAME AS QUADROT

		2DEC	.99200495 B-1	# COS7.25 B1	BUT SCALED B

		2DEC	-.12619897 B-1	# -SIN7.25 B1

		2DEC	0

		2DEC	.12619897 B-1	# SIN7.25 B1

		2DEC	.99200495 B-1	# COS7.25 B1

LOONE		OCT	00001		# TO SET MPAC = 00001 FOR SPECIAL EXIT.
FURST3		EQUALS	13,14,15	# CONSTANT FOR AUTOCK (OCT 70000).

# Page 598
# ..... S22.1 ORBITAL NAVIGATION ROUTINE
# MOD 1
#
# FUNCTIONAL DESCRIPTION
#	1.  UPDATE CSM STATE VECTOR
#	2.  UPDATE LANDMARK POSITION
#	3.  CONVERT W MATRIX FROM 9 TO 6 DIMENSIONS
#
# SUBROUTINES CALLED
#	INTSTALL,INTEGRV,GETNUM,SETRE,R-TO-TP,RP-TO-R,BVECTORS,INCORP1,INCORP2
#	LALOTORV,S22F2410,LAT-LONG,ROWDOT
#
# ERASABLE INITIALIZATION
#	W=9X9 MATRIX
#	ORBWFLAG=0 FOR INVALID W MATRIX, =1 FOR VALID W MATRIX
#	ASTRONAUT ENTRY OF KNOWN,L,OFF
#	8NN= NUMBER OF MARKS, DECIMAL INTEGER B-14
#	REFSMMAT= TRANSFORMATION MATRIX
#	MARKSTAT= ADDRESS OF START OF MARK DATA (MARK DATA OF EACH MARK IS
#		  STORED AS FOLLOWS: TIME,AIG,SA,AMG,PA,AOG) TIME IS IN DOUBLE
#		  PRECISION, ALL OTHERS ARE IN SINGLE PRECISION
#	CSM STATE VECTOR
#
# OUTPUT
#	UPDATED CSM STATE VECTOR
#	UPDATED LANDMARK POSITION
#	NEW 6 DIMENSIONAL W MATRIX
#
# DEBRIS
#	PUSH LIST,CSMPOS,ALPHAV,ERADM,UM,RCLP,USTAR,VARIANCE,X789,BVECTOR,8KK,
#	S22LOC,SVMRKDAT TABLE,22SUBSCL,LANDMARK,CXOFF,S22C,LAT,LOG,ALT,
#	TEMPOR1,S22TOFF,S221OFF,DSPTEM1,S22EORM,S22TPRIM

		BANK	13

		SETLOC	P20S6
		BANK

		EBANK=	LANDMARK
		COUNT	35/LUORB

S22.1		STQ	SSP
			S22RTNEX
			S1
		DEC	6
		SSP	SSP		# SET I=1	ITEM 8KK IS I
			8KK
		DEC	1
			S22LOC
		ECADR	SVMRKDAT	# SET MARK DATA ADDRESS INTO S22LOC
# Page 599
		LXC,2	AXT,1
			MARKSTAT
		DEC	36
S22.111		VLOAD*			# MOVE MARK DATA (5 SETS FROM ADDR. IN
			0,2		# MARKSTAT TO SVMRKDAT TABLE TO AVOID LOSS
		STORE	SVMRKDAT +36D,1	# IF RESTART OCCURS
		INCR,2	TIX,1
		DEC	-6
			S22.111
		SET	EXIT
			P22MKFLG	# DOWNLINKED SVMRKDAT HOLDS PRESENT MARKS
		TC	BANKCALL	# RELEASE VAC AREA WHERE MARK DATA WAS
		CADR	MKRELEAS
		TC	2PHSCHNG
		OCT	00004
		OCT	05022
		OCT	13000
		TC	INTPRET
		AXT,1	BOFF
			0D
			CMOONFLG	# =0 EARTH, =1 MOON
			S22SHIFT
		INCR,1
		DEC	-2
S22SHIFT	SXA,1	SETPD
			S22EORM		# SET =0 EARTH, =-2 MOON FOR SHIFTING
			0D
FIG2EXIT	CALL
			INTSTALL
		CALL
			S22FLGS
#	FLOWCHART D=0	THEN DIM0FLAG=0, D6O9FLG NOT TESTED
#	FLOWCHART D=6	THEN DIM0FLAG=1, D60R9FLG=0
#	FLOWCHART D=9	THEN DIM0FLAG=1, D6OR9FLG=1

		BOFF	CLRGO
			ORBWFLAG
			SETWW5D		# BRANCH TO SET W0-W5,ORBWFLAG,D
			D6OR9FLG	# FLOWCHART D=6 PATH
			SETVANDI
SETWW5D		CLEAR
			DIM0FLAG	# FLOWCHART D=0 PATH
		AXT,1	SSP
		DEC	108
			S1
		DEC	6
		CLEAR	VLOAD
			RENDWFLG	# GSOP CHANGE 8/18/67
			ZEROVECS
CLEARWW5	STORE	W +108D,1
# Page 600
		TIX,1	SLOAD
			CLEARWW5
			WORBPOS
		STORE	W		# SET DIAGONALS OF W0
		STORE	W +8D
		STORE	W +16D
		SLOAD
			WORBVEL
		STORE	W +72D		# SET DIAGONALS OF W4
		STORE	W +80D
		STORE	W +88D
SETVANDI	CLEAR
			DMENFLG		# 0=6X6W, 1=9X9W
S22NXTIN	CALL
			GETTF
		STCALL	TDEC1
			INTEGRV
		CALL
			S22CALRC	# CALC. RC B-29 OR B-27 (CSMPOS)
		LXA,1	SXA,1
			S22LOC		# SETUP ADDR. OF MARK DATA FOR GETUM SUBR.
			MARKDATA
		CALL			# COMPUTE UM
S2GETUM			GETUM
		STORE	UM
DMPINTEG	SLOAD	PUSH		# TEST OFF=I
			8KK
		SLOAD	SR3		# CXOFF SCALED B-5, MUST MOVE TO B-14
			CXOFF		# BEFORE SUBT.
		SR3	SR3
		DSU
		BHIZ	BON
			S22OFF=I	# BRANCH HERE IF OFF=I
			DMENFLG		# 0=6X6W, 1=9X9W
			S22D=9
		CALL
			GRP2PC
		SET
			ORBWFLAG
		SET	SET
			DMENFLG		# =0 ON FIRST PASS THRU HERE FOR D=0, OR 6
			22DSPFLG	# =1 TO DISPLAY DR,DV ON FIRST PASS
		SET	BON
			ERADFLAG	# =1 TO COMPUTE FISCHER RADIUS
			KNOWNFLG
			S22BOX22
		VLOAD	UNIT		# UNIT ALSO PUTS ABVAL(RC) IN 36D
			CSMPOS
		STORE	ALPHAV		# ALPHAV +4=SINL FOR SETRE
		CLEAR	BOFF
# Page 601
			LUNAFLAG
			CMOONFLG
			S22C=I
		SET
			LUNAFLAG
S22C=I		CALL			# ERADM= R0 METERS B-29 BOTH EARTH/MOON
			SETRE
		CALL			# COMPUTE RL FROM EQUATION 2.4.10
			S22F2410	# STORED IN X789,MPAC B-27,B-29
		BOFF	VSR2		# SCALE RL B-29 FOR BOTH EARTH/MOON
			CMOONFLG
			+1
		STORE	S22RL
		DOT	SL1
			UM
		STOVL	S22D		# D=UM RL B-29
			ZEROVECS
		SETPD	PUSH
			0D
		PUSH	PDDL		# SET 0-18D = I BACKWARDS
			HIDPHALF	# PD 18
		SR2			# B-3
		STORE	4D
		STORE	8D
		STOVL	12D
			UM		# B-1
		STOVL	S223X1
			S22RL		# B-29
		CALL			# (UM)(RL T) B-30 STORED IN S22UMRL THRU
			S2231X13	# S22UMRL +17D
		AXT,1	SSP
		DEC	18
			S1
		DEC	6
S22NXTU		VLOAD*	VSR2		# (UM)(RL T) B-32
			S22UMRL +18D,1
		V/SC
			S22D		# D B-29
		BVSU	STADR		# SUBTRACT FROM I B-3
		STORE	S22UMRL +18D,1	# U MATRIX B-3
		TIX,1	AXT,1		# PD 0 AFTER TIX
			S22NXTU
		DEC	36		# S1 STILL 6 FROM ABOVE
S22NXTWI	VLOAD*	MXV
			W +36D,1	# B-19
			S22UMRL		# B-3
		VSL3
		STORE	W +144D,1	# W(I+18)= UW(I) B-19
		TIX,1	DLOAD
			S22NXTWI
# Page 602
			S22RHO		# B-28,B-30
		BOFF	SR2		# MAKE RHO B-30
			CMOONFLG
			+1
		NORM	XAD,2
			X2
			X2
		DSQ	DMP
			SCTVAR		# B+16
		SR1			# ACCOUNTS FOR 1/2 IN NEXT FORMULA
		STORE	S22RHO		# 1/2(RHO SQ)(VARSCT)
		AXT,1
		DEC	18		# S1 STILL 6 FROM ABOVE
S22NXXA		VLOAD*	MXV
			S22UMRL +18D,1	# B-3
			S22UMRL		# B-3
		VXSC	VSR*
			S22RHO
			0 -12D,2	# WITH VARRP SCALED B-28
		STORE	S22UUT +18D,1	# 1/2(RHO SQ)(VARSCT)(U)(U T)
		TIX,1	VLOAD
			S22NXXA
			UM
		STCALL	S223X1		# UM ALSO IN MPAC FOR S2231X13 SUBR.
			S2231X13	# (UM)(UM T) B-2 IN S22UMRL,P17D
		DLOAD	SR3
			ERADM		# B0 B-29
		DDV	DSQ
			S22D		# B-29
		DMP
			RPVAR		# ***** METERS SQ
		STORE	S22RHO		# TEMP (VARRP)(R0/D)
		AXT,1
		DEC	18		# S1 STILL 6 FROM ABOVE
S22NXXB		VLOAD*	VXSC
			S22UMRL +18D,1	# (UM)(UM T) B-2
			S22RHO
		VAD*
			S22UUT +18D,1
		STORE	S22UUT +18D,1	# SMALL E MATRIX
		VLOAD
			ZEROVECS
		STORE	W +162D,1	# CLEAR W8
		TIX,1	BOV
			S22NXXB
			+1
		DLOAD	BMN
			S22UUT +16D	# E5
			S22W76X
		SQRT	BZE
# Page 603
			S22W76X
		STODL	W +148D		# W74= SQ ROOT E5
			S22UUT +14D	# E4
		DDV	BOV
			W +148D
			S22W72X
		STORE	W +146D		# W73= E4/W74
S22W72X		DLOAD	DDV
			S22UUT +12D	# E3
			W +148D
		BOV
			S22W76X
		STORE	W +144D		# W72= E4/W74
S22W76X		DLOAD	DSQ
			W +146D		# W73
		BDSU	BMN
			S22UUT +8D	# E2
			S22W78X
		SQRT	BZE
			S22W78X
		STODL	W +152D		# W76= SQ ROOT (E2-W73 SQ)
			W +144D		# W72
		DMP	BDSU
			W +146D		# W73
			S22UUT +6D	# E1
		DDV	BOV
			W +152D		# W76
			S22W78X
		STORE	W +150D		# W75= (E1-W72W73)/W76
S22W78X		DLOAD	DSQ
			W +150D
		PDDL	DSQ
			W +144D		# W72
		DAD
		BDSU	BMN
			S22UUT		# E0
			S22SCLW
		SQRT
		STORE	W +156D		# W78= SQ RT(E0-W72 SQ-W75 SQ)
S22SCLW		VLOAD	VSR1		# SCALE W6 METERS B-19
			W +144D
		STOVL	W +144D
			W +150D
		VSR1
		STOVL	W +150D
			W +156D
		VSR1
		STORE	W +156D
S22SAVET	CALL
			GETTF
# Page 604
		STORE	S22TPRIM	# SAVE PRESENT TIME FOR PIOS
S22I=N		EXIT			# TEST I=N
		TC	PHASCHNG
		OCT	04022
		CS	8KK
		AD	8NN
		EXTEND
		BZMF	S22F244X	# EXIT TO FIGURE 2.4-4
		CA	8KK		# I=I+1
		AD	ONE
		TS	TEMPOR1
		CA	S22LOC		# ADD 7 TO LOC TO GET ADDR. OF NEXT MARK
		AD	SEVEN
		TS	TEMPOR1 +1
		TC	PHASCHNG
		OCT	04022
		CA	TEMPOR1
		TS	8KK
		CA	TEMPOR1 +1
		TS	S22LOC
		TC	INTPRET
		CALL			# FOR ALL INTEGRATIONS OTHER THAN FIRST
S2INTS1			INTSTALL
		CALL
			S22FLGS
		BON	CLEAR
			DMENFLG
			S22NXTIN	# RETURN ALWAYS EXCEPT OFFSET POINT MARK 1
			DIM0FLAG
		BOFF	SET
			ORBWFLAG
			S22NXTIN	# OFFSET POINT MARK 1, NO W INTEGRATION
			DIM0FLAG
		CLRGO
			D6OR9FLG
			S22NXTIN	# OFFSET POINT MARK 1, INTEGRATE W 6X6
S22OFF=I	CALL
			GETTF
		STOVL	S22TOFF		# TIME SUB OFF
			UM
		STCALL	S22UOFF		# U SUB OFF
			S22I=N		# TEST I=N
S22D=9		VLOAD			# D=9 PATH
			X789
		STODL	0D		# CALL PIOS TWICE TO TRANSFORM RL TO TIME
			S22TPRIM	# T(SUB F) FROM TIME T PRIME
		STORE	6D
		SLOAD	CALL
			S22EORM		# 0=EARTH, NON-ZERO=MOON
S2RTRP			R-TO-RP
# Page 605
		PUSH	CALL		# R-TO-RP LEAVES PUSHLOC AT 0
			GETTF
		STORE	6D
		SLOAD	CALL
			S22EORM
S2RPTR			RP-TO-R
S22BOX32	STORE	X789
		SET	BOV
			INCORFLG	# FLAG=1
			+1		# CLEAR OVERFLOW
		VSU
			CSMPOS
		STORE	RCLP		# RCL=RL-RC
		UNIT	VXV		# USTAR=UNIT(UNIT(RCL)XUM)
			UM
		UNIT	BOV
			S22SAVET	# COMPUTATION OVERFLOW, SAVE TF
		STORE	USTAR
S22BOX12	SET	SET
			DMENFLG		# =1 FOR 9X9 W
			VEHUPFLG	# =1 FOR CSM
		DLOAD	DAD
			SCTVAR		# B+18
			IMUVARR		# B+18
		STOVL	VARIANCE
			RCLP		# B-29 OR B-27
		ABVAL	NORM
			X1
		DSQ	DMP
			VARIANCE
		XAD,1	XAD,1
			X1		# DOUBLE NORM SHIFT SINCE RCLP WAS SQUARED
			S22EORM		# DOUBLE EARTH OR MOON SHIFT, SAME REASON
		XAD,1	SR*
			S22EORM
			0,1		# SCALE VARIANCE B-40 FOR BOTH EARTH, MOON
		TLOAD			# CHANGE MODE TO TRIPLE
			MPAC
		STCALL	VARIANCE	# CALC B0,B1,DELTAQ, NEW USTAR
S2BVTRS			BVECTORS
		VLOAD	VCOMP
			BVECTOR
		STCALL	BVECTOR +12D	# B2=-B0
S2INCP1			INCORP1
		CALL
			GRP2PC
		BOFF	CLEAR
			22DSPFLG	# =1 DISPLAY DELTA R,V	  =0 DO NOT
			S22BOX42
			22DSPFLG
# Page 606
		CALL
			GRP2PC
		VLOAD	ABVAL
			DELTAX		# DELTA R
		LXA,1	SR*
			S22EORM		# SCALE DELTA R ALWAYS METERS B-29
			0,1
		STOVL	N49DISP
			DELTAX +6	# DELTA V
		ABVAL	SR*		# DELTA V=METERS/CSEC B-7 ALWAYS
			0,1
		STORE	N49DISP +2
		EXIT
		CAF	V06N49EE
		TC	BANKCALL
		CADR	GOFLASHR
		TC	GOTOPOOH	# V34E TERMINATE
		TC	+5		# INCORPORATE CHANGES
		TC	S22EXEX		# V32E RECYCLE
		CAF	BIT3
		TC	BLANKET
		TC	ENDOFJOB
		TC	INTPRET
S22BOX42	CALL
			INCORP2
		CALL			# CSMPOS=RC B-29 OR B-27
			S22CALRC
DMPINCP2	BOFF	CALL
			INCORFLG
			S22SAVET	# SAVE TF AND TEST I=N
			GRP2PC
		CLEAR	VLOAD
			INCORFLG	# FLAG=0
			X789
		VSU
			CSMPOS
		STCALL	RCLP		# RCL=RL-RC
			S22BOX12
S22BOX22	AXT,1	SSP		# CLEAR W6,W7,W8.  (27 ELEMENTS 54 REGS)
		DEC	54
			S1
		DEC	6
		VLOAD
			ZEROVECS
CLRW678		STORE	W +162D,1
		TIX,1	SLOAD
			CLRW678
			S22WSUBL
		STORE	W +144D
		STORE	W +152D
# Page 607
		STORE	W +160D
		CLEAR	BOFF		# SET LUNAFLAG, TIME FOR LALOTORV
			LUNAFLAG	# ERADFLAG,LAT,LONG,ALT SET PREVIOUSLY
			CMOONFLG	# CHECK SCALING OF ITEMS,ALT INPUT AND
			S22BX22A	# RL OUTPUT IN ALPHAV BOTH B-29
		SET
			LUNAFLAG
S22BX22A	CALL
			GETTF
		CALL			# COMPUTE RL
			LALOTORV
		VLOAD	BOFF
			ALPHAV		# RL B-29
			CMOONFLG
			S22BX22B
		VSL2			# SCALE RL B-27 FOR MOON
S22BX22B	GOTO
			S22BOX32
S22F244X	TC	INTPRET
S22F244		SLOAD	BHIZ		# FIG 2.4-4	TEST OFF=0
			CXOFF
			S22BOX44
		SR			# SCALE OFFSET B-14 THEN GET GR. 8NN
			9D
		STORE	ALPHAV		# TEMP
		SLOAD	DSU
			8NN
			ALPHAV
		BMN	CALL		# OFFSET GR. NO. MARKS.  FORGET IT
			S22BOX44
			GRP2PC		# GROUP 2 PHASE CHANGE
		DLOAD
			S22TOFF
		STCALL	TDEC1		# CALC RC AT OFFSET TIME
			CSMPREC
		VLOAD
			RATT1		# RC METERS B-29 OR B-27
		STOVL	CSMPOS
			S22UOFF
		STOVL	UM		# U=UOFF
			X789
		ABVAL	BOFF
			CMOONFLG
			+2
		SR2			# SCALE MOON R0 B-29 FOR S22F2410 SUBR
		STCALL	ERADM
			S22F2410
		GOTO
			S22BX44A
S22BOX44	CALL
# Page 608
			GETTF
		STORE	S22TOFF		# PRESENT TIME FOR LAT-LONG SETUP
S22BX44A	CLEAR	VLOAD
			LUNAFLAG
			X789
		BOFF	SET
			CMOONFLG
			S22BX44B
			LUNAFLAG	# SET = 1 FOR LAT-LONG
		VSR2			# SCALE RL MOON B-29 FOR LAT-LONG
S22BX44B	STODL	ALPHAV		# RL SCALED B-29 FOR LAT-LONG
			S22TOFF		# EITHER PRESENT OR OFFSET TIME
		CALL
			LAT-LONG	# **** ALT OUTPUT ALWAYS B-29
		CALL			# DISPLAY LAT/LONG/ALT
			LLASRD
		EXIT
		CAF	V06N89B
		TC	BANKCALL
		CADR	GOFLASH
		TC	S22GTP		# V34E TERMINATE
		TC	+2		# PROCEED	SAVE LANDING SITE COORD
		TC	S22.981X	# RECYCLE	POINT A IN GSOP
		TC 	INTPRET
		DLOAD
			S22TOFF		# EITHER PRESENT OR OFFSET TIME
		STOVL	6D		# 6-7D= LANDING SITE TIME FOR R-TO-RP
			X789
		STORE	0D		# 0-5D= LANDING SITE VEC FOR R-TO-RP
		SLOAD	CALL
			HIDPHALF	# ANY NON-ZERO FOR MOON
			R-TO-RP		# CONVERT RLS TO MOON-FIXED COORD
		STORE	RLS		# LANDING SITE VECTOR
		EXIT
S22.981X	TC	INTPRET
		CALL
			9DWTO6DW
		EXIT			# GO TO POINT A IN CHAPTER 5
S22EXEX		TC	INTPRET		# WITHOUT CONVERTING W
		GOTO
			S22RTNEX

S22GTP		TC	INTPRET		# CONVERT W BEFORE TC GOTOPOOH
		CALL
			9DWTO6DW
		EXIT
		TC	GOTOPOOH
S22F2410	SETPD	VLOAD		# COMPUTE FORMULA 2.4.10
			0D
			CSMPOS		# RC B-29 EARTH, B-27 MOON
# Page 609
		UNIT	DOT		# UNIT ALSO SETS 36D=ABVAL(RC) USED BELOW
			UM
		SL1	DCOMP		# GSOP CHANGE 8/18/67
		PUSH			# PD 2D 8D=COSA=-(UM.RC)/ABVAL(RC) 	B-1
		DSQ	BDSU
			DEC1B2
		PDDL	BOFF		# PD 4D 2D=1-COSA SQ=SINA SQ 		B-2
			ERADM		# R0 ALWAYS B-29 FROM SETRE
			CMOONFLG
			+2
		SL2			# SCALE R0 B-27 FOR MOON
		SR1R	DDV		# (R0/RC) 	B-1
			36D
		DSQ	DSU		# PD 2D (RP/RC) SQ - SINA SQ		B-2
		SQRT	BDSU		# PD 0D COSA-SQRT((R0/RC)SQ-SINA SQ)	B-1
		DMP			# DMP RESULT B-28 MOON, B-30 EARTH
			36D		# VXSC RESULT B-29 MOON, B-31 EARTH
		STORE	S22RHO		# RHO FOR W INIT. OF UNKNOWN LMK B-28,B-30
		VXSC
			UM
		VSL2	VAD		# SCALE B-27 MOON, B-29 EARTH AND ADD RC
			CSMPOS
		STORE	X789
		RVQ			# B-27 FOR EARTH OR B-29 FOR MOON
S22CALRC	LXA,1	VLOAD		# COMPUTE RC B-29 OR B-27
			S22EORM		# =0 FOR EARTH, -2 FOR MOON
			DELTACSM
		VSR*	VAD
			7,1
			RCVCSM
		STORE	CSMPOS
		RVQ
		SETLOC	P22S
		BANK

S2231X13	STORE	S221X3		# MULT 3X1 BY 1X3, STORE RESULTING 3X3 IN
		SSP	AXT,2		# S22UMRL - S22UMRL+17D
			S2
		DEC	2
		DEC	6
		AXT,1
		DEC	18
S2231NXT	VLOAD	VXSC*
			S221X3
			S223X1 	+6,2
		STORE	S22UMRL +18D,1
		INCR,1	TIX,2
		DEC	-6
			S2231NXT
		RVQ
# Page 610
GETTF		LXC,1	DLOAD*		# SET MPAC= TF
			S22LOC
			0,1
		RVQ
S22FLGS		SET	SET		# INTEGRATION FLAGS
			DIM0FLAG
			D6OR9FLG
		SET	SET
			VINTFLAG
			STATEFLG
		CLEAR	RVQ
			INTYPFLG

# SUBROUTINE TO MODIFY ALT AND STORE LAT TO LAT+5 IN LANDLAT TO LANDLAT+5
# PRIOR TO DISPLAY.

LLASRD		DLOAD			# ALT, LANDALT METERS B-29
			ALT
		STODL	LANDALT
			LONG
		SR1
		STORE	LANDLONG
		RVQ

# SUBROUTINE TO MODIFY LANDALT AND STORE LANDALT TO LANDALT+5 IN LAT TO
# LAT+5 AFTER LMK DATA LOADED BY ASTRONAUT.

LLASRDA		DLOAD			# ALT, LANDALT METERS B-29
			LANDALT
		STODL	ALT
			LANDLONG
		SL1
		STORE	LONG
		RVQ
		SETLOC	P20S6
		BANK

9DWTO6DW	STQ	SETPD
			9DWXX
			0D
		VLOAD	PUSH		# CLEAR WORKING AREA OF PUSHLIST
			HI6ZEROS	# INCLUDING P
		PUSH	PUSH		# PD 18D
		SSP
			9DWJ		# J=29	USE 2*29 FOR DP WORDS
		DEC	58
9DWI=J		LXA,1	SXA,1		# SET I=J
			9DWJ
			9DWI
9DWEPCAL	CALL
# Page 611
			ROWDOT
		LXA,1			# P VARIES 0-20 INSTEAD OF 20-0
			9DWP
		STORE	EMATRIX +40D,1
		INCR,1	SXA,1
		DEC	2
			9DWP
		SLOAD	BHIZ		# TEST I=0
			9DWI
			9DWTESTJ
		DSU			# I=I-1
			9DWID
		STORE	9DWI
		DSU	BHIZ		# TEST I=26
			9DW26D
			9DWSETI2
		GOTO			# NEXT E SUB P
			9DWEPCAL
9DWSETI2	SSP	GOTO		# I=2
			9DWI
		DEC	4
			9DWEPCAL
9DWTESTJ	SLOAD	BHIZ		# TEST J=0
			9DWJ
			9DWFIG6
		DSU
			9DWID
		STORE	9DWJ		# J=J-1
		DSU	BHIZ		# TEST J=26
			9DW26D
			9DWSETJ2
		GOTO
			9DWI=J
9DWSETJ2	SSP	GOTO		# SET J=2
			9DWJ
		DEC	4
			9DWI=J
9DWFIG6		CALL
			GRP2PC
		SSP	VLOAD		# START OF FIGURE 2.4-6
			9DWJ		# J=29
		DEC	58
			HI6ZEROS
		STORE	9DWP		# P,N,I=0
		AXT,1	SSP
		DEC	108		# CLEAR W0 TO W54
			S1
			6
CLEARW54	STORE	W +108D,1
		TIX,1
# Page 612
			CLEARW54
9DWI=JA		LXA,1	SXA,1		# I=J
			9DWJ
			9DWI
		CALL
			ROWDOT
		LXA,1	BDSU*
			9DWP
			EMATRIX +40D,1
		INCR,1	SXA,1		# -(P+1)
			2
			9DWP
		LXC,1	XSU,1		# -(I+N)
			9DWI
			9DWN
		BPL	DLOAD		# TEST WSQ LTE 0
			9DWAAA
			HI6ZEROS	# W=0
		GOTO
			9DWAAB
9DWAAA		SQRT			# W= SQRT(WSQ)
9DWAAB		STORE	W,1
		STODL	WORKW
			9DWJ		# TEST J=0
		BHIZ
			9DWEXITX	# EXIT
TST2I=0		SLOAD	BHIZ		# TEST I=0
			9DWI
			9DWN=N+3
		DSU
			9DWID
		STORE	9DWI		# I=I-1
		DSU	BHIZ		# TEST I=26
			9DW26D
			9DWAAC
		GOTO
			9DWNEXEP
9DWAAC		SSP			# I=2
			9DWI
			4
9DWNEXEP	CALL
			ROWDOT
		LXA,1	BDSU*		# (EP-ROWI*ROWJ)/W
			9DWP
			EMATRIX +40D,1
		DDV	INCR,1		# P=P+1
			WORKW
			2
		SXA,1	LXC,1
			9DWP
# Page 613
			9DWI
		XSU,1	BOV		# -(I+N)
			9DWN
			SETWIN=0
		GOTO
			9DWSETWX
SETWIN=0	DLOAD			# W(I+N)=0
			HI6ZEROS
9DWSETWX	STORE	W,1
		GOTO
			TST2I=0
9DWN=N+3	LXA,1	INCR,1		# N=N+3
			9DWN
			6
		SXA,1	SLOAD		# J=J-1
			9DWN
			9DWJ
		DSU
			9DWID
		STORE	9DWJ
		DSU	BHIZ		# TEST J=26
			9DW26D
			SETJ=2A
		GOTO
			9DWI=JA
SETJ=2A		SSP	GOTO		# J=2
			9DWJ
			4
			9DWI=JA
9DWEXITX	CALL
			GRP2PC
		AXT,1	SSP		# CLEAR W6,W7,W8 USED TEMP FOR EMATRIX
		DEC	54
			S1
			6
		VLOAD
			HI6ZEROS
9DWEXXXA	STORE	W +162D,1
		TIX,1	GOTO
			9DWEXXXA
			9DWXX
ROWDOT		SSP	BOV
			XTMP1
		OCT	377
			+1
		LXC,1	LXC,2
			9DWI
			9DWJ
		DLOAD	PUSH
			HI6ZEROS
# Page 614
ROWDOT1		DLOAD*	DMPR*
			W,1
			W,2
		DAD	PUSH
		BOV	INCR,1
			ROWDOT3
		DEC	-6
		INCR,2	SLOAD
		DEC	-6
			XTMP1
		BHIZ	SR1
			ROWDOT2
		STORE	XTMP1
		GOTO
			ROWDOT1
ROWDOT2		DLOAD
		RVQ
ROWDOT3		CLRGO
			ORBWFLAG
			ROWDOT2
WORKW		=	0D
XTMP1		=	6D
9DWP		=	8D		# P
9DWI		=	10D		# I
9DWN		=	12D		# N
9DWJ		=	14D		# J
9DWXX		=	S22UOFF
S22UMRL		=	BVECTOR		# 18
S22UUT		=	DELTAX		# 18
S223X1		=	18D		# 6
S221X3		=	24D		# 6
S22D		=	30D		# 2
S22RHO		=	32D		# 2
S22RL		=	W +156D		# 6
9DW26D		2DEC	52 B-14

9DWID		2DEC	2 B-14

SCTVAR		2DEC	1.0 E-6 B+18

IMUVARR		2DEC	0.04 E-6 B+18

DEC1B2		2DEC	1 B-2

V06N49EE	VN	00649
V06N89B		VN	00689
S22UOFF		=	LEMPOS		# 6	U SUB OFF
		SETLOC	P20S2
		BANK
# Page 615
# Nothing on this page. --- RSB 2009.

# Page 616
# SUBROUTINE NAME:  V89CALL
# MOD NO:  0					DATE: 8 FEB 1968
# MOD BY:  DIGITAL DEVEL GROUP			LOG SECTION:  P20-P25
#
# FUNCTIONAL DESCRIPTION:
#
# CALLED BY VERB 89 ENTER DURING P00.  PRIO 10 USED.  CALCULATES AND
# DISPLAYS FINAL GIMBAL ANGLES TO POINT CSM +X AXIS OR PREFERRED AXIS
# (UNIT(Z)COS55 DEG + UNIT(X)SIN55 DEG) AT LM.
#
# 1. KEY IN V89 E ONLY IF IN PROG 00. IF NOT IN P00, OPERATOR ERROR AND
# EXIT R63, OTHERWISE CONTINUE.
#
# 2. IF IN P00, DO IMU STATUS CHECK (R02BOTH).  IF IMU ON AND ITS
# ORIENTATION KNOWN TO CGC, CONTINUE.
#
# 3. FLASH DISPLAY V 04 N 06.  R2 INDICATES WHICH SPACECRAFT AXIS IS TO
# BE POINTED AT LM.  INITIAL CHOICE IS PREFERRED AXIS.  (R2=1).
# ASTRONAUT CAN CHANGE TO (+X) AXIS (R2 NOT= 1) BY V22 E 2 E.  CONTINUE
# AFTER KEYING IN PROCEED.
#
# 4. SET PREFERRED ATTITUDE FLAG ACCORDING TO OPTION DESIRED.  SET FLAG
# FOR PREFERRED AXIS.  RESET FLAG FOR X AXIS.
#
# 5. CURRENT TIME IS STORED AND R63COMP IS CALLED
#
#	R63COMP JOB:
#
#		UPDATE CSM AND LM STATE VECTORS USING CONIC EQUATIONS
#
#		CALCULATES BOTH PREFERRED AND X AXIS TRACKING ATT FROM CSM TO LM.
#
#		DESIRED GIMBAL ANGLES AS INDICATED BY PREFERRED ATTITUDE FLAG
#		ARE STORED FOR LATER R60CSM CALL.
#
# 6. FLASH DISPLAY V 06 N18 AND AWAIT RESPONSE.
#
# 7. RECYCLE: RETURN TO STEP 5.
#    TERMINATE: EXIT R63 ROUTINE
#    PROCEED:  RESET 3AXISFLG AND CALL R60CSM FOR ATTITUDE MANEUVER.
#
# CALLING SEQUENCE:	V 89 E
#
# SUBROUTINES CALLED:  CHKPOOH, R02BOTH, GOXDSPF, R63COMP, R60CSM
#
# ALARMS	1.  OPERATOR ERROR IF NOT IN P00
#		2.  PROGRAM ALARM IF IMU IS OFF
#		3.  PROGRAM ALARM IF IMU ORIENTATION IS UNKNOWN
# Page 617
#
# ERASABLE INITIALIZATION REQUIRED:  NONE
#
# DEBRIS:  OPTION1, OPTION1+1, PRFTEXAT(PREF ATT FLAG), P21TIME, 3AXISFLG

DP1MIN		2DEC	6000

		EBANK=	P21TIME
		BANK	34
		SETLOC	P20S4
		BANK
		COUNT*	$$/R63

V89CALL		TC	BANKCALL	# IMU STATUS CHECK. RETURNS IF ORIENTATION
		CADR	R02BOTH		# KNOWN.  ALARMS IF NOT.
		CAF	THREE		# ALLOW ASTRONAUT TO SELECT DESIRED
		TS	OPTION1		# TRACKING ATTITUDE AXIS.
		CAF	ONE
		TS	OPTION1 +1
		CAF	VB04N06		# V 04 N 06
		TC	BANKCALL
		CADR	GOFLASH
		TC	ENDEXT		# TERMINATE
		TC	+2		# PROCEED
		TC	-5		# DATA IN.  OPTION1 +1 = 1 FOR PREF AXIS
					#		       = 2 FOR X AXIS
		CS	OPTION1 +1	# 1 FOR PREF AXIS.  2 FOR X AXIS.
		AD	ONE
		EXTEND
		BZF	SETPAF
RSTPAF		TC	DOWNFLAG	# RESET PREF ATT FLAG FOR R63COMP
		ADRES	RNGSCFLG	# TO DO X AXIS.  RESET BIT 10 FLAG 5
V89RECL		TC	INTPRET
		RTB	DAD
			LOADTIME	# READ PRESENT TIME
			DP1MIN		# INTEGRATE TO 1 MIN FROM NOW
		STCALL	P21TIME		# STORE TIME FOR CALL TO R63COMP.  R63COMP
			R63COMP		# LEAVES DESIRED GIM ANGS IN THETAD, LOS IN
		EXIT			# POINTVSM, AND SELECTED AXIS IN SCAXIS.
		CAF	VB06N18		# V 06 N 18
		TC	BANKCALL	# NOUN 18 REFERS TO THE DESIRED GIMBAL
		CADR	GOFLASH
		TC	ENDEXT		# TERMINATE
		TC	+2		# PROCEED
		TC	V89RECL		# RECYCLE
		TC	DOWNFLAG	# RESET 3 AXIS FLAG
		ADRES	3AXISFLG	# RESET BIT 6 FLAG 5
# Page 618
		TC	BANKCALL	# PERFORMS CSM MANEUVER TO ALIGN SELECTED
		CADR	R60CSM		# SPACECRAFT AXIS TO LOS.
		TCF	ENDEXT

SETPAF		TC	UPFLAG		# SET PREFERRED ATT FLAG FOR R63COMP
		ADRES	RNGSCFLG	# TO DO PREF AXIS.  SET BIT 10 FLAG 5.
		TC	V89RECL

VB04N06		VN	0406
VB06N18		VN	0618

R63COMP		EQUALS	R63

# Page 619
# PROGRAM NAME:  P23 CISLUNAR MIDCOURSE NAVIGATION
# MOD NO:
# MOD BY:  TOM KNATT
#
# FUNCTIONAL DESCRIPTION:  DO MIDCOURSE NAVIGATION BY INCORPORATION OF
# STAR/EARTH AND STAR/MOON OPTICAL MEASUREMENTS.
#
# CALLING SEQUENCE:  ASTRONAUT OPERATED
#
# SUBROUTINES CALLED:  R52,R53,R57,R60,ORBITAL INTEGRATION (INTEGRV)
# INCORP1,INCORP2,LALOTORV,LUNLMKLD, AND DISPLAY INTERFACE ROuTINES.
#
# N0RMAL EXIT MODES:  VIA R00
#
# ALARMS:  NONE
#
# ABORT MODES:  NONE
#
# ERASABLE INITIALIZATION REQUIRED:  PAD-LOADED ERASABLES, ORBWFLAG RESET,
# REFSMFLG=0 IF IMU OFF AND REFSMFLG=1 IF IMU ONE
#
# INPUTS BY USER REQUIRED:  STAR NUMBER, LANDMARK LAT, LONG/2, ALT OR ID NUMB.
# IF LANDMARK IS USED, NEAR OR FAR HORIzON IF HORIZON IS USED, AND
# BODY TO BE MARKED ON (EARTH OR MOON).  SEE GSOP CHAPT 4.
#
# OUTPUT:  UPDATED CMC STATE VECTOR.  VECTOR FROM S/C TO HORIZON OR LANDMARK
# IN POINTAXS.  POINTAXS CAN BE USED TO GENERATE THIS VECTOR APART FROM
# P23 IF DESIRED.
#
# DEBRIS:  NO USABLE DEBRIS IS GENERATED.  RENDWFLG IS RESET FOR P20 UPON
# COMPLETION OF P23.  RUPTREGS AND ERASABLES USED BY DISPLAYS ARE DEBRIS.

		BANK	31
		SETLOC	RT23
		BANK
		COUNT	31/S23
		EBANK=	W
P23		TC	DOWNFLAG
		ADRES	RNDVZFLG

		TC	2PHSCHNG
		OCT	00004		# LEAVE GROUP 4
		OCT	00012		# ENTER GROUP 2
		CAF	PRIO13
		TS	PHSPRDT2
		TC	INTPRET
		SSP	CLEAR
			MARKINDX
			1
			TARG2FLG	# TARGET FLAG USED R52 AND R53
		CLEAR	SSP
			TARG1FLG
			STARIND
			0
		SSP	CLEAR
			BESTI
			0
			R57FLAG		# SET = DO NOT REPERFORM R57
		CLEAR	EXIT
			V94FLAG		# SET = ALLOW V94
P23.00		TC	INTPRET
# Page 620
		BON	CALL
			REFSMFLG	# SET NOW AS INPUT, NORMALLY EXTERNAL CONT
			P23.05		# WHEN ALIGNED, PERFORM MEASUREMENT
			R57		# DO OPTICS CALIBRATION IF IMU NOT ALIGNED.
		CALL
			R53
		GOTO
			P23.60
P23.05		CLEAR	EXIT
			SAVECFLG	# USED TO SAVE SPACE IN P23.65
P23.06		CAF	V05N70
		TC	BANKCALL	# IDENTIFICATION:  STAR, HOR  IDENT.
		CADR	GOFLASH
		TC	GOTOPOOH	# TERMINATE
		TC	P23.15
		TC	-5		# REDISPLAY
P23.15		CA	LANDMARK	# IF C=2, LUNAFLAG=1.  IF C=1, LUNAFLAG=0
		EXTEND
		BZF	P23.151
		CA	HORIZON
		EXTEND
		BZF	+2
		TC	R23.10		# OPERATOR DSKY ERROR
		CA	LANDMARK
		TC	P23.152
P23.151		CA	HORIZON
		EXTEND
		BZF	R23.10
P23.152		MASK	BITS7-9		# IS C EQUAL TO 1 OR 2
		AD	NEG100
		EXTEND
		BZF	P23.16
		AD	NEG100
		EXTEND
		BZF	+2
		TC	R23.10
		TC	UPFLAG
		ADRES	LUNAFLAG
		TCF	+3
P23.16		TC	DOWNFLAG
		ADRES	LUNAFLAG
		CA	STARCODE	# IS STARCODE GREATER THAN OR
		EXTEND			# EQUAL TO 0 AND LESS THAN 37
		BZF	P23.176
		EXTEND
		BZMF	R23.10
		AD	NEG37
		EXTEND
		BZMF	+2
		TC	R23.10
# Page 621

		TC	INTPRET
P23.17		SLOAD	BZE
			STARCODE
			P23.175
		PUSH
		SLOAD	DMP
			SPSIX
		LXA,1	SXA,1
			MPAC +1
			BESTI		# BESTI = 6 X STAR NUMBER
		CALL
			LOWMEMRY	# NEEDED TO RETRIEVE STAR VECTOR FROM LOW
		STORE	STARSAV2	# STORE FOR R53,P23.  US(IN P23)=STARSAV2
P23.175		EXIT
P23.176		CA	HORIZON
		EXTEND
		BZF	P23.20
		MASK	BITS4-6
		AD	-OCT10
		EXTEND
		BZF	P23.18
		AD	-OCT10
		EXTEND
		BZF	+2
		TC	R23.10
		TC	UPFLAG
		ADRES	NORFHOR
		TC	P23.30
P23.18		TC	DOWNFLAG
		ADRES	NORFHOR
		TC	P23.30
P23.20		TC	INTPRET
		CALL
			P22SUBRB
		EXIT
P23.30		TC	INTPRET
		SLOAD	BZE
			STARCODE
			LDPLANET
P23.31		BON	EXIT
			SAVECFLG
			P23.85
		CAF	V50N25P
		TC	BANKCALL
		CADR	GOPERF1		# GOPERF1 BLANKS OUT R2 AND R3
		TC	GOTOPOOH
		TC	V94ENTER	# PROCEED.  AUTOCONTROL CMC
P23.55		TC	INTPRET
		GOTO
			P23.56
# Page 622

# VERB 94 BEGINS HERE
V94ENTER	TC	INTPRET
		RTB
			LOADTIME	# READ CLOCK
		STCALL	MARKTIME
			POINTAXS	# RETURN LOS IN RCLL AND MPAC
		MXV	UNIT
			REFSMMAT
		STOVL	POINTVSM
			JCAXIS
		STORE	SCAXIS
		EXIT
		TC	DOWNFLAG	# CLEAR AND GO TO VECPOINT IN R60
		ADRES	3AXISFLG	# BIT 6 FLAG 5
		CAF	R60ADRS
		TS	TEMPFLSH
		TC	PHASCHNG
		OCT	00012
R60CALL		TC	BANKCALL
		CADR	R60CSM
		TC	PHASCHNG
		OCT	04022
		TC	INTPRET
		BON
			R57FLAG
			P23.57		# DO NOT REPERFORM R57
P23.56		CALL
			R57
P23.57		SET	SET
			V94FLAG
			R57FLAG
		CALL
			R52
		CLEAR	CLEAR
			V94FLAG
			R57FLAG
P23.60		EXIT
		INHINT
		CA	MARKSTAT
		MASK	LOW10
		TS	MARKDATA
		EXTEND
		INDEX	MARKDATA
		DCA	0
		DXCH	MARKTIME
		INDEX	MARKDATA
		CA	5
		XCH	TRUNION
		RELINT
		TC	INTPRET
# Page 623
		LXC,1	VLOAD*
			MARKDATA
			1,1
		STODL*	MARKDOWN +1
			0,1
		STORE	MARKDOWN
		EXIT
		CAF	V05N71
		TC	BANKCALL
		CADR	GOFLASH
		TC	GOTOPOOH	# TERMINATE
		TC	P23.65		# STORE DATA
		TC	-5		# REDISPLAY
P23.65		TC	INTPRET
		SET	EXIT
			SAVECFLG
		TC	P23.15
P23.85		CLEAR	CALL
			RENDWFLG
			POINTAXS
		GOTO
			R23.55

# WE BEGIN CALCULATIONS HERE
# POINTAXIS SUBROUTINE

POINTAXS	STQ
			POINTEX
R23.05		BON	DLOAD
			ORBWFLAG
			R23.1
			WMIDPOS
		STCALL	0
			INITIALW	# INITIALIZE W-MATRIX FIRST PASS IN P23
R23.1		CALL
			SETINTG		# SETUP FOR CSM INTEGRATION
		BOF	SET
			ORBWFLAG
			R23.2
			DIM0FLAG
R23.2		SET	CALL
			ORBWFLAG
			INTEGRV		# INTEGRATE CSM STATE VEC. TO MARKTIME
		EXIT
		TC	PHASCHNG
		OCT	04022
		TC	INTPRET
		CALL
			RECT.1		# PICKUP CSM STATE VECTOR FROM PERM
		BOFF
			ZMEASURE	# IN SPHERE OF INFLUENCE OF PRIMARY BODY
			R23.3
# Page 624
		DLOAD	CALL
			MARKTIME
			LUNPOS
		BON	VCOMP
			CMOONFLG
			+1
		VAD
			RZC
		STORE	RZC
R23.3		SLOAD	BHIZ
			LANDMARK	# IF LANDMARK = 0, USE HORIZ SUBR
			R23.4
		SET
			ERADFLAG
		DLOAD	CALL
			MARKTIME
			LALOTORV
		GOTO
			R23.5
R23.4		CALL
			HORIZ
R23.5		VSU	SETPD
			RZC
			0
		GOTO
			POINTEX
# Page 625
R23.55		UNIT	PUSH		# RCLL IS IN MPAC
		VLOAD
			34D		# RCLL * RCLL
		STOVL	30D		# PUSH 30-31 =RCLL*RCLL 32-33=ABVAL RCLL
			VZC
		VXSC	VSR
			ONE/C
			15D
		VAD			# PUSH UP RCLL(UNIT)
		UNIT
		STOVL	UCLSTAR
			VZC
		VSR2	VSU
			VESO
		VXSC	VSR
			ONE/C
			13D
		VAD	UNIT
			US
		STORE	USSTAR
		DOT	SL1
			UCLSTAR
		PUSH	VLOAD		# PD 0,1 = USSTAR(DOT)UCLSTAR
			UCLSTAR
		VXSC	VCOMP
		VSL1	VAD
			USSTAR
		UNIT
		STOVL	BVECTOR		# USSTAR - COSQ(UCLSTAR)
			ZEROVECS
		STORE	BVECTOR +6
		STODL	BVECTOR +12D
			0
		ACOS	DCOMP
		PUSH	DLOAD
			ZEROVECS
		EXIT
		CA	VARSUBL		# PUT FIXED INTO ERASABLE FOR MSU
		TS	L		# INSTRUCTION COMING UP
		CA	TRUNION		# REQUIRED TO CHANGE 2'S COMPLEMENT
		EXTEND			# TRUNION TO 1'S COMPLEMENT
		MSU	L		# TRUNION (2'S)-00000 CONVERTS TRUNION TO
		TS	MPAC		# 1'S.  VARSUBL=00000.
		TC	INTPRET
		PUSH	SLOAD		# PUSH IS DP.  WHEN BDSU IS EXECUTED, 2ND
			TRUNBIAS	# HALF OF PUSHLIST IS GUARANTEED ZERO FROM
		BDSU			# DLOAD ZEROVECS ABOVE
		SR3	DAD
		DAD	DMP
# Page 626
			TRUN19
			32D
		DMP	SL3
			PI/4.0
		BOFF	SL2
			CMOONFLG
			R23.51
R23.51		STODL	DELTAQ
			30D		# RCLL * RCLL
		DMP	RTB
			TRUNVAR
			TPMODE
		TAD
			VARSUBL
		STORE	VARIANCE
		CLEAR	CALL
			DMENFLG
			INCORP1
		CALL
			GRP2PC
		VLOAD	ABVAL
			DELTAX +6
		BOF	SR2		# DISPLAY IS 2-27 IF IN LUNAR SPHERE.
			CMOONFLG
			R23.52
R23.52		STOVL	N49DISP +2
			DELTAX
		ABVAL
		BOF	SR2
			CMOONFLG
			R23.53
R23.53		STORE	N49DISP
		EXIT
R23.6		CAF	V6N49
		TC	BANKCALL
		CADR	GOFLASHR
		TC	GOTOPOOH
		TC	R23.7		# INCORPORATE DATA
		TC	GOTOPOOH
		CAF	BIT3		# BLAN OUT R3
		TC	BLANKET
		TC	PHASCHNG
		OCT	00012
		TC	ENDOFJOB
R23.7		TC	INTPRET
R23.8		SET	CALL
			VEHUPFLG
			INCORP2
		EXIT
R23.END		TC	GOTOPOOH

# Page 627
R23.10		TC	FALTON
		TC	P23.06
HORIZ		STQ	SETPD
			SRRETURN
			0
		DLOAD	PDDL		# PUSH 0-1 = -AYO SCALED B0
			-AYO
			AXO
		PDDL	PDVL		# PUSH 2-3 = +AX SCALED B0
			DPPOSMAX
			US
		VXV	UNIT
			RZC
		STOVL	UBAR2
		VXV	UNIT		# PUSH UP
			UBAR2
		STOVL	UBAR0
			UBAR2
		VXV	UNIT
			UBAR0
		STORE	UBAR1
		BON	DOT
			LUNAFLAG
			HORIZ.6
			0		# UBAR1 DOT UZ
		STCALL	ALPHAV +4
			GETERAD
		DAD	PDDL		# MPAC HAS RADIUS OF FISCHER ELLIPSOID
			HORIZALT	# PUSH 0-1 = BH SCALED B29
			AEARTH
		DAD	PUSH		# PUSH 2-3 = AH B29
			HORIZALT
HORIZ.1		VLOAD	MXV
			RZC		# B29
			UBAR0		# B1
		VSL1	PDVL		# PUSH 4-9 = RH(XH,YH,ZH) B29
			US
		MXV	VSL1
			UBAR0
		PDDL			# PUSH 10-15 = USH B1
			2		# AH
		STODL	34D
			4		# XH
		CALL
			DIVIDE
		SR*	DMP
			8D,1		# NOW SCALED B9
			MPAC
		STODL	30D
			0
# Page 628
		STODL	34D
			6		# YH
		CALL
			DIVIDE
		SR*	DMP
			8D,1		# B9
			MPAC		# B18
		DAD	PUSH		# PUSH 16-17 =A SCALED B18
			30D
		DSU	SQRT
			1.0B18
		PDDL			# PUSH 18-19 SQRT(A-1) B9
			16D
		STODL	34D
			4		# XH
		CALL
			DIVIDE
		SR*	PDDL
			17D,1		# PUSH 20-21 = XH/A B29
			6		# YH
		CALL
			DIVIDE
		SR*	PDDL
			17D,1		# PUSH 22-23 = YH/A B29
			16D		# A
		STODL	34D
			18D		# SQRT(A-1)
		CALL
			DIVIDE
		SR*
			8D,1
		STODL	28D
			0		# BH
		STODL	34D
			2		# AH
		CALL
			DIVIDE
		SR*	DMP		# AH/BH SCALED B1
			0,1
			28D		# SQRT(A-1)/A
		DMP	SL1
			6		# YH
		PDDL
			2		# AH
		STODL	34D
			0
		CALL
			DIVIDE
		SR*	DMP		# BH/AH SCALED B1
			0,1
# Page 629
			28D		# SQRT (A-1)/A
		DMP	SL1
			4		# XH
		PDDL	DAD
			20D		# XH/A
			24D		# ALPHA
		PDDL	DSU
			22D		# YH/A
			26D		# BETA
		PUSH	SETPD
			16D
		DLOAD	DSU
			20D		# XH/A
			24D		# ALPHA
		PDDL	DAD
			22D		# YH/A
			26D		# BETA
		PDDL	PUSH
			ZEROVECS
		STOVL	32D		# ZERO THIRD COMP. OF T-0 VECTOR
			28D
		VSU	UNIT
			4		# RH VECTOR
		DOT	PDVL		# PUSH 22-23 A-SUB-ZERO
			10D		# USH VECTOR
			16D		# T1 VECTOR
		VSU	UNIT
			4		# RH VECTOR
		DOT	PUSH		# PUSH 24-25 A-SUB-ONE
			10D
		BDSU	BMN
			22D		# A-SUB-ZERO
			HORIZ.3
		BON
			NORFHOR
			HORIZ.4
HORIZ.2		VLOAD	GOTO
			28D		# T-0 VECTOR
			HORIZ.5
HORIZ.3		BON	GOTO
			NORFHOR
			HORIZ.2
			HORIZ.4
HORIZ.4		VLOAD
			16D		# T1 VECTOR
HORIZ.5		VXM	VSL1
			UBAR0
		GOTO
			SRRETURN
HORIZ.6		DLOAD	PUSH
# Page 630
			RADMOON
		PUSH	GOTO
			HORIZ.1
DIVIDE		NORM	SR1
			X1
		STODL	36D
			34D
		NORM	BDDV
			S1
			36D
		XSU,1	RVQ
			S1
RECT.1		BOFF	AXT,2		# SR TO SET ZMEASURE = 0 IF MEASUREMENT
			CMOONFLG	#   PLANET AND PRIMARY PLANET ARE THE SAME.
			RECT.3		#     OTHERWISE = 1
		DEC	-2
		BOFF			#       VEC. AND SCALE B29 AND B7
			LUNAFLAG
			RECT.4
RECT.2		CLEAR	GOTO
			ZMEASURE
			RECT.5
RECT.3		AXT,2	BOFF
			0
			LUNAFLAG
			RECT.2
RECT.4		SET
			ZMEASURE
RECT.5		VLOAD	VSR7
			DELTACSM	# SCALED B22 OR B18
		VSR*	VAD
			0,2
			RCVCSM		# SCALED B29 OR B27
		VSR*
			0,2
		STOVL	RZC		# NOW SCALED B29
			NUVCSM		# SCALED B3 OR B-1
		VSR4	VSR*
			0,2
		VAD	VSR*
			VCVCSM		# SCALED B7 OR B5
			0,2
		STORE	VZC		# NOW SCALED B7
		RVQ
ONE/C		2DEC*	.333564049 E-6 B+21*

AEARTH		2DEC	6378166 B-29	# A AXIS OF EARTH (METERS B-29)

RADMOON		2DEC	1738090 B-29	# RADIUS MOON IN METERS

# Page 631

TRUN19		OCT	01604
TRUN19A		OCT	00000
1.0B18		2DEC	1.0 B-18

VARSUBL		DEC	0
VARSUBL3	2DEC*	3.4299040 E+6 B-26*

TRUNVAR		2DEC	2.5 E-9 B+18

V6N49		VN	0649
V05N70		VN	0570
V05N71		VN	0571
OCT00077	OCT	00077
V50N25P		OCT	00202
SPSIX		OCT	00006
JCAXIS		2DEC	.**********	# 1/2(SIN 32.523 DEG)  TRACK AXIS

		2DEC	0

		2DEC	.**********	# 1/2(COS 32.523 DEG)

R60ADRS		CADR	R60CALL +3
NEG37		DEC	-37
BITS7-9		OCT	700
BITS4-6		OCT	70
		SETLOC	RT53
		BANK
LOWMEMRY	VLOAD*	RVQ
			CATLOG,1
		BANK	37
		SETLOC	P23S1
		BANK
LDPLANET	EXIT			# KEEP THIS OPEN SUBROUTINE IN EBANK=5
		CAF	VNPLAN23	# BECAUSE STAR IS EBANK=5
		TC	BANKCALL	# LDPLANET ALLOWS VECTOR TO PLANET TO BE
		CADR	GOFLASH		# STORED IN STARSAV2 IF STORED STARS ARE
		TC	GOTOPOOH	# NOT VISIBLE
		TC	+2
		TC	-5
		TC	INTPRET
		VLOAD
			STARSAV3
		VXSC	UNIT
			1/SQR3
		STORE	STARSAV2
		GOTO
			P23.31
VNPLAN23	VN	0688
		BLOCK	02
GOTOV56		EXTEND			# P20 TERMINATES BY GOTOV56 INSTEAD OF
# Page 632
		DCA	VB56CADR	#	GOTOPOOH
		TCF	SUPDXCHZ
		EBANK=	WHOCARES
VB56CADR	2CADR	TRACKTRM

		SETLOC	FFTAG2
		BANK
		COUNT*	$$/P20
		BANK	40
		SETLOC	ENDPINS1
		BANK
		COUNT*	$$/EXTVB
V67CALL		TC	INTPRET
		CALL
			V67WW
		EXIT
V06N99DS	CAF	V06N99A
		TC	BANKCALL
		CADR	GOXDSPF
		TCF	ENDEXT
		TC	V06N9933
		TC	V06N99DS
V06N9933	TC	INTPRET
		SLOAD	BHIZ		# IF R3 OF V67 = 0 EXIT
			WWOPT
			+3
		GOTO
			V6N99INP
		EXIT
		TCF	ENDEXT
V6N99INP	LXA,1	LXA,2
			WWPOS
			WWVEL
		SLOAD	DSU
			WWOPT
			V67DEC2
		BHIZ	BPL
			V67WORB
			V67WMID
		SXA,1	SXA,2
			WRENDPOS
			WRENDVEL
		GOTO
			V67EXITX
V67WORB		SXA,1	SXA,2
			WORBPOS
			WORBVEL
		GOTO
			V67EXITX
V67WMID		SXA,1	SXA,2
# Page 633
			WMIDPOS
			WMIDVEL
V67EXITX	CLEAR	CLEAR
			ORBWFLAG
			RENDWFLG
		EXIT
		TCF	ENDEXT
V67WW		STQ	BOV
			S2
			+1
		CALL
			INTSTALL
		SSP	DLOAD
			S1
		DEC	6
			ZEROVECS
		STORE	WWPOS
		STORE	WWVEL
		STORE	WWOPT
		AXT,1
		DEC	36
NXPOSVEL	VLOAD*	VSQ
			W +36D,1
		DAD
			WWPOS
		STORE	WWPOS
		VLOAD*	VSQ
			W +90D,1
		DAD
			WWVEL
		STORE	WWVEL
		TIX,1	SQRT
			NXPOSVEL
		STODL	WWVEL
			WWPOS
		SQRT
		STORE	WWPOS
		BOV	GOTO
			+2
			V67XXX
		DLOAD
			DPPOSMAX
		STORE	WWPOS
		STORE	WWVEL
V67XXX		DLOAD	DSU
			WWPOS
			FT99999
		BMN	DLOAD
			+3
			FT99999
# Page 634
		STORE	WWPOS
		LXA,1	SXA,1
			S2
			QPRET
		EXIT
		TC	POSTJUMP
		CADR	INTWAKE
WWPOS		=	RANGE
WWVEL		=	RRATE
WWOPT		=	RTHETA
V06N99A		VN	0699
FT99999		2DEC	30479 B-19

V67DEC2		2DEC	2 B-14

		SBANK=	LOWSUPER


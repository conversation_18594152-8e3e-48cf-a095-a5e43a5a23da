# Copyright:	Public domain.
# Filename:	P40-P47.agc
# Purpose:	Part of the source code for Colossus 2A, AKA Comanche 055.
#		It is part of the source code for the Command Module's (CM)
#		Apollo Guidance Computer (AGC), for Apollo 11.
# Assembler:	yaYUL
# Contact:	<PERSON> <<EMAIL>>.
# Website:	www.ibiblio.org/apollo.
# Pages:	684-736
# Mod history:	2009-05-11 RSB	Adapted from the Colossus249/ file
#				of the same name, using Comanche055 page
#				images.
#		2009-05-20 RSB	In S20.1, a DMP DDV was corrected to DMPR DDV.
#		2009-05-22 RSB	In BESTTRIM, TC PACTOFF corrected to
#				TS PACTOFF.
#		2009-05-23 RSB	Prior to the 2CADR at T5IDLDAP, added an
#				SBANK.
#
# This source code has been transcribed or otherwise adapted from digitized
# images of a hardcopy from the MIT Museum.  The digitization was performed
# by <PERSON>, and arranged for by <PERSON> of the Museum.  Many
# thanks to both.  The images (with suitable reduction in storage size and
# consequent reduction in image quality as well) are available online at
# www.ibiblio.org/apollo.  If for some reason you find that the images are
# illegible, contact <NAME_EMAIL> about getting access to the
# (much) higher-quality images which <PERSON> actually created.
#
# Notations on the hardcopy document read, in part:
#
#	Assemble revision 055 of AGC program Comanche by NASA
#	2021113-051.  10:28 APR. 1, 1969
#
#	This AGC program shall also be referred to as
#			Colossus 2A

# Page 684
# PROGRAM DESCRIPTION ** P40CSM **

		EBANK=	DAPDATR1
		BANK	31
		SETLOC	P40S
		BANK

		COUNT	24/P40

P40CSM		TC	DOWNFLAG
		ADRES	ENG2FLAG

		TC	INTPRET
		SLOAD	BOFF
			ECSTEER		# IS THIS AN EXTERNAL DELTA V BURN
			XDELVFLG
			P40S/C		# NO	CSTEER = ECSTEER
		DLOAD			# YES	CSTEER = ZERO
			HI6ZEROS
P40S/C		STODL	CSTEER
			FENG		# SET UP THRUST FOR P40 20,000 LBS
P40S/F		STODL	F		# P41 ENTERS HERE
			TIG		# ORIGINAL TIG MAY BE SLIPPED BY P40S/SV
		STORE	NOMTIG		# SET ORIGINAL TIME OF IGNITION FOR S40.9

		EXIT
		TC	BANKCALL
		CADR	R02BOTH		# IMU STATUS CHECK

P40PVA		TC	INTPRET
		CALL
			S40.1		# COMPUTE VGTIG,UT
		CALL
			S40.2,3		# COMPUTE PREFERRED ATTITUDE
		SET	EXIT
			PFRATFLG
P40SXTY		TCR	SETMINDB -1	# NARROW DEADBAND FOR MANEUVER (EBANK6)
		RELINT
		TC	BANKCALL
		CADR	R60CSM		# ATTITUDE MANEUVER
		CS	ONE		# FOR UPDATEVG
		TS	NBRCYCLS
		TC	UPFLAG
		ADRES	TIMRFLAG	# ALLOW CLOCKTASK

		TC	P41/P40
		TC	P41/DSP		# P41

P40TTOG		CAF	V06N40		# INITIALIZE FOR CLOCKTASK WHICH IS CALLED
# Page 685
		TS	NVWORD1		# BELOW

		TC	INTPRET
		VLOAD	ABVAL		# FOR R2
			VGTIG
		STODL	VGDISP
			HI6ZEROS
		STORE	DVTOTAL
		EXIT

		EXTEND
		DCA	STEERADS	# SET FOR UPDATEVG AND TEST FOR STEERING
		DXCH	AVEGEXIT	# AFTER AVERAGE G

P40GMB		CAF	P40CKLS2	# (4.1 PROTECTION)
		TC	BANKCALL
		CADR	GOPERF1
		TCF	POST41		# V34
		TCF	TST,TRIM	# V33
TRIMONLY	CS	BIT1		# SET MRKRTEMP FOR GIMBAL TRIM (-1)
	+1	TS	MRKRTMP		# ENTRY FROM TST,TRIM

		CAF	ZERO		# SET CNTR	+0 FOR RESTART LOGIC IN S40.6
		TS	CNTR		#	+0 SAYS NORMAL ENTRY
					#	+1 (PRE40.6) SAYS RESTART ENTRY

		CAF	ONE
		TC	WAITLIST
		EBANK=	DAPDATR1
		2CADR	S40.6

		CCS	MRKRTMP		# TEST TO FIND TIME TO WAIT FOR GIMBAL TEST
		CAF	18SEC		# PLUS, DELAY FOR 18 SECONDS
		TCF	+2		# HOLE
		CAF	5SEC		# DELAY FOR TRIM ONLY TASK
		TC	BANKCALL
		CADR	DELAYJOB
		TC	2PHSCHNG
		OCT	40026		# 6.2 = PRE40.6(-0CS), CLOKTASK(100CS)
		OCT	00234		# 4.23 = P40S/SV (PRIO12)
P40S/RS		CAF	ONE
		TC	WAITLIST	# P41/SDP
		EBANK=	TIG
		2CADR	CLOKTASK

		RELINT

P40S/SV		TCR	E7SETTER	# JOB, 4.23 PRETECTS, PREO12
		EBANK=	TIG
# Page 686
		TC	INTPRET
		DLOAD	DSU
			TIG
			SEC29.96
		STORE	TDEC1

		CALRB			# RETURN IN BASIC
			MIDTOAV1
		TCF	+2
		TC	P40SNEWM	# INTEGRATION TIME GREATER THAN ALLOWED
P40SET		EXTEND
		DCA	MPAC		# DELTA TIME TO PREREAD (INT.INIT.)
		DXCH	P40TMP
		EXTEND
		DCS	5SECDP		# FOR TIGBLNK
		DAS	P40TMP
		EXTEND
		DCA	P40TMP
		TC	LONGCALL
		EBANK=	TIG
		2CADR	TIGBLNK

		TC	PHASCHNG
		OCT	20214		# 4.21 = TIGBLNK (P40TMP CS)

		TCF	ENDOFJOB
P40BLNKR	TC	BANKCALL
		CADR	CLEANDSP	# REMOVE RESIDUE
		TCF	ENDOFJOB
		EBANK=	TIG
P40SNEWM	EXTEND
		DCA	PIPTIME1
		DXCH	TIG		# SET NEW TIG FOR 06 40
		EXTEND
		DCA	SEC29.96
		DAS	TIG
		TCF	P40SET		# FOR LONGCALL OF TIG-30 (OR -35)

		EBANK=	DAPDATR1
POSTBURN	CAF	V16N40
		TC	BANKCALL
		CADR	REFLASH
		TCF	POST41		# V34 GO FINISH
		TCF	P40RCS		# PROCEED
		TCF	POSTBURN	# RECYCLE
P40RCS		EXTEND			# V99N40 ENTERS HERE ON A P40 BYPASS SPS
		DCA	ACADN85
		DXCH	AVEGEXIT
		CAF	2SECS		# WAIT FOR CALCN85 VIA AVEGEXIT
		TC	BANKCALL
# Page 687
		CADR	DELAYJOB

P40MINDB	TCR	SETMINDB -1
		RELINT
TIGNOW		TC	PHASCHNG
		OCT	05024		# TYPE C GROUP 4 BELOW FOR NOUN 85
		OCT	20000		# PRIO 20
		CAF	V16N85B
		TC	BANKCALL
		CADR	REFLASH
		TCF	POST41		# FINISH P40/P41
		TCF	POST41		# V03 PROCEED WITH REST OF THE CLEAN-UP
		TCF	TIGNOW		# V32 NOT GSOP RESPONSE BUT REDISPLAY N85

POST41		EXTEND
		DCA	SERVCADR
		DXCH	AVEGEXIT
		TCF	GOTOPOOH

MINDB		DEC	46
MAXDB		DEC	455
		EBANK=	DAPDATR1
	-1	INHINT
SETMINDB	CA	CDUX		# ROUTINE FOR SETTING
		TS	THETADX		# THE MINIMUM DEADBAND
		EXTEND			# IN AUTOPILOT
		DCA	CDUY
		DXCH	THETADY
		CA	MINDB		# SHOULD BE CALLED UNDER
		TS	ADB		# INTERRUPT INHIBITED
		CS	BIT4		# EBANK = E6
		MASK	DAPDATR1
		TS	DAPDATR1
		TC	Q

		EBANK=	DAPDATR1
	-1	INHINT
SETMAXDB	CA	MAXDB		# ROUTINE FOR SETTING
		TS	ADB		# THE MAXIMUM DEADBAND IN AUTOPILOT
		CS	DAPDATR1
		MASK	BIT4		# SHOULD BE CALLED UNDER
		ADS	DAPDATR1	# INTERRUPT INHIBITED
		TC	Q		# EBANK = E6

# Page 688
# PROGRAM DESCRIPTION ** P41CSM **

		SETLOC	P40S2
		BANK

		EBANK=	DAPDATR1
		COUNT	24/P41

P41CSM		TC	UPFLAG
		ADRES	ENG2FLAG	# SET FOR RCS

		TC	INTPRET
		DLOAD
			HI6ZEROS	# FOR P41 CSTEER =0
		STORE	CSTEER

		DLOAD	BON
			FRCS2		# 2JET THRUST FOR S40.1
			NJETSFLG
			P40S/F		# NJETS = 1 2-JET
		DAD	GOTO		# NJETS = 0 4-JET
			FRCS2
			P40S/F

		SETLOC	P40S
		BANK

P41/P40		CS	MODREG
		MASK	ONE		# P41EXITS AT CALL LOC +1
		EXTEND
		BZF	+2		# P41
		INCR	Q		# P40 EXITS AT CALL LOC +2
		TC	Q

TTG/0		CAF	PRIO20		# TASK (4.4 PROTECTS IN P41)
		TC	NOVAC
		EBANK=	DAPDATR1
		2CADR	TIGNOW

P40CLK		TC	DOWNFLAG
		ADRES	TIMRFLAG

		TCF	TASKOVER

P41/DSP		CAF	V06N85B		# SET UP FOR NONFLASH V 06 N85 BY CLOCKJOB
		TS	NVWORD1

		TC	INTPRET
# Page 689
		CALL			# COMPUTE
			P40CNV85	#	VGTIG IN CTRL COORDS
		EXIT
		EXTEND			# DO CONTROL COORD CALCULATION AFTER AVEG
		DCA	ACADN85
		DXCH	AVEGEXIT
		TC	2PHSCHNG
		OCT	40036		# 6.3=CLOKTASK(100CS)
		OCT	234		# 4.23=P40S/SV(PRIO12)

		TCF	P40S/RS
P41REDSP	CAF	V16N85B		# ENTER FROM P41 SIDE OF TIGAVEG
		TS	NVWORD1		# REDISPLAY NONFLASHING
		CAF	SEC29.96 +1
		TC	WAITLIST
		EBANK=	DAPDATR1
		2CADR	TTG/0

		CS	BIT3
		TCF	TTGPHS
P40CNV85	STQ	SETPD
			QTEMP1
			0
		VLOAD	PUSH
			VGPREV		# EQUALS VGTIG (TARGETTING INPUT)
		CALL
			S41.1
		STCALL	VGBODY
			QTEMP1

		EBANK=	DAPDATR1
CALCN85		TC	INTPRET
		CALL
			UPDATEVG	# NEW VG, S40.8 (+MAYBE S40.9)
		CALL
			P40CNV85	# COMPUTE VGBODY
		EXIT
		TC	SERVXT
FENG		2DEC	9.1188544 B-7	# SPS THRUST (20500LBS), SC.AT B+7 NEWT/E4

FRCS2		2DEC	.********* B-7	# RCS ULLAGE (199.6COS10 LBS), SC.AT

					#	B+7 NEWTONS/E+4
SEC24.96	DEC	2496
SEC29.96	2DEC	2996

18SEC		DEC	1800
P40CKLS2	OCT	204
40CST5		OCT	37730		# 40 CS FOR THE T5 CLOCK
OCT12		=	TEN
# Page 690
V1683		VN	1683
V06N85B		VN	0685
V16N85B		VN	1685
V06N40		VN	0640
V16N40		VN	1640
OCT27/24	OCT	27
OCT53		OCT	53
OCT35		OCT	35
		EBANK=	DAPDATR1
T5IDL24		2CADR	T5IDLOC

3MDOT		DEC	86.6175796 B-16	# 3SEC MASS LOSS (63.8 LBS/SEC), SC.AT
					# B+16 KB/SEC (NOT, EMDOT IS PAD-LOADED,
					# BUT 3MDOT IS NOT A CRITICAL QUANTITY, SO
					# IT CAN REMAIN IN FIXED MEMORY)
TST,TRIM	CAF	BIT1		# SET UP FOR GIMB DRIVE TEST AND TRIM (+1)
		TCF	TRIMONLY +1
TIGBLNK		CAF	5SEC		# CALL TIGAVEG IN FIVE SEC AT TIG-30
		TC	WAITLIST
		EBANK=	TIG
		2CADR	TIGAVEG

		CAF	ZERO		# DISABLE HERE, NOT IN P40BLNKR
		TS	NVWORD1

		CAF	PRIO14
		TC	NOVAC
		EBANK=	TIG
		2CADR	P40BLNKR	# DON'T PROTECT -- RESTARTS BLANK DSKY

		CS	OCT37		# 4.37 = TIGAVEG (500CS)
P40TSK		TC	NEWPHASE
		OCT	4
		TC	TASKOVER

		EBANK=	TIG
ACADN83		2CADR	CALCN83

		EBANK=	TIG
SERVCADR	2CADR	SERVEXIT

		EBANK=	DAPDATR1
ACADN85		2CADR	CALCN85

# Page 691
# PROGRAM DESCRIPTION ** P47CSM **

		COUNT	24/P47

		EBANK=	TIG
P47CSM		TC	BANKCALL	# IMU STATUS CHECK
		CADR	R02BOTH
		TC	INTPRET
		CALRB
			MIDTOAV2
		CA	MPAC +1		# DELTA TIME TO RPEREAD (LESS THAN 100
		TS	P40TMP		#	CS, WITH A TPAGREE, INT.INIT.)
		TC	WAITLIST
		EBANK=	TIG
		2CADR	TIGON		# TIGON IS REQUIRED TO MATHCHTAT AND AVEG

		TC	PHASCHNG
		OCT 	40574		# A, 4.57 = TIGON (P40TMP CS)
		TCF	ENDOFJOB

		EBANK=	P40TMP
TIGON		EXTEND
		DCA	ACADN83
		DXCH	AVEGEXIT
		CAF	PRIO30		# FORCE ZEROING OF N83 BEFORE SERVICER
		TC	NOVAC
		EBANK=	TIG
		2CADR	P47BODY

		CS	BIT2		# 4.2 = PRECHECK (-0CS), P47BODY (PRIO30)
		TCF	TTGPHS

		EBANK=	TIG
CALCN83		TC	INTPRET
		SETPD			# SET UP PUSHLIST FOR S41.1
			0
		VLOAD	VAD
			DELVCTL
			DELVREF
		STORE	DV47TEMP	# FOR COPYCYCLE BELOW
		PUSH	CALL
			S41.1
		STCALL	DELVIMU
			S11.1		# CALC. VI, H, HDOT FOR NOUN 62
		EXIT
		TC	PHASCHNG
		OCT	10035
# Page 692
		CAF	FIVE
		TC	GENTRAN
		ADRES	DV47TEMP
		ADRES	DELVCTL

		TC	SERVXT
P47BODY		TC	INTPRET
		VLOAD
			HI6ZEROS
		STORE	DELVIMU		# CLEAR DISPLAY AND ACCUMULATOR STORAGE
		STORE	DELVCTL		# UPON INITIATION OR ENTER RESPONSE
		EXIT
P47BOD		CAF	PRIO15		# LOWER PRIO THAN CALCN83 (20)
		TC	PRIOCHNG	#	TO PREVENT INTERRUPTION OF CALCN83
		TC	PHASCHNG
		OCT	05024		# TYPE C GROUP 4 BELOW FOR NOUN 83
		OCT	15000		# PRIO 15
P47/DSP		CAF	V1683
		TC	BANKCALL
		CADR	GOFLASH
		TC	GOTOPOOH
		TC	GOTOPOOH
		TCF	P47BODY		# RECYCLE -- CLEAR ACCUMULATED VELOCITY

# Page 693
# ROUTINE ** TIG-30 ** DESCRIPTION

		EBANK=	TIG
		COUNT	24/P40

TIGAVEG		TC	P41/P40		# TASK (4.37 PROTECTS)
		TCF	P41REDSP

		CAF	V06N40		# UNBLANK DISPLAY
		TS	NVWORD1

		CAF	SEC24.96
		TC	WAITLIST
		EBANK=	TIG
		2CADR	TIG-5

		CS	SIX		# 4.6 = TIG-5 (2496CS), PRECHECK (-0CS)
TTGPHS		TC	NEWPHASE	# ENTRY FROM P41REDSP (P41) WITH A=-4, OR
		OCT	4		#       FROM TIGON    (P47) WITH A=-1

PRECHECK	CCS	PHASE5		# HAS SERVICER BEEN RESTARTED
		TCF	TASKOVER	# YES, DON'T START ANOTHER ONE
		TC	POSTJUMP
		CADR	PREREAD

# Page 694
# ROUTINE ** TIG-5 ** DESCRIPTION

		EBANK=	TIG
TIG-5		CAF	5SEC
		TC	WAITLIST
		EBANK=	DAPDATR1
		2CADR	TIG-0

		CS	BIT9		# WILL CAUSE V99 FLASH
		TS	NVWORD1

		TC	2PHSCHNG
		OCT	40074		# A, 4.7 = TIG-0 (500CS)
		OCT	00033		# A, 3.3 = S40.13 (PRIO20)

		CAF	PRIO20
		TC	FINDVAC
		EBANK=	TGO
		2CADR	S40.13

		TCF	TASKOVER

# Page 695
# ROUTINES ** TIG-0 ** AND ** IGNITION ** DESCRIPTION

		EBANK=	DAPDATR1	# TASK, 4.7 PHASE, OR 4.77 (-0CS) IN R40
TIG-0		CS	FLAGWRD7	# SET IGN FLAG
		MASK	BIT13
		ADS	FLAGWRD7

		CAE	FLAGWRD7	# CHECK ASTN FLAG FOR V99 RESPONSE
		MASK	BIT12
		EXTEND
		BZF	TASKOVER	# WAIT FOR V99P

		CAF	V06N40		# CLEAR THE V99 (IN CASE OF A RESTART
		TS	NVWORD1		#	DURING THE V99 SEQUENCE)

		TC	PHASCHNG	# V99P HAS COME ALREADY, DO IGNITION NOW
		OCT	00614		# A, 4.61 = IGNITION (-0CS) TBASE OLD

IGNITION	CAE	CDUX		# SAVE FOR ROLL DAP REFERENCE OGAD
		TS	OGAD		#	V99PJOB (CLOCKJOB) SETS UP IGNITION
		EXTEND			# 	TASK (4.61 PROTECTION)
		DCA	TIME2		#	FOR RESTARTS
		DXCH	TEVENT
		CS	FLAGWRD5	# SET ENGONFLG
		MASK	BIT7
		ADS	FLAGWRD5
SPSON		CAF	BIT13		# TURN ON SPS ENGINE
		EXTEND
		WOR	DSALMOUT

IMPULCHK	CAF	BIT9		# CHECK FOR IMPULSIVE BURN
		MASK	FLAGWRD2
		CCS	A
		TCF	IMPLBURN	# IMPULSIVE
		CS	FLAGWRD6	# NON-IMPULSIVE, SET STRULLSW FOR STEERULL
		MASK	BIT13
		ADS	FLAGWRD6

PREPTVC		CS	OCT60000	# RESET T5 BITS
		MASK	FLAGWRD6
		TS	FLAGWRD6

		EXTEND			# KILL RCS
		DCA	T5IDL24
		DXCH	T5LOC

		CS	THREE		# 4.3 = DOTVCON (40CS)
		TC	NEWPHASE
		OCT	4

# Page 696
		TC	FIXDELAY
		DEC	40		# 0.4 SECOND DELAY FOR THRUST BUILDUP

DOTVCON		CS	BIT1		# SET TVCPHASE = TVCDAPON CALL (FRESHDAP)
		TS	TVCPHASE
		CAF	ZERO		# SET TVCEXECUTIVE PHASE
		TS	TVCEXPHS
		CS	OCT60000	# SET T5 BITS TO INDICATE TVC TAKEOVER ....
		MASK	FLAGWRD6	#	BITS 15,14 = 10
		AD	BIT15
		TS	FLAGWRD6

		CAF	THREE		# 6.3 = CLOKTASK (100CS), DROPPING PRE40.6
		TS	L		#	WHICH IS HANDLED NOW BY REDOTVC
		COM
		DXCH	-PHASE6

		EXTEND			# STORE RCS ATTITUDE ERRORS FOR USE IN
		DCS	ERRORY		# INITIALIZING TVC ATTITUDE ERRORS
		DXCH	ERRBTMP

		CS	FIVE		# 4.5 = DOSTRULL (160 CS)
		TC	NEWPHASE
		OCT	4

		CAF	POSMAX		# SET TIME5 FOR STARTING RIGHT AWAY
		TS	TIME5
		EXTEND
		DCA	TVCON2C		# (TVCDAPON)
		DXCH	T5LOC		# (KILLS RCS DAP)

		TC	FIXDELAY	# 0.4 + 1.6 = 2.0 SEC FOR ULLAGE-OFF AND
		DEC	160		# 	STEERING (IF NON-IMPULSIVE)

DOSTRULL	CAF	BIT13		# CHECK STRULLSW FOR IMPULSIVE BURN
		MASK	FLAGWRD6
		CCS	A
		TCR	STEERULL	# NON-IMPULSIVE, STEERING AND ULLAGE OFF
		TCR	ULAGEOFF	# ULLAGE OFF (ONLY, OR AGAIN)

		EXTEND
		DCA	NEG0		# KILL GROUP 4 (DP NEG0 = -0,+0)
		DXCH	-PHASE4

ENDIGN		TCF	TASKOVER

STEERULL	CS	FLAGWRD2	# SET STEERSW
		MASK	BIT11
		ADS	FLAGWRD2

# Page 697

ULAGEOFF	CAF	ZERO
		EXTEND
		WRITE	CHAN5		# ZERO CHANNEL 5
		TC	Q

IMPLBURN	CS	BIT13		# RESET STRULLSW (COULD BE AN IMPULSIVE
		MASK	FLAGWRD6	#	ENGINE FAIL)
		TS	FLAGWRD6

		TCR	E7SETTER

		EBANK=	TIG
		EXTEND			# PREPARE FOR R1 OF V06N40 (CLOCKTASK)
		DCA	TGO
		DXCH	TIG
		EXTEND
		DCA	TIME2
		DAS	TIG

		TC	2PHSCHNG
		OCT	40153		# A, 3.15 = ENGINOFF (TGO+1) .... NOT GROUP
		OCT	07014		# C, DELTAT NEXT, TASK BELOW, IN
		DEC	-0		# -0 CS
		EBANK=	DAPDATR1
		2CADR	IMPLCONT

		CAE	TGO +1		# (TPAGREE IN S40.13, LESS THAN 600CS)
		TC	WAITLIST
		EBANK=	TGO
		2CADR	ENGINOFF

IMPLCONT	CS	BIT9		# RESET IMPULSW, ENGINOFF IS NOW SET UP
		MASK	FLAGWRD2
		TS	FLAGWRD2

		TCR	E6SETTER
		EBANK=	DAPDATR1

		CAF	ZERO		# SET UP V97VCNTR IN CASE ENGINOFF (MASS-=
		TS	V97VCNTR	#	BACK) ARRIVES BEFORE TVCDAPON

		TCF	PREPTVC

		EBANK=	TGO		# E7 FORCED BY 3.15SPOT VARIABLE DELTA-T
ENGINOFF	TCR	E6SETTER	# TASK, 3.15 PHASE (TGO+1 CS)	GET E6
		EBANK=	DAPDATR1
		CAE	CSMMASS
		TS	MASSTMP		# COPYCYCLE FOR MASSBACK
# Page 698
		TC	2PHSCHNG
		OCT	00003		# KILL GROUP 3 PROTECTION OF ENGINOFF, DO
		OCT	40634		# A, 4.63 = DOSPSOFF (-0CS)
DOSPSOFF	TCR	SPSOFF		# SHUTDOWN SPS, MASS UPDATES, ETC.
		CS	OCT27/24	# (OCTAL 27)
		TC	NEWPHASE
		OCT	4		# 4.27 = DOTVCRCS (250 CS)

		TC	FIXDELAY	# 2.5 SECOND DELAY FOR SPS TAILOFF
		DEC	250

DOTVCRCS	TCR	SETMAXDB	# WIDE DEADBAND FOR CUTOFF TRANSIENT

		TC	IBNKCALL	# SET UP RCS DAP (KILLS TVCDAPS, SETS T5
		CADR	RCSDAPON	#	BITS, WAITS 0.6SEC FOR TVCEXEC DIE)

		TC	IBNKCALL	# UPDATE WEIGHT/G AND MASS-PROPERTIES FOR
		CADR	MASSPROP	#	RCS DAP STARTUP IN 0.6 SECONDS

		TCR	TVCZAP		# WIPE OUT TVC, TURN OFF CLOKTASK

		TC	PHASCHNG
		OCT	00354		# A, 4.35 = POSTBURN (NOVAC, PRIO12)
		CAF	PRIO12		# SET UP POSTBURN V16N40 JOB
		TC	NOVAC
		EBANK=	DAPDATR1	# (SET MAXDB IN POST41)
		2CADR	POSTBURN

		TCF	TASKOVER

		EBANK=	DAPDATR1
SPSOFF		EXTEND			# ESTABLISH SPSOFF TEVENT
		DCA	TIME2
		DXCH	TEVENT
		CS	BIT7		# RESET ENGONFLG
		MASK	FLAGWRD5
		TS	FLAGWRD5	# (RESTARTS WILL SHUT DOWN SPS NOW)
		CS	BIT13		# SHUT DOWN SPS ENGINE
		EXTEND
		WAND	DSALMOUT

		CAF	BIT14		# ISSUE SIV CUTOFF COMMAND
		EXTEND			# FOR POSSIBLE BACK-UP USE
		WOR	CHAN12
MASSBACK	CAE	V97VCNTR	# RESTORE PART OF PRE-DECREMENTED MASS
					#	V97CNTR = VCNTR UNLESS V97 IS
					#	ACTIVE.  ONLY V97CNTR IS THEN RIGHT.
		EXTEND			# VCNTR COUNTS 1/2-SECONDS IN TVC EXEC
		MP	EMDOT		#	MDOT, SC.AT B+3 KG/CS
		LXCH	A
# Page 699
		EXTEND
		MP	1SEC		# DEC 100
		AD	MASSTMP		# CORRECTION IS ACCURATE TO 5 CS OF FLOW
		TS	CSMMASS		#	(1.44 KG OR 0.4 BITS)

		CA	TVCPHASE	# CHECK IF OK FOR TRIM UPDATE
		AD	ONE		#	THESE CHECKS ARE ONLY NEEDED
		EXTEND			#	FOR A LESS THAN 0.4 SEC BURN
		BZF	BTRIMR		# NO.  INITIALIZATION NOT COMPLETE
		CS	FLAGWRD6	# YES, CHECK IF TVC
		MASK	OCT60000
		EXTEND
		BZMF	BTRIMR		# NO, NOT TVC YET
BESTTRIM	CAE	DELPBAR		# UPDATE TRIMS WITH DELFILTER VALUES
		TS	PACTOFF
		CAE	DELYBAR
		TS	YACTOFF
BTRIMR		TC	Q
		EBANK=	DAPDATR1
STEERADS	2CADR	STEERING

.6SECT5		OCT	37703
5SECDP		DEC	0		# MAKE DP 5SEC
5SEC		DEC	500
OCT02202	OCT	02202		# BITS 2, 8, 11 FOR CHANNEL 12 TVC/OPTICS
		EBANK=	DAPDATR1
TVCON2C		2CADR	TVCDAPON

	-1	INHINT
TVCZAP		CS	OCT02202	# DISABLE TVC AND OPT ERR CNTRLS, REENGAGE
		EXTEND			#	OPTICS DAC
		WAND	CHAN12
		CS	BIT1		# ENABLE T4RUPT OPTICS MONITOR .... PERMIT
		TS	OPTIND		#	OPTICS-ZERO BUT NOT OPTICS-DRIVE
		CAF	ZERO		# CLEAR NVWORD1 IN CASE CLOCKJOB WAITING
		TS	NVWORD1
		CS	BIT11		# CLEAR TIMRFLAG TO STOP CLOKTASK
		MASK	FLAGWRD7
		TS	FLAGWRD7
		TC	Q
		EBANK=	DAPDATR1
UPDATEVG	STQ	BON
			QTEMP1
			XDELVFLG
			CALL40.8

		SLOAD	BMN
			NBRCYCLS
			SETUP.9
# Page 700

		VLOAD	VAD
			DELVSUM
			DELVREF
		STORE	DELVSUMP
		EXIT
		CA	ONE
		AD	NBRCYCLS
		TS	NBRCYCLP

		TC	PHASCHNG	# TYPE B RESTART RESTART BELOW AND 5.3 REREADACCS
		OCT	10035

		CA	NBRCYCLP
		TS	NBRCYCLS
		TC	INTPRET
		VLOAD
			DELVSUMP
		STORE	DELVSUM

CALL40.8	CALL
			S40.8
		GOTO
			QTEMP1

SETUP.9		BON	SLOAD
			FIRSTFLG
			SURELY.9
			NBRCYCLP
		NORM	VXSC		# (NORM HANDLES ZERO PROPERLY)
			X1
			BDT
		VSR*	VAD
			0 -14D,1
			VGTEMP
		VSU
			DELVSUM
		STORE	VGPREV
SURELY.9	EXIT
		CAF	PRIO10
		TC	FINDVAC
		EBANK=	DAPDATR1
		2CADR	S40.9

		TC	2PHSCHNG
		OCT	00051		# A, 1.5 = REDO40.9, PRIO 10
		OCT	10035
		TC	INTPRET
		VLOAD
			RN		# ACTIVE VEHICLE RADIUS VECTOR AT T1
		STOVL	RINIT
# Page 701
			VN		# ACTIVE VEHICLE VELOCITY VECTOR AT T1
		STODL	VINIT
			PIPTIME
		STORE	TNIT
		BDSU
			TPASS4
		STOVL	DELLT4
			HI6ZEROS
		STODL	DELVSUM
			HI6ZEROS
		STORE	NBRCYCLS
		GOTO
			CALL40.8
		EBANK=	DAPDATR1
STEERING	TC	INTPRET
		CALL
			UPDATEVG
		EXIT
		CAF	BIT9		# CHECK IMPULSW
		MASK	FLAGWRD2
		CCS	A
		TCF	+3		# PRE-IGNITE, REQUEST ENG-OFF, OR POST-OFF
SERVXT		TC	POSTJUMP
		CADR	SERVEXIT
		CAF	BIT13		# CHECK ENGINE-ON/-OFF
		EXTEND
		RAND	DSALMOUT
		EXTEND
		BZF	SERVXT		# ENGINE-OFF, SO PRE-IGNITE OR POST-OFF
		TCR	E7SETTER
		EBANK=	TIG
		INHINT
		EXTEND
		DCA	TIG
		DXCH	MPAC
		EXTEND
		DCS	TIME2
		DAS	MPAC
		TCR	DPAGREE
		CAE	MPAC +1		# (LESS THAN 6 (OR 4) SECONDS TO GO)
		CCS	A		# PROTECT AGAINST NEG/ZRO W.L. CALL
		TCF	+3
		TCF	+2
		CAF	ZERO
		AD	ONE
		XCH	L
		CA	ZERO
		DXCH	TGO
		CA	TGO +1
		TC	WAITLIST
# Page 702
		EBANK=	TGO
		2CADR	ENGINOFF

		TC	2PHSCHNG
		OCT	40153		# A, 3.15 = ENGINOFF (TGO+1) .... NOTE GROUP
		OCT	10035		# B, 5.3 = REREADAC, AND START BELOW
		TC	DOWNFLAG	# CLEAR IMPULSW, ENGINOFF IS NOW SET UP
		ADRES	IMPULSW		# RESTARTS OK
		TCF	SERVXT

# Page 703
# ROUTINE ** CLOKTASK ** DESCRIPTION

		EBANK=	TIG
CLOKTASK	CAF	BIT11		# IS TIMRFLAG SET
		MASK	FLAGWRD7
		CCS	A
		TCF	CLOCKON
		TC	PHASCHNG
		OCT	00006		# KILL RESTART
		TC	TASKOVER

CLOCKON		EXTEND
		DCA	TIME2
		DXCH	TTOGO
		EXTEND
		DCS	TIG
		DAS	TTOGO

SETCLOCK	CAF	1SEC
		TC	WAITLIST
		EBANK=	TIG
		2CADR	CLOKTASK

		CCS	NVWORD1
		TCF	+3
		TCF	SETTB6

		TCF	+1
		CS	V06N85B		# CHECK FOR V06N85B (P41)
		AD	NVWORD1
		EXTEND
		BZF	SETUPDYN	# V06N85, SO UPDATE N85 FOR DYNAMIC DISP

		CAF	PRIO27
		TC	NOVAC
		EBANK=	DAPDATR1
		2CADR	CLOCKJOB

SETTB6		CS	TIME1		# SET GROUP6 TIMEBASE
		TS	TBASE6
		TCF	TASKOVER

SETUPDYN	CAF	PRIO27		# SET UP A JOB TO UPDATE N85 (FOR P41=V06)
		TC	FINDVAC
		EBANK=	DAPDATR1
		2CADR	DYNDISP

		TCF	SETTB6		# CLOSE OUT CLOCKTASK
# Page 704
DYNDISP		TC	INTPRET		# UPDATE N85 FOR A DYNAMIC V06N85 IN P41.
		CALL			#	PRIOR TO BLANKING AND AVEG (V16N85)
			P40CNV85
		EXIT
		TCF	CKNVWRD1

# Page 705
# ROUTINE ** CLOCKJOB ** DESCRIPTION

		EBANK=	DAPDATR1
CLOCKJOB	CA	CDUX
		TS	CDUSPOTX
		CA	CDUY
		TS	CDUSPOTY
		CA	CDUZ
		TS	CDUSPOTZ
		TC	BANKCALL
		CADR	QUICTRIG
CKNVWRD1	INHINT
		CCS	NVWORD1		# DETERMINE FUNCTION, INDICATED BY NVWORD1
		TCF	NOFLASH
		TCF	ENDOFJOB
		TCF	ENGREQST	# SPS ENGINE-ON-ENABLE V99 FLASH
FAILDSP		CAF	V06N40		# SPS ENGINE-FAILED V97 FLASH
		TC	BANKCALL
		CADR	CLOCPLAY
		TCF	V97T		# TERMINATE
		TCF	V97P		# PROCEED
		TCF	V97E		# ENTER

ENGREQST	CAF	V06N40
		TC	BANKCALL
		CADR	CLOCPLAY	# LINUS MAKES IT A REDO, INHINT OK
		TCF	V99T		# TERMINATE
		TCF	V99P		# PROCEED
		TCF	V99E		# ENTER

NOFLASH		CAE	NVWORD1		# DISPLAY NVWORD1 NORMALLY
		TC	BANKCALL
		CADR	REGODSP

E7SETTER	CAF	EBANK7
		TS	EBANK
		EBANK=	TIG
		TC	Q

E6SETTER	CAF	EBANK6		# SET UP EBANK6
		TS	EBANK
		EBANK=	DAPDATR1
		TC	Q

		EBANK=	DAPDATR1
V99E		TC	2PHSCHNG
		OCT	00006		# KILL PRE40.6/CLOKTASK PROTECTION
		OCT	05024		# C, PRIORITY NEXT, JOB BELOW
# Page 706
		OCT	27000
V99EJOB		TCR	TVCZAP -1	# WIPE OUT TVC, CLOKTASK
		TCF	P40RCS		# V16N85 POST-BURN OPERATIONS

		EBANK=	DAPDATR1
V99T		TC	2PHSCHNG	# (ENTRY FROM V97T FLOW TOO)
		OCT	00006		# KILL PRE40.6/CLOKTASK PROTECTION
		OCT	05024		# C, PRIORITY NEXT, JOB BELOW
		OCT	27000
V99TJOB		TCR	TVCZAP -1	# WIPE OUT TVC, CLOKTASK
		TCF	POST41		# AVEGEXIT, SETMAXDB, GOTOPOOH

V99P		INHINT
		CAE	FLAGWRD7	# CHECK ASTN FLAG FOR PRIOR V99P
		MASK	BIT12
		CCS	A
		TCF	V99P/TIG	# YES, THIS MUST BE A RESTART ENTRY

ASTNV99P	CAF	BIT12		# SET ASTN FLAG
		ADS	FLAGWRD7
		CAE	FLAGWRD7	# CHECK IGN FLAG FOR TIG-0 ARRIVAL
		MASK	BIT13
		EXTEND
		BZF	V99P/TIG	# NO, CLEAR THE V99 AND WAIT FOR TIG-0

ENDV99PI	CAF	BIT1		# TIG-0 HAS COME ALREADY
		TC	WAITLIST	# SET UP IGNITION HERE
		EBANK=	DAPDATR1
		2CADR	IGNITION

V99P/TIG	CAF	V06N40		# CLEAR THE V99 FLASH AND WAIT FOR TIG-0
		TS	NVWORD1
ENDV99P		TCF	ENDOFJOB

		EBANK=	CSMMASS
V97T		TC	2PHSCHNG
		OCT	00006		# KILL GROUP 6 (CLOKTASK)
		OCT	40674		# A, 4.67 = V97TTASK (-0 CS), TBASE NOW
		CAF	BIT1
		TC	TWIDDLE
		ADRES	V97TTASK	# KEEP EBANK6 FOR MASSES, SPSOFF, ETC.
		TCF	ENDOFJOB

		EBANK=	CSMMASS
V97TTASK	CAF	ZERO		# DISABLE CLOCKJOB
		TS	NVWORD1
		CAF	3MDOT		# 3 SECONDS OF MDOT (2-4 SEC ENGFAIL
		AD	CSMMASS		#	DETECTION) NOT LOST BECAUSE THRUST
		TS	MASSTMP		#	FAILED.  COPYCYCLE FOR MASSBACK
# Page 707
		TC	PHASCHNG
		OCT	05014		# C, DELTAT NEXT, TASK BELOW, IN
		DEC	-0		# -0 CS

		TCR	SPSOFF		# SHUTDOWN SPS ENGINE, MASS UPDATE, ETC.
		TC	PHASCHNG
		OCT	00714		# A, 4.71 = V97TRCS (250 CS), TBASE OLD
		TC	FIXDELAY	# DELAY 2.5 SECONDS FOR (POSSIBLE) TAIL-
		DEC	250		#	OFF (FALSE THRUST-LOSS)

		EBANK=	DAPDATR1
V97TRCS		TC	IBNKCALL	# RCS DAP IN 0.6SEC, SETTING T5 BITS TO
		CADR	RCSDAPON	#	KILL TVCEXEC/TVCROLLDAP STARTS
		CAF	PRIO27		# SET UP V99T FOR TVCZAP AND POST41 (SET-
		TC	NOVAC		#	MAXDB AND GOTOPOOH)
		EBANK=	DAPDATR1	# EBANK6 FOR SETMAXDB IN POST41
		2CADR	V99T

ENDV97T		TCF	TASKOVER

		EBANK=	V97VCNTR
V97P		TC	PHASCHNG
		OCT	40734		# A, 4.73 = V97PTASK (-0 CS), TBASE NOW
		CAF	BIT1
		TC	TWIDDLE
		ADRES	V97PTASK
		TCF	ENDOFJOB

		EBANK=	V97VCNTR
V97PTASK	CAE	V97VCNTR	# GET MASS UPDATES (TVCEXEC) GOING AGAIN
		TS	VCNTR		#	(ERRORS IF FLASE THRUST-LOSS AND/OR
					#	POOR SYNC OF MANUAL ENGINE-ON AND
					#	THE VERB 97 PROCEED)
		CAF	V06N40		# REDISPLAY V06N40
		TS	NVWORD1
		TC	UPFLAG		# SET IDLEFAIL TO ALLOW R41-BYPASS, IN
		ADRES	IDLEFAIL	#	CASE OF UNFAVORABLE S40.8 SYNCH
		TC	UPFLAG		# SET STEERSW TO RE-ENABLE STEERING
		ADRES	STEERSW
		TC	PHASCHNG
		OCT	00134		# A, 4.13 = R40ENABL (200 CS), TBASE OLD
		TC	FIXDELAY	# WAIT 2 SECONDS, THEN
		DEC	200

		EBANK=	WHOCARES
R40ENABL	TC	DOWNFLAG	# RE-ENABLE R40 BY CLEARING IDLEFAIL
		ADRES	IDLEFAIL
		TC	PHASCHNG
		OCT	00004		# KILL GROUP 4
# Page 708
ENDV97P		TCF	TASKOVER

		EBANK=	WHOCARES
V97E		TC	PHASCHNG
		OCT	40534		# A, 4.53 = V97ETASK (-0 CS), TBASE NOW
		CAF	BIT1
		TC	WAITLIST
		EBANK=	TIG
		2CADR	V97ETASK

		TCF	ENDOFJOB

		EBANK=	TIG
V97ETASK	CS	OCT24		# FORCE R1 OF V06N40 TO READ  59X59
		TS	TIG
		CAF	V06N40		# REDISPLAY V06N40
		TS	NVWORD1
		TCR	E6SETTER	# RETURN TO EBANK6 FOR REST OF V97ETASK
		EBANK=	CSMMASS
		CAF	3MDOT		# 3 SECONDS OF MDOT (2-4 SEC ENGFAIL
		AD	CSMMASS		#	DETECTION) NOT LOST BECAUSE THRUST
		TS	MASSTMP		#	FAILED....COPYCYCLE FOR MASSBACK
		TC	PHASCHNG
		OCT	00754		# A, 4.75 = SPSOFF97 (-0 CS), TBASE OLD
SPSOFF97	TCR	SPSOFF
		TC	PHASCHNG
		OCT	00114		# A, 4.11 = V97E40.6 (250 CS), TBASE OLD
		TC	FIXDELAY	# DELAY 2.5 SECONDS FOR (POSSIBLE) TAIL-
		DEC	250		#	OFF (FALSE THRUST-LOSS)

		EBANK=	DAPDATR1
V97E40.6	CAF	BIT1
		TC	WAITLIST
		EBANK=	CNTR
		2CADR	PRE40.6		# USE S40.6 RESTART ENTRY TO TRIM ENGINE

		TC	IBNKCALL	# RCS DAP IN 0.6SEC, SETTING T5 BITS TO
		CADR	RCSDAPON	#	KILL TVCEXEC/TVCROLLDAP STARTS.
					#	LEAVE NARROW DEADBAND FOR REIGNITE.

		TC	2PHSCHNG
		OCT	00026		# A, 6.2 = PRE40.6 (-0 CS), CLOKTASK (1 SEC)
		OCT	05014		# C, DELTAT NEXT, TASK BELOW, IN
		DEC	-0		# -0 CS.

QUICKIGN	CS	PRIO14		# CLEAR ASTNFLAG AND SET IGNFLAG FOR
		MASK	FLAGWRD7	#	IMMEDIATE V99 RESPONSE.
		AD	BIT13
		TS	FLAGWRD7
		TC	FIXDELAY	# DELAY TO ALLOW TIME FOR PRE40.6
# Page 709
		DEC	30

V99FLASH	CS	BIT9		# CAUSE V99 TO FLASH
		TS	NVWORD1
		TC	2PHSCHNG
		OCT	40774		# A, 4.77 = TIG-0 (-0 CS) TBASE FOR PREPTVC
		OCT	00033		# A, 3.3 = S40.13 (PRIO 20)
		CAF	PRIO20		# SET UP TIMEBURN
		TC	FINDVAC
		EBANK=	TGO
		2CADR	S40.13

ENDV97E		TCF	TASKOVER	# WAIT FOR CLOCKJOB (IMMEDIATE) REACTION
					# 	TO FLASHING V99 RESPONSE.

# MOD N02				LOG SECTION P40-P47
# MOD BY ZELDIN
#
# FUNCTIONAL DESCRIPTION
#	COMPUTE INITIAL THRUST DIRECTION(UT) AND INITIAL VALUE OF VG
#	VECTOR(VGTIG).
#
# CALLING SEQUENCE
#	L	CALL
#	L+1		S40.1
#
# NORMAL EXIT MODE
#	AT L+2 OF CALLING SEQUENCE (GOTO L+2) NORMAL RETURN OR
#	ERROR RETURN IF NOSOFLAG =1
#
# SUBROUTINES CALLED
#	CSMPREC
#	INITVEL
#	CALCGRAV
#	MIDGIM
#
# ALARM OR ABORT EXIT MODES
#	L+2 OF CALLING SEQUENCE, UNSOLVABLE CONIC IF NOSOFLAG=1
#
# ERASABLE INITIALIZATION REQUIRED
#	WEIGHT/G	ANTICIPATED VEHICLE MASS	SP B16 KGM
#	XDELVFLG	1=DELTA-V MANEUVER, 0=AIMPT STEER
#   IF DELTA-V MANEUVER:
#	DELVSIN		SPECIFIED DELTA-V REQUIRED IN
#			INERTIAL COORDS. OF ACTIVE VEHICLE
#			AT TIME OF IGNITION		VECTOR B7 M/CS
#	DELVSAB		MAG. OF DELVSIN			DP B7 M/CS
#	RTIG		POSITION AT TIME OF IGNITION	VECTOR B29 M
#	VTIG		VELOCITY AT TIME OF IGNITION	VECTOR B7 M/CS.
#	CSTEER = 0					DP
#   IF AIMPOINT STEERING:
#   IF AIMPT STEER
#	TIG		TIME OF IGNITION		DP B28 CS
#	RTARG		POSITION TARGET TIME		VECTOR B29 M
#	CSTEER = ECSTEER (GR 0) 			DP B1
# Page 710
#	TPASS4 -- TIME OF ARRIVAL AT AIMPOINT
#
# OUTPUT
#	UT		1/2 UNIT VECTOR ALIGNED WITH THRUST DIRETION IN REF COOR
#	VGTIG		INITIAL VALUE OF VELOCITY
#			TO BE GAINED (INERT. COORD.)		VECTOR B7 M/CS
#	DELVLVC		VGTIG IN LOC. VERT. COORDS.		B7 M/CS
#	F		NOMINAL THRUST FOR ENG USED FOR S40.13	DP B7 M-NEWT
#	BDT		V REQUIRED AT TIG -V REQUIRED AT (TIG-2SEC)
#	-GDT		FOR S40.13				VECT B7 M/CS
#	RTIG		CALC IN S40.1B (AIMPT) FOR S40.2,3	VECTOR B29M
#			POSITION AT TIME OF IGNITION
#
# DEBRIS	QTEMP1
#		MPAC, QPRET
#		PUSHLIST
#		RTX2,RTX1

		BANK	14
		SETLOC	P40S1
		BANK

		COUNT	16/S40.1

S40.1		SET	VLOAD
			FIRSTFLG
			LO6ZEROS
		STORE	BDT
		STQ	BOF
			QTEMP
			XDELVFLG
			S40.1B		# LAMBERT
		VLOAD	ABVAL		# EXTERNAL DELTA-V
			DELVSIN
		STORE	DELVSAB		# COMPUTE FOR P30/P40 INTERFACE
					#	THUS PERMITTING MODULE-ONLY CHANGE
		SETPD	VLOAD
			0
			VTIG
		STORE	VINIT
		VXV	UNIT
			RTIG
		STOVL	UT		# UP IN UT
			RTIG
		STORE	RINIT
		VSQ	PDDL
			36D
		DMPR	DDV
			THETACON
		DMP	DMP
			DELVSAB
			WEIGHT/G
		DDV
# Page 711
			F
		STOVL	14D
			DELVSIN

		DOT	VXSC
			UT
			UT
		VSL2	PUSH		# (DELTAV.UP)UP SCALED AT 2(+7) P.D.L. 0
		BVSU	PDDL		# DELTA VP SCALED AT 2(+7) P.D.L. 6
			DELVSIN
			14D
		SIN	PDVL
			6D
		VXV	UNIT
			UT
		VXSC	STADR
		STOVL	VGTIG		# UNIT(VP X UP)SIN(THETAT/2) IN VGTIG.
		UNIT	PDDL		# UNIT(DELTA VP) IN P.D.L. 6
			14D
		COS	VXSC
		VAD	VXSC
			VGTIG
			36D
		VSL2 	VAD
		STADR
		STORE	VGTIG		# VG IGNITION SCALED AT 2(+7) M/CS

		UNIT
		STOVL	UT		# THRUST DIRECTION SCALED AT 2(+1)
			VGTIG
		PUSH	SET
			AVFLAG
		CALL
			MIDGIM		# VGTIG IN LV COOR AT 2(+7)M/CS IN DELVLVC
		GOTO
			QTEMP
S40.1B		DLOAD	DSU		# LAMBERT
			TIG
			TWODT
		STODL	TDEC1
			TPASS4
		DSU
			TDEC1
		STCALL	DELLT4
			AGAIN
		VLOAD
			VIPRIME
		STODL	UT
			TIG
		STORE	TDEC1
# Page 712
		BDSU
			TPASS4
		STCALL	DELLT4
			AGAIN
		VLOAD	PUSH
			DELVEET3
		STORE	VGTIG
		SET	CALL
			AVFLAG
			MIDGIM
		SETPD	GOTO
			0
			CALCUT

THETACON	2DEC	.******** B-8

		SETLOC	P40S3
		BANK

		COUNT	24/S40.1

EP4(45)H	2DEC	.125

EP4(10)H	2DEC	.*********

AGAIN		STQ	CALL
			QTEMP1
			THISPREC
		SXA,2	SXA,1
			RTX2
			RTX1
		VLOAD
			RATT
		STORE	RTIG
		STOVL	RINIT
			VATT
		STORE	VTIG
		STORE	VINIT
		SETPD	SLOAD
			0
			HI6ZEROS
		PDDL	BON
			EP4(45)H
			NORMSW
			+3
		DLOAD
			EP4(10)H
		PUSH	CALL
			INITVEL
		SETPD	GOTO
# Page 713
			0
			QTEMP1
CALCUT		VLOAD	CALL
			RTIG
			CALCGRAV	# GDELTAT IN MPAC AT 2(+7) M/CS
		VSL1	V/SC
			200CS		# G AT 2(-5) M/CS. CS
		PDVL	VSU
			VIPRIME
			UT
		V/SC	VSU
			200CS
		VXSC	VSL2
			CSTEER
		STOVL	12D		# B.C SCALED AT 2(-15) PDL 12D
			VGTIG
		UNIT	PUSH		# UG PDL 0 SCALED AT 2(+1)

		DOT	VXSC
			12D
			0
		VSL2	BVSU
			12D
		STODL	12D		# Q PDL SCALED AT 2(-5)
			F
		SRR	DDV
			4
			WEIGHT/G
		DSQ	PDVL		# F/MASS SQUARED PDL 6 AT 2(-10M/(CS.CS)
			12D
		VSQ
		BDSU	SQRT
		VXSC	VSL1
		VAD	UNIT
			12D
		STCALL	UT
			QTEMP
200CS		2DEC	200 B-12

# Page 714
# PROGRAM DESCRIPTION S40.2,3		DATE 15 NOV 66
# MOD NO 2				LOG SECTIONS P40-P47
# MOD BY ZELDIN
#
# FUNCTIONAL DESCRIPTION
#
#	COMPUTE GIMBAL ANGLES IF THRUSTING OCCURRED WITH PRESENT IMU
#	ORIENTATION, WINGS LEVEL SPACECRAFT, HEADS UP
#	COMPUTE X AXIS OF ENGINE BELL
#	COMPUTE PREFERRED IMU ORIENTATION (XSCREF)
#	FOR THIS CALCULATION, ASSUME X AXIS OF SC ALONG UT INITIALLY,
#	YSC=UNIT(XXR), ZSC=UNIT(XX(XXR)) AND ROTATE ENGINE BELL ALONG UT.
#	NEW SC AXES WILL BE APPROX. WINGS LEVEL AND NEW SC AXES IN REF.
#	COORDS. WILL BE PREFERRED IMU ORIENTATION.
#	COMPUTE DESIRED THRUST DIRECTION IN SM COORDS.
#
# CALLING SEQUENCE
#	L	CALL
#	L+1		S40.2,3
#
# NORMAL EXIT MODE
#	AT L+2 OF CALLING SEQUENCE (GOTO L+2)
#
# SUBROUTINES CALLED
#	CALCGA
#
# ALARM OR ABORT MODES
#	NONE
#
# ERASABLE INITIALIZATION REQUIRED
#	PACTOFF		TOTAL PITCH TRIM ANGLE		SP AT 1.0795111 REV.
#	YACTOFF		TOTAL YAW   TRIM ANGLE		SP AT 1.0795111 REV.
#	UT		DESIRED THRUST DIRECTION	VECT. B2 M/(CS.CS)
#	RTIG		POSITION AT TIME OF IGNITION	VECT. B29 M
#	ENG2FLAG	ON=RCS  OFF=SPS
#
# OUTPUT
#	SCAXIS		UNIT VECT. ALIGNED WITH ENG BELL IN SC COOR.	B1
#	XSCREF		UNIT VECTORS ALIGNED WTH PREFERRED IMU		B1
#	YSCREF
#	ZSCREF
#	GIMBAL ANGLES IN THETAD
#	POINTVSM	UNIT VECT ALONG DESIRED THRUST DIRECTION IN SM	B1
#
# DEBRIS
#	PUSHLIST, QPRET, MPAC
#	QTEMP	TEMP. ERASABLE

		BANK	24
		SETLOC	P40S
		BANK
		COUNT*	$$/S40.2
S40.2,3		VLOAD	MXV
			UT
			REFSMMAT
		VSL1	STQ
			QTEMP
		STORE	POINTVSM	# THRUST IN SM AT 2
		SETPD	BON
			0
# Page 715
			ENG2FLAG
			S40.2,3B
		DLOAD
			HI6ZEROS
		PUSH	SLOAD		# ZERO PDL 0
			YACTOFF
		DMP	SL1
			TRIMSCAL
		DAD	PUSH
			YBIAS
		COS	PDDL		# COS(Y +Y0) PDL 2
		SIN	PUSH		# SIN(Y +Y0) PDL 4
		SLOAD
			PACTOFF
		DMP	SL1
			TRIMSCAL
		DAD	PUSH
			PBIAS
		COS	PDDL		# COS(P +P0) PDL 6
		SIN	PUSH		# SIN(P +P0) PDL 8D
		STODL	ZSCREF		# SIN(P +P0)
			6
		DMP	SL1
			4
		DCOMP	PDDL		# -SIN(Y+Y0)COS(P+P0) PDL 10
			6
		DMP	SL1
			2
		VDEF

		STODL	XSCREF		# PD POINTER AT 6 NEW SC X AXIS SCALED AT
			ZSCREF
		DMP	SL1
			4
		PDDL	DMP
			ZSCREF
			2
		SL1	DCOMP
		VDEF

		STODL	ZSCREF		# PD POINTER AT 4 NEW SC Z AXIS SCALED AT 2
		VDEF

		STODL	YSCREF		# PD POINTER AT 0 NEW SC Y AXIS SCALED AT 2
			ZSCREF
		PDDL	PDDL
			YSCREF
			XSCREF
		VDEF
# Page 716
		STOVL	SCAXIS		# ENGINE BELL SCALED AT 2
			UT
		PDVL	UNIT
			RTIG
		VXV	VCOMP
			0
		UNIT	PUSH
		CALL
			TSTRXUT
		VXV	VCOMP
			0
		VSL1	PDVL		# 2 RF/SC IN PDL 12D
			XSCREF
		VXM	VSL1
			0
		STOVL	XSCREF		# X OF PREF. IMU,X OF SC IN REF COOR. AT 2
			YSCREF
		VXM	VSL1
			0
		STOVL	YSCREF		# Y OF PREF. IMU,Y OF SC IN REF COOR. AT 2
			ZSCREF
		VXM	VSL1
			0
		STORE	ZSCREF		# Z OF PREF. IMU,Z OF SC IN REF COOR. AT 2
		SETPD	GOTO
			0
			QTEMP
S40.2,3B	VLOAD
			UNITX
		STOVL	SCAXIS
			UT
		STORE	XSCREF
		VXV	UNIT
			RTIG
		STCALL	6D
			TSTRXUT
		STORE	YSCREF
		VXV	VCOMP
			XSCREF
		VSL1
		STCALL	ZSCREF		# ZNB AXIS IN REF COOR
			QTEMP
TSTRXUT		DLOAD	BHIZ
			36D
			BADVCTOR
		VLOAD	RVQ
			6D
BADVCTOR	VLOAD	UNIT
			RTIG
		PDVL	UNIT
# Page 717
			VTIG
		VSR3	VAD
		VXV	UNIT
			UT
		VCOMP
		STORE	6D
		RVQ
TRIMSCAL	2DEC	1.07975111 B-1

YBIAS		2DEC	+.00263888889	# YAW	MECH BIAS (+0.95 DEG, THRUST ON)

PBIAS		2DEC	-.00597222222	# PITCH	MECH BIAS (-2.15 DEG, THRUST ON)

					# REFERENCE, TRW 68.6520.3.3-40 27 FEB, 1968

# PROGRAM DESCRIPTION S41.1		DATE 8 DEC 66
# MOD NO 1				LOG SECTION P40-P47
# MOD BY ZELDIN
#
# FUNCTIONAL DESCRIPTION
#
#	COMPUTE VELOCITY TO BE GAINED INITIALLY IN REF COORDS.
#	TO CONTROL COORDS.
#
# CALLING SEQUENCE
#
#	L	CALL
#	L+1		S41.1
#
# NORMAL EXIT MODE
#
#	AT L +2 OF CALLING SEQUENCE
#
# SUBROUTINES CALLED:
#
#	CALCSMSC
#	CDUTRIG
#
# ALARM OR ABORT MODES
#
#	NONE
#
# ERASABLE INITIALIZATION REQUIRED
#
#	VG IN REF. COORD. PDL L POINTER AT L+5.  S41.1 WILL RETURN WITH
# 	POINTER AT L (L MUST BE LESS THAN OR = TO 14D)
#
# OUTPUT
#
#	MPAC CONTAINS VG IN CONTROL COORDS		VECT. B7 M/CS
#
# DEBRIS:
#
#	QTEMP		TEMP ERASABLE
#	QPRET

		COUNT	22/S41.1

		SETLOC	P40S5
		BANK

S41.1		STQ	CALL
			QTEMP
			CDUTRIG
		VLOAD
		MXV	CALL
			REFSMMAT
			*SMNB*
# Page 718
		MXV	VXSC
			QUADROT
			TENBNK14	# VG IN CONTROL COORD IN MPAC SCALED AT
		VSL5	GOTO		# VG IN CONTROL COORDS. IN MPAC AT 2(+7)
			QTEMP
TENBNK14	2DEC	10. B-4

# Page 719
# NAME		S40.8 -- CROSS PRODUCT STEERING
# FUNCTION	(1) UPDATES THE VELOCITY-TO-BE-GAINED VECTOR.
#		(2) GENERATES ANGULAR RATE STEERING COMMANDS FOR AUTOPILOT.
#		(3) ESTABLISHES ENGINE CUT-OFF SIGNALS AT APPROPRIATE TIMES.
#		(4) INITIATES THRUST-FAIL ROUTINE, R40
# CALLING SEQ	CALL S40.6
# INPUT		VGPREV 		LAST VALUE OF THE VELOCITY-TO-BE-GAINED VECTOR
#				PRIOR TO UPDATING IN METERS/CS AT +7.
#		DELVREF		CHANGE IN VEHICLE VELOCITY SINCE LAST MEASUREMENT
#				IN METERS/CS AT +7.
#		BDT		EFFECT OF RATE OF CHANGE OF REQUIRED VELOCITY AND
#				GRAVITY DURING DT UPON VELOCITY-TO-BE-GAINED IN
#				METERS/CS AT +7.
#		CSTEER		A SCALAR OF THE STEERING LAW, SC.AT B+1, USED FOR
#				SPS AIMPOINT STEERING MANEUVERS.
#		IDLEFAIL	A FLAG TO INHIBIT (IDLE) THE THRUST-FAIL ROUTINE.
#		STEERSW		A SWITCH TO PRECLUDE NEEDLESS CONDUCT OF STEERING.
#		REFSMMAT, DAPDATR1, PIPTIME
#		EREPFRAC, ETDECAY, KPRIMEDT FOR TVC.
# OUTPUT	TTOGO		TIME REMAINING FOR ENGINE BURN IN CS AT +28.
#		OMEGAC		DP VECTOR RATE COMMAND, SC.AT 1/(2TVCDT) REVS/SEC.
#		VG, VGPREV, VGDISP, TGO, TIG, SCALED AS NOTED IN CODING
#		STEERSW, IMPULSW, NVWORD1
#		REPFRAC, CNTR, VCNTR, VCNTRTMP FOR TVC (R40 INTERFACING)
# DEBRIS	OMEGAXC, +1
# SUBROUTINES USED:  *SMNB*, ALARM

		SETLOC	P40S1
		BANK
		EBANK=	DAPDATR1
		COUNT	16/S40.8

S40.8		SETPD	STQ
SPBIT1			00D
			QTEMP
		VLOAD	BVSU		# CONSTRUCT DELVG, SC.AT B+7 M/CS
			DELVREF
			BDT
		VAD
			VGPREV
		STORE	VG		# VELOCITY-TO-BE-GAINED, SC.AT B+7 M/CS

		ABVAL
		STORE	VGDISP		# FOR DISPLAY PURPOSES
		EXIT
		TC	PHASCHNG
		OCT	10035		# TYPE B RESTART RESTART BELOW AND 5.3 REREADAC

		TC	INTPRET
		VLOAD
# Page 720
			VG
		STORE	VGPREV
		BOFF	VLOAD
			STEERSW		# SKIP TGO AND CROSS-PRODUCT
			QTEMP
			DELVREF
		ABVAL	PUSH		# CHECK FOR LOTHRUST
		SLOAD	DMP
			DVTHRESH	# SC.AT B-2 M/CS
			DPB-9
		BDSU
		BMN	EXIT
			LOTHRUST
		CAE	DAPDATR1	# ENABLE TVCDAP CG TRACKING
		MASK	BIT14
		CCS	A
		CAF	BIT1
		INDEX	A		# LM-OFF, LM-ON VALUE
		CAE	EREPFRAC
		TS	REPFRAC

		TC	INTPRET
TGOCALC		VLOAD	BVSU		# GET DELVG
			DELVREF
			BDT
		UNIT
		DOT	PUSH		# (00D)
			VG
		BPL	DDV		# ANGLE SHOULD BE GREATER THAN PI/2
			INCRSVG		#	DISPLAY ALARM IF NOT
			2VEXHUST
		DAD	DMP		# (DOT PRODUCT UP FROM 00D)
			LODPHALF
		NORM	SR1
			X1
		PDDL	NORM
			36D		# (MAG DELVG)
			X2
		BDDV
		XSU,2	SL*
			X1
			0 -9D,2
		DMP	PUSH		# (00D)
			-FOURDT
		SLOAD	SR
			ETDECAY		# ETDECAY SC.AT B+14 CS
			14D
		BDSU	STADR
		STORE	TGO		# TIME TO GO IN CS. AT +28
		DAD
# Page 721
			PIPTIME
		STODL	TIG
			TGO
		DSU	BMN
			FOURSEC
			S40.81

XPRODUCT	VLOAD	VXSC
			BDT
			CSTEER
		VSL2	VSU
			DELVREF
		UNIT	PDVL
			VG
		UNIT	VXV
		MXV	CALL
			REFSMMAT	# (REFSMMAT/2)
			*SMNB*
		VXSC
			KPRIMEDT	# (KPRIMEDT SCIAT PI/8 RAD)
OMEGACLC	STORE	OMEGAC
		GOTO
			QTEMP

		SETLOC	DAPS7
		BANK
		COUNT	17/S40.8

TWODT		2DEC	200.0 B-28	# 2 SEC

-FOURDT		2DEC	-800 B-18	# -4(200CS), SC.AT B+18CS (-4 FOR SCALING)

2VEXHUST	2DEC	63.020792 B-7	# 2(10338.0564 FPS), SC.AT B+7 M/CS

FOURSEC		2DEC	400.0 B-28	# 4 SEC

DPB-9		2DEC	1 B-9

		SETLOC	DAPS6
		BANK

		COUNT	20/S40.8

S40.81		SET	VLOAD		# TGO LESS THAN 4 SECONDS
			IMPULSW		# FOR ENGINE-OFF CALL
			HI6ZEROS
RATEZRO		STORE	OMEGAC		# TVC TO ATTITUDE HOLD
		EXIT
		CAF	POSMAX		# INHIBIT SWITCHOVER/TVC EG TRACKING
		TS	CNTR
# Page 722
		TC	INTPRET
		CLEAR	GOTO
			STEERSW		# RESTARTS OK
			QTEMP
INCRSVG		EXIT			# ALARM INDICATING THAT THRUST IS POINTING
		TC	ALARM		# IN WRONG DIRECTION.
		OCT	01407
		TC	INTPRET
		GOTO
			QTEMP

LOTHRUST	BON	VLOAD		# THRUST FAILURE (LO-OR-NO) INDICATED
			IDLEFAIL	# SET BY V97P.  ALLOWS 1 BYPASS IN CASE OF
			QTEMP		#	UNFAVORABLE S40.8 SYNCH.
			HI6ZEROS	# START OF ENGINE-FAIL (R40) OPERATIONS
		STORE	OMEGAC		# PUT TVC IN ATTITUDE HOLD
		EXIT

		CS	ZERO
		TS	VCNTR		# KILL CSMMASS UPDATING
		TS	VCNTRTMP	# (TVCEXEC LOGIC REQUIRES THIS TOO)
		TS	REPFRAC		# KILL TVCDAP CG TRIM TRACKING
		TS	NVWORD1		# SET UP ENGINE-FAIL V97FLASH (CLOCKJOB)

		TC	INTPRET
		CLEAR	GOTO		# INHIBIT STEERING AND TGO CALC (MANUAL
			STEERSW		# 	SHUTDOWN IF NOT SET UP AGAIN)
			QTEMP		# RESTARTS OK

# Page 723
# NAME		S40.9 -- VTOGAIN (AIMPOINT MANEUVERS ONLY)
# FUNCTION	(1) GENERATES REQUIRED VELOCITY AND VELOCITY-TO-BE-GAINED
#		VECTORS FOR USE DURING AIMPOINT MANEUVERS.
#		(2) UPDATES THE B VECTOR WHICH IS USED IN THE FINAL
#		CALCULATION OF EXTRAPOLATING THE VELOCITY-TO-BE-GAINED.
# CALLING SEQ	VIA FINDVEC AS NEW JOB.
# INPUT		RNIT	ACTIVE VEHICLE RADIUS VECTOR IN METERS AT +29.
#		VNIT	ACTIVE VEHICLE VELOCITY VECTOR IN METERS/CS AT +7.
#		VRPREV	LAST COMPUTED VELOCITY REQUIRED VECTOR IN
#			METERS/CS AT +7.
#		NONTIG	TIME OF IGN.  USED IN TARGETTING ROUTINES B+28
#		DELLT4	TRANSFER TIME FROM PIPTIME TO TARGET B+28
#		TNIT	TIME OF RNIT AND VNIT IN CS AT +28
#		GDT/2	HALF OF VELOCITY GAINED IN DELTA T TIME DUE TO
#			ACCELERATION OF GRAVITY IN METERS/CS AT +7.
#		DELVREF	CHANGE IN VELOCITY DURING LAST 2 SEC IN
#			METERS/CS AT +7.
#		NORMSW	SET=CENTRAL ANGLE BETWEEN RTARG AND RTIG IS BETWEEN
#			165 TO 195 DEGREES.
#			RESET=CENTRAL ANGLE OUTSIDE CONE DESCRIBED ABOVE.
# OUTPUT	VGTEMP	VELOCITY TO BE GAINED VECTOR IN METERS/CS AT +7.
#		COGA	INPUT OF INITIAL GUESS FOR LAMBERT FROM S40.1
#			OR PREVIOUS PASS THRU S40.9.
#		GOBL/2	OBLATENESS TERM IN AVG GRAV CALC: GOBL*RSQ/MU
#		VRPREV/	VELOCITY REQUIRED VECTOR IN METERS/CS AT +7.
#		BDT	B VECTOR IN METERS/CS AT +7.
# SUBROUTINES USED -- INITVEL

		SETLOC	P40S1
		BANK

		EBANK=	NBRCYCLS
		COUNT	16/S40.9

S40.9		TC	INTPRET
		SETPD	DLOAD
			00D
			LO6ZEROS
		PDDL
			EP4(45)L
		BON	DLOAD
			NORMSW
			+2
			EP4(10)L
		PUSH
		CLEAR	CALL
			GUESSW
			HAVEGUES
		EXIT
		TC	PHASCHNG	# SAVE TIME BY NOT REDOING LAMBERT CALCS
		OCT	05021		# C, PRIORITY NEXT, JOB BELOW
# Page 724
		OCT	10000
		TC	INTPRET
ENDLAMB		BON
			FIRSTFLG
			FIRSTTME
		VLOAD	VSU
			VIPRIME
			VRPREV
		PDDL	DSU
			TNIT
			TNITPREV
		SL	BDDV
			17D
			200CSHI
		VXSC
		VSU	VSL1
			GDT/2
		STORE	BDT
FIRSTTME	SLOAD	DCOMP
			RTX2
		BMN
			MOONCASE
		VLOAD	UNIT
			RN
		DLOAD	DSU
			PIPTIME
			NOMTIG
		DMP	DDV
			EARTHMU
			34D
		VXSC	VAD
			GOBL/2
			VGTEMP		# NOTE: NO TEST IS MADE TO SUBTRACT GOBL
		STORE	VGTEMP		# INSIDE 165-195 DEGREE CONE AREA.
MOONCASE	EXIT
		TC	PHASCHNG
		OCT	04021		# C, JOB BELOW

COPY40.9	TC	INTPRET
		DLOAD
			TNIT
		STOVL	TNITPREV
			VIPRIME
		STORE	VRPREV
		CLEAR	EXIT
			FIRSTFLG
	-2	CS	ONE		# REDO40.9 (RESTART) ENTRY TO END S40.9
		TS	NBRCYCLS
ENDS40.9	TC	PHASCHNG
		OCT	00001
# Page 725
		TCF	ENDOFJOB

REDO40.9	TC	INTPRET		# S40.9 RESTARTS COME HERE TO GRACEFULLY
		VLOAD			#	TERMINATE S40.9 SO THAT IT CAN BE
			LO6ZEROS	#	SET UP WITH LATEST R,V,T NEXT PASS
		STODL	DELVSUM		#	(TYPE C PHASE POINTS '04021' WILL
			LO6ZEROS	#	FORCE NORMAL S40.9 TERMINATIONS,
		STOVL	NBRCYCLS	#	RATHER THAN LOSE TIME OF BRAND NEW
			VGPREV		#	PASS -- QUICK OLD DATA BETTER THAN
		STORE	VGTEMP		#	NONE) NOW CAN GO THRU SETUP.9
		EXIT			#	WITHOUT DISTURBING VGPREV.
		TCF	ENDS40.9 -2	# STORE 0,0 COVERED NBRCYCLS,P -- FIX UP S

200CSHI		2DEC	200 B-12

EARTHMU		2DEC*	-3.986032 E10 B-36*

EP4(45)L	2DEC	.125

EP4(10)L	2DEC	.*********

# Page 726
# NAME:  		S40.13 -- TIMEBURN
#
# FUNCTION		(1) DETERMINE WHETHER A GIVEN COMBINATION OF VELOCITY-TO-
#			BE-GAINED AND ENGINE CHOICE RESULT IN A BURN TIME SUFFICIENT
#			TO ALLOW STEERING AT THE VEHICLE DURING THE BURN, AND
#			(2) THE MAGNITUDE OF THE RESULTING BURN TIME -- IF IT IS SHORT --
#			AND THE ASSOCIATED TIME OF THE ENGINE OFF SIGNAL.
#
# CALLING SEQUENCE	VIA FINDVAC AS A NEW JOB
#
# INPUT			VGTIG -- VELOCITY TO BE GAINED VECTOR (METERS/CS) AT +7
#			WEIGHT/G -- MASS OF VEHICLE IN KGM AT TIG
#			F -- ENGINE THRUST IN M.NEWTONS AT +7
#			MDOT -- RATE OF DECREASE OF VEHICLE MASS DURING ENGINE BURN
#				IN KILOGRAMS/CENTISECOND AT +3.  THIS SCALING MAY
#				REQUIRE MODIFICATION FOR SATURN BURNS.
#
# OUTPUT		IMPULSW		ZERO FOR STEERING
#					ONE FOR ATTITUDE HOLD
#			TGO		TIME TO BURN IN CENTISECONDS AT +14
#			THE QUANTITY M.NEWTON SHALL BE USED TO EXPRESS WEIGHT IN TERMS OF
#			(KILOGRAM*METER)/(CENTISECOND*CENTISECOND)
#			(1) M.NEWTON = (10000) NEWTONS.

		EBANK=	TGO
		COUNT	16/40.13

S40.13		TC	INTPRET
		SETPD	SET
			00D
			IMPULSW		# ASSUME NO STEERING UNTIL FOUND OTHERWISE
		VLOAD	ABVAL
			VGTIG		# VELOCITY TO BE GAINED AT +7
		EXIT
		CAF	BIT7		# TEST +X TRANSLATION
		EXTEND
		RXOR	CHAN31
		MASK	BIT7
		EXTEND
		BZF	NOTADDUL
		TC 	INTPRET
		PDDL	DDV		# 00D = MAG OF VGTIG AT +7
			S40.135		# COMPENSATION FOR 2 JET ULLAGE AT +24
			WEIGHT/G	# MASS IN KGMS AT +16
		BON	SL1		# DOUBLE CORRECTION IF FOUR JETS
			NJETSFLG
			S40.130
S40.130		BDSU
		PDDL	DDV		# 00D = MAG OF VGTIG CORRECTED FOR ULLAGE
			K1VAL		# M.NEWTON-CS AT +24
			WEIGHT/G
		BDSU	BMN
			00D
			S40.131		# TGO LESS THAN 100 CS
		PDDL	DMP		# 02D = TEMP1 AT +7
# Page 727
			EMDOT		# SPS FLOW RATE SC.AT B+3 KG/CS (SP, NOTE)
			3.5SEC		# 350 CS AT +14
		BDSU	PDDL
			WEIGHT/G
			FANG
		DMP	SR2
			5SECOND		# 500 CS AT +14
		DDV	PUSH		# 04D = TEMP2
		BDSU	BPL
			02D
			S40.133		# TGO GREATER THAN 600 CS
		DLOAD	BDDV
		DMP	DAD
			5SECOND		# 500 CS AT +14
			1SEC2D		# 100 CS AT +14
		GOTO
			S40.132
S40.131		DLOAD	DMP		# TGO LESS THAN 100 CS
			WEIGHT/G
		DAD	DDV
			K2VAL		# M.NEWTON CS AT +24
			K3VAL		# M.NEWTON AT +10
S40.132		EXIT
		EBANK=	TGO
		TC	TPAGREE
		CA	MPAC
		XCH	L
		CA	ZERO
		DXCH	TGO		# TGO IN CS AT +28
		TC	S40.134
S40.133		CLEAR	EXIT		# WILL STEER VEHICLE
			IMPULSW
S40.134		TC	PHASCHNG	# KILL GROUP 3
		OCT	3

		TCF	ENDOFJOB

NOTADDUL	TC	INTPRET
		GOTO
			S40.130 +1	# DO NOT COMPENSATE FOR 7 SEC OF ULLAGE
		SETLOC	DAPS7
		BANK

		COUNT	17/40.13

K1VAL		=	EK1VAL		# DP PAD LOAD B+23 NEWTON-SEC/E+2
K2VAL		=	EK2VAL		# DP PAD LOAD B+23 NEWTON-SEC/E+2
K3VAL		=	EK3VAL		# DP PAD LOAD B+09 NEWTONS/E+4
1SEC2D		2DEC	100.0 B-14	# 100.0 CS AT +14
# Page 728
3.5SEC		2DEC	350.0 B-13	# 350 CS AT +13

5SECOND		2DEC	500.0 B-14	# 500.0 CS AT +14

S40.135		2DEC	69.6005183 B-23	# IMPULSE FROM 7.96 SECS OF 2-JET FIRING
					# 	7.96 (199.6)COS(10) LB-SEC, SC.AT
					#	B+23 NEWTON-SEC/E+2 (7 SEC ULLAGE
					#	TO GO, PLUS 0.96 SEC FROM PIPTIME)

# Page 729
# NAME		S40.6 GIMBAL DRIVE TEST AND/OR GIMBAL TRIM
# MOD NO 5				DATE 9 MARCH, 1967
# MOD BY ENGEL				LOG SECTION P40-P47
#
# FUNCTIONAL DESCRIPTION
#	GIMBAL DRIVE TEST....0,+2,-2,0 DEGREE ENGINE COMMANDS, AT 2 SECOND
#		INTERVALS, FIRST IN PITCH, THEN IN YAW.  ASTRONAUT VERIFICATION
#		OF GIMBAL MOTION ON GPI
#	GIMBAL TRIM....AFTER A 4 SECOND DELAY, ENGINE COMMANDED TO
#		PRE-COMPUTED TRIM POSITION.  ASTRONAUT VERIFICATION ON GPI.
#	PRE40.6....RESTART ENTRY TO RE-DO S40.6, ONLY IF RCS IS ON --- IF TVC
#		IS NOT ON --- PRIMARILY TO GET ACTUATORS TRIMMED FOR IGNITION.
#		BYPASS 4 SEC DELAY.  SPEED IS CRITICAL NEAR IGNITION.
#		IF TVC IS ON (TVCDAPON OR LATER) THEN REDOTVC WILL TAKE CARE
#		OF RESTARTING ACTUATORS.
#
# CALLING SEQUENCE....
#	WAITLIST, WITH 2CADR FOR S40.6 (OR PRE40.6), WITH EBANK= CNTR
#
# NORMAL EXIT MODE -- FIXDELAY, TASKOVER
#
# SUBROUTINES CALLED....
#	OUTPUT (INTERNAL)
#	FIXDELAY
#
# ALARM OR ABORT EXIT MODES --- NONE
#
# ERASABLE INITIALIZATION REQUIRED
#	CNTR = +0, NORMALLY SET BY THE P40 CALL AT TST,TRIM.
#	MRKRTMP....POSITIVE FOR GIMBAL DRIVE TEST AND GIMBAL TRIM (BOTH)
#		   NEGATIVE FOR GIMBAL TRIM ONLY
#	PACTOFF, YACTOFF SC.AT 85.41 ARCSEC/BIT (V48N48 P, YTRIM)
#	"SC CONT" SWITCH AT "CMC" (A/P CONTROL SWITCH AT "GNC")
#	ACTIVE SPS GIMBAL MOTOR POWER(S), PITCH, YAW
#
# OUTPUT
#	TVCYAW, TVCPITCH (BITS RELEASED)
#	TVC ENABLE AND OPTICS ERROR COUNTER ENABLE
#
# DEBRIS
#	TBMPR60, CNTR

		BANK	17
		SETLOC	DAPS6
		BANK

		EBANK=	CNTR
		COUNT	20/S40.6

PRE40.6		CS	FLAGWRD6	# RESTART ENTRY TO S40.6 (DO NOT PERMIT
		MASK	OCT60000	#	IF TVC, BITS 15,14 = 1,0)
		EXTEND
		BZMF	+2
		TCF	TASKOVER	# TVC, REDOTVC WILL REESTABLISH INTERFACE

		CS	BIT1		# RCS, SO DO S40.6, GIMTRIM ONLY
# Page 730
		TS	MRKRTMP

		CAF	BIT1		# FOR REVISED S40.6 TIMING FOR RESTARTS...
		TS	CNTR		# TO INDICATE A RESTART ENTRY (CNTR 1S
					#	NORMALLY +0, BY S40.6)

		EBANK=	CNTR
S40.6		CS	ZERO		# INHIBIT OPTICS ACTIVITY
		TS	OPTIND

		CS	BIT2		# DISENABLE OPTICS ERROR COUNTERS (ZERO,
		EXTEND			# 	AND INHIBIT PULSE TRANSMISSION --
		WAND	CHAN12		#	NORMAL STATE)

		CAF	OCT02200	# TVC ENABLE (SPS SERVO AMPS SEE DAC
		EXTEND			#	VOLTAGES) AND DISENGAGE OPTICS/DAC
		WOR	CHAN12

		TC	FIXDELAY	# 60MS PROCEDURAL DELAY (40MS MINIMUM) FOR
		DEC	6		#	RELAY LATCHING

		CAF	BIT2		# ENABLE OPTICS ERROR COUNTERS
		EXTEND
		WOR	CHAN12

		TC	FIXDELAY	# 20MS PROCEDURAL DELAY (4MS MINIMUM) FOR
		DEC	2		#	RELAY LATCHING

RSTRTST		CCS	CNTR		# CHECK FOR RESTART ENTRY (PRE40.6)
		TCF	GIMTRIM +2	# RESTART ENTRY....BYPASS 4 SECOND DELAY
					#	TST,TRIM SETS +0 ON NORMAL ENTRY

		CAE	MRKRTMP		# CHECK FOR TEST/TRIM OR TRIM ONLY
		TS	CNTR		#	MRKRTMP SAVES CNTR FOR RESTARTS
		EXTEND
		BZMF	GIMTRIM		# (TRIM ONLY)

GDTSETUP	CS	ZERO		# GIMBAL DRIVE TEST SETUP, FOR PITCH
		TS	CNTR

GIMDTEST	CAF	+2ACTDEG	# GIMBAL DRIVE TEST, 1ST INCREMENT
		TC	OUTPUT		#	(LEAVES GIMBAL AT +2 DEG)
		CAF	-4ACTDEG	# 2ND INCREMENT (LEAVES GIMBAL AT -2)
		TC	OUTPUT
		CAF	+2ACTDEG	# 3RD INCREMENT (LEAVES GIMBAL AT -0)
		TC	OUTPUT

		CS	CNTR		# CHECK FOR COMPLETION OF YAW TEST.
# Page 731
		CCS	A
		TCF	GIMTRIM		# COMPLETED, GO TO GIMBAL TRIM ROUTINE
		CS	BIT1		# SET UP YAW TEST
		TS	CNTR
		TCF	GIMDTEST	# FOR YAW TEST

OUTPUT		EXTEND			# OUTPUT THE INCREMENT....SAVE Q
		QXCH	TEMPR60

		INDEX	CNTR
		TS	TVCPITCH

		INDEX	CNTR
		CAF	BIT11
		EXTEND
		WOR	CHAN14

		TC	FIXDELAY	# WAIT 2SEC, WHILE ASTRONAUT VERIFIES
		DEC	200		# 	GIMBAL MOTION ON GPI
		TC	TEMPR60

GIMTRIM		TC	FIXDELAY	# WAIT 4 SECONDS BEFORE GIMBAL TRIM
		DEC	400

	+2	CS	ZERO		# PICK UP TRIM VALUES AND OUTPUT THEM
		AD	PACTOFF		#	(AVOID +0) ENTRY POINT FROM RSTRTST
		TS	TVCPITCH	#	ON A RESTART, TO AVOID 4SEC DELAY
		CS	ZERO
		AD	YACTOFF
		TS	TVCYAW

		CAF	PRIO6		# RELEASE THE COUNTERS, BITS 11,12
		EXTEND
		WOR	CHAN14

ENDS40.6	TCF	TASKOVER

OCT02200	OCT	02200		# BITS 8,11 FOR CHANNEL 12 TVC/OPTICS
-4ACTDEG	DEC	-168		# -2(+2ACTDEG), WHOLE BITS, NO ROUNDUP
+2ACTDEG	DEC	+84		# +2 DEG, SC.AT 85.41 ARCSEC/BIT (+84D)

# CALLED BY "DONOUN46" (VERB 48), OR DIRECTLY BY "FRESHDAP" (RCS DAP) VIA IBNKCALL

		COUNT	20/S41.2

S41.2		CA	DAPDATR1
# Page 732
		MASK	THREE
		AD	A
		TS	RATEINDX

		INHINT
		CAE	DAPDATR1	# IS LEM ATTACHED (BITS 14,13 OF DAPDATR1
		MASK	PRIO30		#	=10)
		AD	-BIT14		# (OCT57777)
		EXTEND
		BZF	TOGETHER	# YES

		CS	BIT2		# NO, UNSET FLAG
		MASK	FLAGWRD7
		TS	FLAGWRD7

		TCF	+4

TOGETHER	CS	FLAGWRD7	# ATTACHED, SET FLAG FOR INTEGRATION
		MASK	BIT2
		ADS	FLAGWRD7

		RELINT

		CA	DAPDATR1
		MASK	BIT4
		EXTEND
		BZMF	+2		# DEC 46 MEANS NARROW DB
		CA	DEC409
		AD	DEC46		# DEC 455 MEANS WIDE DB
		TS	ADB

		CA	DAPDATR1
		MASK	BIT7		# QUAD BD
		EXTEND
		BZMF	+2
		CA	ONE
		TS	XTRANS
		CA	DAPDATR1
		MASK	BIT10		# QUAD AC
		EXTEND
		BZMF	+2
		CS	ONE
		ADS	XTRANS

		INHINT
		EXTEND
		BZF	+5		# CLEAR NJETSFLG (4 JETS, OR NO JETS)
		CS	FLAGWRD1	# SET NJETSFLG (2 JETS, AC OR BD QUADS)
		MASK	BIT15		# NJETSFLG = 1 FOR 2 JET ULLAGE (AC OR BD)
		ADS	FLAGWRD1
# Page 733
		TCF	+4
		CS	BIT15		# KJETSFLG = 0 FOR 4 JET (OR 0 JET) ULLAGE
		MASK	FLAGWRD1
		TS	FLAGWRD1
		RELINT
		CA	DAPDATR2
		MASK	BIT13
		EXTEND
		BZMF	+2
		TCF	+2
		CS	ONE
		COM
		TS	ACORBD		# MINUS FOR A-C, PLUS FOR B-D

		CA	DAPDATR2
		MASK	BIT10
		CCS	A
		TCF	+4
		CA	ONE
		TS	RACFAIL
		TCF	BDFAIL
		CA	ZERO
		TS	RACFAIL
		CA	DAPDATR2
		MASK	BIT4
		CCS	A
		TCF	BDFAIL
		CS	ONE
		TS	RACFAIL
BDFAIL		CA	DAPDATR2
		MASK	BIT7
		CCS	A
		TCF	+4
		CA	ONE
		TS	RBDFAIL
		TC	Q
		CA	ZERO
		TS	RBDFAIL
		CA	DAPDATR2
		MASK	BIT1
		CCS	A
		TC	Q
		CS	ONE
		TS	RBDFAIL
		TC	Q

# DAPFIG ENTRY VIA TC POSTJUMP AS JOB FROM "STABLISH" (VERB 46)

		BANK	42
		SETLOC	EXTVBS
# Page 734
		BANK

DAPFIG		CS	BIT9		# TURN OFF SIVB TAKEOVER
		EXTEND
		WAND	CHAN12
		CAE	DAPDATR1	# DETERMINE VEHICLE CONFIGURATION
		EXTEND
		MP	BIT3		#	RIGHT SHIFT 4 OCTAL DIGITS
		MASK	THREE		#	(IN CASE BIT 15 IS USED)
		INDEX	A
		TCF	+1		#	BRANCH BASED ON CONFIG....

		TCF	NODAPUP		# CM.......ACTIVATE NODAP
		TCF	RCSDAPUP	#	CSM......ACTIVATE RCSDAP
		TCF	RCSDAPUP	#	CSM/LEM..ACTIVATE RCSDAP
		TC	POSTJUMP
		CADR	SATSTKON
RCSDAPUP	INHINT			# CALL TO ACTIVATE RCSDAP, AND RETURN
		TCR	IBNKCALL
		CADR	RCSDAPON
		RELINT
		TCF	ENDFIG		# CAME IN VIA V46, GO OUT VIA GOPIN
NODAPUP		EXTEND			# T5 IDLE FOR NODAP (DON'T WORRY ABOUT T)
		DCA	T5IDLDAP
		DXCH	T5LOC
		TC	DOWNFLAG	# RESET T5-USAGE FLAGS FOR NODAP
		ADRES	DAPBIT1		# BIT 15 FLAG 6 = 0
		TC	DOWNFLAG
		ADRES	DAPBIT2		# BIT 14 FLAG 6 = 0
		INHINT
		TC	IBNKCALL	# ZERO JET CHANNELS IN 14 MS AND THEN
		CADR	ZEROJET		# LEAVE THE T6 CLOCK DISABLED.
		RELINT
		CAF	BIT1		# KILL KALCMANU JOB
		TS	HOLDFLAG
ENDFIG		TC	POSTJUMP	# CAME IN VIA V46, GO OUT VIA GOPIN
		CADR	GOPIN
		SBANK=	PINSUPER	# Added by RSB 2009
		EBANK=	PACTOFF
T5IDLDAP	2CADR	T5IDLOC

		SBANK=	LOWSUPER
		BANK	17
		SETLOC	DAPS6
		BANK

DEC409		DEC	409
DEC46		DEC	46

# Page 735

# CALLED BY "DONOUN47" (VERB 48), OR DIRECTLY BY "FRESHDAP" (RCS DAP)
S40.14		CAE	IXX		# RCS ENTRY
		EXTEND
		MP	CONTONE
		TS	J/M

		CA	IAVG
		EXTEND
		MP	CONTONE
		TS	J/M1

		TS	J/M2

		EXTEND
		DCA	CONTTWO
		EXTEND
		DV	IXX
		TS	KMJ

		EXTEND
		DCA	CONTTWO
		EXTEND
		DV	IAVG
		TS	KMJ1

		TS	KMJ2

		TC	Q

CONTONE		DEC	.662034		# 2PI/M
CONTTWO		2DEC	.00118

		COUNT 	24/TVNG

		BANK	31
		SETLOC	P40S
		BANK

POS-2.5		OCT	37405
		EBANK=	DAPDATR1
RCSCADR		2CADR	RCSUP

6SECT5		OCT	37704
		COUNT	21/RCSUP

		BANK	20

		SETLOC	DAPS3
		BANK

# Page 736

RCSUP		LXCH	BANKRUPT
		EXTEND
		QXCH	QRUPT

		TCR	RCSDAPON	# ACTIVATE RCS DAP

		TCF	RESUME

		EBANK=	DAPDATR1
RCSADDR		2CADR	RCSATT

0.6SECT5	OCT	37704

					# RCSDAPON ENTRY MUST BE UNDER INT-INHIBIT
RCSDAPON	CAF	0.6SECT5	# 0.6 SEC ALLOWS TVCEXEC/ROLLDAP TO DIE
	+1	TS	TIME5		# ENTRY FROM R00TOP00
		TS	T5PHASE		# WILL CAUSE FRESHDAP (+1)

		CS	RCSFLAGS	# SET BIT3 TO REINITIALIZE FDAI ERROR
		MASK	BIT3		#	DISPLAY, IN CASE SC CONT SWITCH
		ADS	RCSFLAGS	#	IN SCS NOT GNC (GUIDEMODE PRIMARY)

		EXTEND
		DCA	RCSADDR		# (RCSATT)
		DXCH	T5LOC

		CS	OCT60000	# SEE BITS 15,14 TO 01 TO INDICATE
		MASK	FLAGWRD6	#	T5 TAKEOVER BY RCSDAP
		AD	BIT14
		TS	FLAGWRD6	# KILLS TVCEXEC AND ROLLDAP STARTS

		TC	Q		# RETURN TO CALLER (TVCDAPOF OR RCSDAPUP)



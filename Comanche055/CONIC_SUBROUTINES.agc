# Copyright:	Public domain.
# Filename:	CONIC_SUBROUTINES.agc
# Purpose:	Part of the source code for Colossus 2A, AKA Comanche 055.
#		It is part of the source code for the Command Module's (CM)
#		Apollo Guidance Computer (AGC), for Apollo 11.
# Assembler:	yaYUL
# Contact:	<PERSON> <<EMAIL>>.
# Website:	www.ibiblio.org/apollo.
# Pages:	1262-1308
# Mod history:	2009-05-08 RSB	Adapted from the Colossus249/ file of the
#				same name, using Comanche055 page images.
#		2009-05-20 RSB	Corrected:  Fixed four interpreter
#				instructions.
#
# This source code has been transcribed or otherwise adapted from digitized
# images of a hardcopy from the MIT Museum.  The digitization was performed
# by <PERSON>, and arranged for by <PERSON> of the Museum.  Many
# thanks to both.  The images (with suitable reduction in storage size and
# consequent reduction in image quality as well) are available online at
# www.ibiblio.org/apollo.  If for some reason you find that the images are
# illegible, contact <NAME_EMAIL> about getting access to the
# (much) higher-quality images which <PERSON> actually created.
#
# Notations on the hardcopy document read, in part:
#
#	Assemble revision 055 of AGC program Comanche by NASA
#	2021113-051.  10:28 APR. 1, 1969
#
#	This AGC program shall also be referred to as
#			Colossus 2A

# Page 1262
# PROGRAM DESCRIPTION -- ENTIRE CONIC SUBROUTINE LOG SECTION	DATE -- 1 SEPTEMBER 1967
# MOD NO. -- 0							LOG SECTION -- CONIC SUBROUTINES
# MOD BY KRAUSE							ASSEMBLY -- COLOSSUS REVISION 88
#
# FUNCTIONAL DESCRIPTION --
#	THE FOLLOWING SET OF SUBROUTINES SOLVE VARIOUS PROBLEMS INVOLVING THE TRAJECTORY PRODUCED BY A CENTRAL
# 	INVERSE-SQUARE FORCE ACTING ON A POINT MASS, AS OUTLINED IN THE CMC AND LGC LUNAR LANDING MISSION GSOP, SECTION
#	*******.  A GENERAL USAGE POINT-OF-VIEW WAS TAKEN IN FORMULATING, MECHANIZING, AND SCALING THE SUBROUTINES,
#	RATHER THAN OPTIMIZING EACH FOR A PARTICULAR USE.  THEREFORE, MULTIPLE USAGE CAN BE MADE OF THE SUBROUTINES
#	INVOLVING ANY REALISTIC SET OF CONSTRAINTS.  IT SHOULD BE NOTED THAT ONLY ONE SET OF CODING IS USED, WHETHER THE
#	EARTH, MOON, OR ANY OTHER CELESTIAL BODY IS SPECIFIED AS THE CENTRAL BODY OF THE PROBLEM, PROVIDED ONE OBSERVES
#	THE INHERENT SCALE CHANGE REQUIRED IN POSITION, VELOCITY, MU, AND TIME, AS OUTLINED IN MISSION PROGRAMMING
#	DEFINITION MEMO NO. 10.  THIS CAN BE ACCOMPLISHED BY SIMPLY ADDING TO THE MUTABLE AND INITIALIZING THE SUBROUTINES
#	APPROPRIATELY.
#
#	DUE TO THE UNIFORMITY OF THE EQUATIONS INVOLVED, CODING WAS MINIMIZED BY TREATING INDIVIDUAL EQUATIONS AND
#	BLOCKS OF EQUATIONS AS SUBROUTINES OF LOWER RANK WHENEVER POSSIBLE.  AS A RESULT, THREE BY-PRODUCTS SUBROUTINES,
# 	DIRECTLY USABLE AS INDEPENDENT SUBROUTINES, WERE GENERATED.
#
# RESTRICTIONS --
#	THE ONLY LIMITATION IN THE SCOPE OF THE PROBLEM WHICH CAN BE SOLVED BY A PARTICULAR SUBROUTINE IS THE SCALING
#	LIMIT OF EACH PARAMETER AS SPECIFIED IN THE GSOP.  THESE SCALING LIMITS WERE CHOSEN SO THAT ALL FEASIBLE TRAJECTORIES
#	COULD BE HANDLED.
#
#	SINCE THE SUBROUTINES (EXCEPT KEPLER) USE COMMON SUBROUTINES OF LOWER RANK WHICH USE ERASABLE OTHER THAN
#	THE PUSHLIST (DUE TO ITS LIMITED SIZE) AND COMMON INTERPRETIVE SWITCHES, THE CONIC SUBROUTINES CANNOT BE ALLOWED
#	TO INTERRUPT EACH OTHER.  IT IS UP TO THE USER TO GUARANTEE THIS CONDITION.

# Page 1263
# PROGRAM DESCRIPTION -- KEPLER SUBROUTINE			DATE -- 11 OCTOBER 1967
# MOD NO. -- 1							LOG SECTION -- CONIC SUBROUTINES
# MOD BY KRAUSE							ASSEMBLY -- COLOSSUS 103 AND SUNDANCE 222
# MOD NO. -- 2 (AUGUST 1968) BY ROBERTSON: TO PERMIT BACKDATING BY MORE THAN ONE ORBITAL PERIOD.
# MOD NO. -- 3 (DEC 1968) BY ROBERTSON: SUPPRESSION OF X-MODULO-ING
# MOD NO. -- 4 (JAN 1969) BY ROBERTSON: CLEAR OVFIND AT KEPLER ENTRY
#
# FUNCTIONAL DESCRIPTION --
#	THIS SUBROUTINE, GIVEN AN INITIAL STATE VECTOR AND THE DESIRED TRANSFER TIME THROUGH WHICH THE STATE IS TO
#	BE UPDATED ALONG A CONIC TRAJECTORY, COMPUTES THE NEW, UPDATED STATE VECTOR.  THE TRAJECTORY MAY BE ANY CONIC
#	SECTION -- CIRCULAR, ELLIPTIC, PARABOLIC, HYPERBOLIC, OR RECTILINEAR WITH RESPECT TO THE EARTH OR THE MOON. THE
#	USE OF THE SUBROUTINE CAN BE EXTENDED USING OTHER PRIMARY BODIES BY SIMPLE ADDITIONS TO THE MUTABLE WITHOUT
#	INTRODUCING ANY CODING CHANGES, ACCEPTING THE INHERENT SCALE FACTOR CHANGES IN POSITION AND VELOCITY.  AN ITERATION
#	TECHNIQUE IS UTILIZED IN THE COMPUTATION.
#
#	IF A NEGATIVE TIME-OF-FLIGHT IS INPUT, THE PROGRAM WILL SOLVE FOR THE STATE WHICH WOULD BE PRODUCED BY
#	EXTRAPOLATING THE POSITION BACKWARD IN TIME.
#
#	IF THE ABSOLUTE VALUE OF THE DESIRED TRANSFER TIME EXCEEDS THE ORBITAL PERIOD, THE SUBROUTINE, THROUGH A
# 	MODULAR TECHNIQUE, WILL COMPUTE THE STATE CORRESPONDING TO THE DESIRED TIME (WHETHER POSITIVE OR NEGATIVE).
#
# THE RESTRICTIONS ARE --
#	1.	(PREVIOUS RESTRICTION ON THE NEGATIVE DESIRED TRANSFER TIME IS NOW DELETED.)
#	2.	THE PARAMETERS IN THE PROBLEM CANNOT EXCEED THEIR SCALING LIMITS AS SPECIFIED IN THE GSOP.  IF
#		ANY OF THESE LIMITS ARE EXCEEDED, THE RESULTING SOLUTION WILL BE MEANINGLESS.
#
# 	THE NUMBER OF ITERATIONS AND, THEREFORE, THE COMPUTATION SPEED IS DEPENDENT ON THE ACCURACY OF THE
# 	GUESS, XKEPNEW.  THE AGC COMPUTATION TIME IS APPROXIMATELY .061 SECONDS FOR INITIALIZATION, .065 SECONDS FOR THE
# 	FINAL COMPUTATIONS, PLUS .083 SECONDS FOR EACH ITERATION.
#
# REFERENCES --
#	R-479, MISSION PROGRAMMING DEFINITION MEMO NO. 10, LUNAR LANDING MISSION GSOP, SECTION 5.5, SGA
#	MEMO 67-4.
#
# INPUT -- ERASABLE INITIALIZATION REQUIRED
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	RRECT		+29 FOR EARTH		DP INITIAL POSITION VECTOR IN METERS
#			+27 FOR MOON
# Page 1264
#	VRECT		+7 FOR EARTH		DP INITIAL VELOCITY VECTOR IN METERS/CENTISECOND
#			+5 FOR MOON
#	X1 (38D)	NONE			INDEX REGISTER SET TO -2D OR -10D ACCORDING TO WHETHER THE EARTH OR MOON,
#							RESPECTIVELY, IS THE CENTRAL BODY
#	TAU		+28			DESIRED TRANSFER TIME IN CENTISECONDS (DP)
#							MAY BE POS OR NEG AND ABSOLUTE VALUE MAY BE GREATER OR LESS THAN ONE ORBITAL PERIOD.
#	XKEPNEW		+17 FOR EARTH		DP GUESS OF ROOT X OF KEPLERS EQN IN SQRT(METERS).SIGN SHOULD AGREE WITH THAT OF TAU.
#			+16 FOR MOON			AND ABS VALUE SHOULD BE LESS THAN THAT CORRESPONDING TO A PERIOD, VIZ, 2PI SQRT(SEMI-
#							MAJOR AXIS), FOR SPEED OF CONVERGENCE, BUT IF EITHER CONDITION FAILS, XKEPNEW IS RESET
#							BY KEPLER TO A POOR BUT VALID GUESS.
#	TC		+28			DP PREV. VALUE OF TIME IN CENTISECS. MUST BE LESS THAN ONE ORBITAL PERIOD.
#	XPREV		+17 FOR EARTH		DP PREV. VALUE OF X IN SQRT(METERS).  MUST BE LESS THAN AN X CORRESPONDING TO ONE
#			+16 FOR MOON			ORBITAL PERIOD, VIZ, 2PI SQRT(SEMI-MAJOR AXIS)
#
# SUBROUTINES CALLED --
#	DELTIME
#
# CALLING SEQUENCE AND NORMAL EXIT MODES --
#	KEPRTN-2	GOTO			# MUST BE IN INTERPRETIVE MODE BUT OVFIND ARBITRARY.
#	KEPRTN-1		KEPLER		# RETURNS WITH XPREV IN MPAC.  PL IS AT 0.
#	KEPRTN		...			# CONTINUE
#
#	KEPLER MUST NOT BE CALLED DIRECTLY SINCE AN INTERRUPTION OF IT WOULD DESTROY THE ERASABLES IT NEEDS TO COMPLETE
#	THE INTERRUPTED JOB.  THEREFORE THE USER MUST CALL CSMCONIC OR LEMCONIC WHICH GUARANTEES NO INTERRUPTS AND WHICH
#	ALSO CALLS KEPPREP TO COMPUTE A GUESS OF XKEPNEW.
#
# ABORT EXIT MODES --
#	NONE
#
# OUTPUT --
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	RCV		+29 FOR EARTH		DP TERMINAL POSITION VECTOR IN METERS
#			+27 FOR MOON
#	VCV		+7 FOR EARTH		DP TERMINAL VELOCITY VECTOR IN METERS/CENTISEC
#			+5 FOR MOON
#	TC		+28			DP TRANSFER TIME IN CENTISECS TO WHICH KEPLER CONVERGED.  ALWAYS LESS THAN ONE PERIOD.
#	XPREV		+17 FOR EARTH		DP VALUE OF X IN SQRT(METERS) TO WHICH KEPLER CONVERGED. ALWAYS LESS THAN THE X
#			+16 FOR MOON			CORRESPONDING TO ONE PERIOD.
# Page 1265
#	FOR OTHER OUTPUT WHICH MAY BE OF USE, SEE DEBRIS.
#
# DEBRIS --
#	PARAMETERS WHICH MAY BE OF USE --
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	URRECT		+1			DP UNIT VECTOR OF INITIAL POSITION
#	R1		+29 FOR EARTH		DP MAGNITUDE OF INITIAL POSITION IN METERS
#			+27 FOR MOON
# 	ALPHA		-22 FOR EARTH		DP INVERSE OF SEMIMAJOR AXIS IN 1/METERS
#			-20 FOR MOON
#	TMODULO		+28			DP INTEGRAL NUMBER OF PERIODS IN CENTISECS, WHICH WAS SUBTRACTED FROM TAU. TO PRODUCE A
#							TAU. OF LESS THAN ONE PERIOD.
#
# PARAMETERS OF NO USE --
#	DP PARAMETERS -- EPSILONT, DELX, DELT, RCNORM, XMODULO, PLUS PUSHLIST REGISTERS 0 THROUGH 39D.

# Page 1266
# PROGRAM DESCRIPTION -- LAMBERT SUBROUTINE			DATE -- 1 SEPTEMBER 1967
# MOD NO. -- 0							LOG SECTION -- CONIC SUBROUTINES
# MOD BY KRAUSE							ASSEMBLY -- COLOSSUS REVISION 88
#
# FUNCTIONAL DESCRIPTION --
#	THIS SUBROUTINE CALCULATES THE INITIAL VELOCITY REQUIRED TO TRANSFER A POINT-MASS ALONG A CONIC TRAJECTORY
#	FROM AN INITIAL POSITION TO A TERMINAL POSITION IN A PRESCRIBED TIME INTERVAL.  THE RESULTING TRAJECTORY MAY BE
#	A SECTION OF A CIRCLE, ELLIPSE, PARABOLA, OR HYPERBOLA WITH RESPECT TO THE EARTH OR THE MOON.  THE USE OF THE
#	SUBROUTINE CAN BE EXTENDED USING OTHER PRIMARY BODIES BY SIMPLE ADDITIONS TO THE MUTABLE WITHOUT INTRODUCING ANY
#	CODING CHANGES, ACCEPTING THE INHERENT SCALE FACTOR CHANGES IN POSITION AND VELOCITY.  AN ITERATION TECHNIQUE IS
#	UTILIZED IN THE COMPUTATION.
#
# THE RESTRICTIONS ARE: --
#	1. RECTILINEAR TRAJECTORIES CANNOT BE COMPUTED.
#	2. AN ACCURACY DEGRADATION OCCURS AS THE COSINE OF THE TRUE ANOMALY DIFFERENCE APPROACHES +1.0.
#	3. THE ANGLE BETWEEN ANY POSITION VECTOR AND ITS VELOCITY VECTOR MUST BE GREATER THAN 1 DEGREE 47.5 MINUTES
#	   AND LESS THAN 178 DEGREES 12.5 MINUTES.
#	4. NEGATIVE TRANSFER TIME IS AMBIGUOUS AND WILL RESULT IN NO SOLUTION.
#	5. THE PARAMETERS IN THE PROBLEM MUST NOT EXCEED THEIR SCALING LIMITS SPECIFIED IN THE GSOP.  IF THE
#	   LIMITS ARE EXCEEDED, THE RESULTING SOLUTION WILL BE MEANINGLESS.
#
#	THE NUMBER OF ITERATIONS AND, THEREFORE, THE COMPUTATIONS SPEED IS DEPENDENT ON THE ACCURACY OF THE FIRST
#	GUESS OF THE INDEPENDENT VARIABLE, COGA.  THE AGC COMPUTATION TIME IS APPROXIMATELY
#	.105 SECONDS FOR INITIALIZATION, .069 SECONDS FOR FINAL COMPUTATIONS, PLUS .205 SECONDS FOR EACH ITERATION.
#
# REFERENCES --
#	R-479, MISSION PROGRAMMING DEFINITION MEMO NO. 10, LUNAR LANDING MISSION GSOP -- SECTION 5.5, SGA MEMO 67-8,
#	SGA MEMO 67-4.
#
# INPUT -- ERASABLE INITIALIZATION REQUIRED
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	R1VEC		+29 FOR EARTH		DP INITIAL POSITION VECTOR IN METERS
#			+27 FOR MOON
#	R2VEC		+29 FOR EARTH		DP TARGET OR TERMINAL POSITION VECTOR IN METERS
#			+27 FOR MOON
#	TDESIRED	+28			DP DESIRED TRANSFER TIME IN CENTISECONDS
#	X1 (38D)	NONE			INDEX REGISTER SET TO -2D OR -10D ACCORDING TO WHETHER THE EARTH OR MOON,
#							RESPECTIVELY, IS THE CENTRAL BODY
#	GEOMSGN		NONE			SP +.5 IF DESIRED TRANSFER ANGLE IS LESS THAN 180 DEGREES, -.5 IF GREATER THAN 180 DEG.
#	GUESSW		NONE			AN INTERPRETER SWITCH TO BE SET IF NO GUESS OF COGA IS AVAILABLE, CLEAR IF A GUESS OF
# Page 1267
#							COGA IS TO BE USED BY LAMBERT
#	COGA		+5			DP GUESS OF COTANGNT OF FLIGHT PATH ANGLE (MEASURED FROM VERTICAL).  THIS WILL BE
#						IGNORED IF GUESSW IS SET.
#	NORMSW		NONE			AN INTERPRETER SWITCH TO BE SET IF UN IS TO BE AN INPUT TO THE SUBROUTINE, CLEAR IF
#							LAMBERT IS TO COMPUTE ITS OWN NORMAL (UN).
#	UN		+1			DP UNIT NORMAL TO THE DESIRED ORBIT PLANE IN THE DIRECTION OF THE RESULTING ANGULAR
#							MOMENTUM VECTOR.  THIS WILL BE IGNORED IF NORMSW IS CLEAR.
#	VTARGTAG	NONE			A S.P. TAG TO BE SET TO ZERO IF LAMBERT IS TO COMPUTE THE VELOCITY AT R2VEC AS WELL AS
#							AT R1VEC.
#
# SUBROUTINES CALLED --
#	GEOM, GETX, DELTIME, ITERATOR, LAMENTER (PART OF NEWSTATE)
#
# CALLING SEQUENCE AND NORMAL EXIT MODES --
#	L 	CALL			# MUST BE IN INTERPRETIVE MODE BUT OVFIND ARBITRARY.
#	L+1		LAMBERT		# RETURNS WITH PL AT 0 AND WITH VVEC IN MPAC IF VTARGTAG WAS WAS NON-ZERO OR VTARGET
#					# 	IN MPAC IF VTARGTAG WAS ZERO
#	L+2	BON			# CONTINUE IF SOLNSW CLEAR SINCE SOLUTION IS ACCEPTABLE
#	L+3		SOLNSW
#	L+4		LAMABORT
#
#	IF A LAMBERT RESULT IS TO BE A FIRST GUESS FOR THE NEXT LAMBERT CALCULATION, COGA MUST BE PRESERVED AND
#	GUESSW MUST BE CLEAR FOR EACH SUCCEEDING LAMBERT CALL.
#
# ABORT EXIT MODES --
#	IF SOLNSW WAS SET UPON EXITING, EITHER LAMBERT WAS ASKED TO COMPUTE A TRANSFER TOO NEAR 0 OR 360 DEG, OR T
#	WAS TOO SMALL TO PRODUCE A REALISTIC TRANSFER BETWEEN R1VEC AND R2VEC.  IN EITHER CASE THE FIX MUST BE MADE
#	ACCORDING TO THE NEEDS OF THE PARTICULAR USER.  THE ABORT EXIT MODE MAY BE CODED AS ...
#	LAMBERT		DLOAD	ABS		# A MEASURE OF THE PROXIMITY TO 0 OR
#				1-CSTH		# 360 DEGREES.
#			DSU	BMN
#				ONEBIT
#				CHANGER2	# CHANGE R2VEC DIRECTION SLIGHTLY.
#			DLOAD	DAD
#				TDESIRED
#				SOMETIME
#			STCALL	TDESIRED	# INCRESE TDESIRED
#				LAMBERT
#
# OUTPUT --
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
# Page 1268
#	--------	--------------		-----------------------
#	VVEC		+7 FOR EARTH		DP INITIAL VELOCITY VECTOR IN METERS/CENTISECOND REQUIRED TO SATISFY THE BOUNDARY VALUE
#			+5 FOR MOON			PROBLEM.
#	VTARGET		+7 FOR EARTH		DP RESULTANT VELOCITY VECTOR AT R2VEC IN METERS/CENTISECOND.
#			+5 FOR MOON
#	SOLNSW		NONE			INTERPRETER SWITCH WHICH IS SET IF THE SUBROUTINE CANNOT SOLVE THE PROBLEM, CLEAR IF THE
#							SOLUTION EXISTS.
#
#	FOR OTHER OUTPUT WHICH MAY BE OF USE, SEE DEBRIS.
#
# DEBRIS --
#	PARAMETERS WHICH MAY BE OF USE --
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	SNTH		+1			DP SIN OF ANGLE BETWEEN R1VEC AND R2VEC
#	CSTH		+1			DP COSINE OF ANGLE
#	1-CSTH		+2			DP 1-CSTH
#	COGA		+5			DP COTAN OF INITIAL REQUIRED FLIGHT PATH ANGLE MEASURED FROM VERTICAL
#	P		+4			DP RATIO OF SEMILATUS RECTUM TO INITIAL RADIUS
#	R1A		+6			DP RATIO OF INITIAL RADIUS TO SEMIMAJOR AXIS
#	R1 (32D)	+29 FOR EARTH		DP INITIAL RADIUS IN METERS
#			+27 FOR MOON
#	UR1		+1			DP UNIT VECTOR OF R1VEC
#	U2		+1			DP UNIT VECTOR OF R2VEC
#
#	PARAMETERS OF NO USE --
#		DP PARAMETERS -- EPSILONL, CSTH-RHO, TPREV, TERRLAMB, R2, RTNLAMB (SP), PLUS PUSHLIST REGISTER 0 THROUGH 41D
#		ADDITIONAL INTERPRETIVE SWITCHES USED -- INFINFLG, 360SW, SLOPESW, ORDERSW

# Page 1269
# PROGRAM DESCRIPTION -- TIME-THETA SUBROUTINE			DATE -- 1 SEPTEMBER 1967
# MOD NO. -- 0							LOG SECTION -- CONIC SUBROUTINES
# MOD BY KRAUSE							ASSEMBLY -- COLOSSUS REVISION 88
#
# FUNCTIONAL DESCRIPTION --
#	THIS SUBROUTINE, GIVEN AN INITIAL STATE VECTOR AND A DESIRED TRUE-ANOMALY-DIFFERENCE THROUGH WHICH THE
#	STATE IS TO BE UPDATED ALONG A CONIC TRAJECTORY, CALCULATES THE CORRESPONDING TIME-OF-FLIGHT AND, IN ADDITION,
#	PROVIDES THE OPTION OF COMPUTING THE NEW UPDATED STATE VECTOR.  THE RESULTING TRAJECTORY MAY BE A SECTION OF A
#	CIRCLE, ELLIPSE, PARABOLA, OR HYPERBOLA WITH RESPECT TO THE EARTH OR THE MOON.  THE USE OF THE SUBROUTINE CAN BE
# 	EXTENDED USING OTHER PRIMARY BODIES BY SIMPLE ADDITIONS TO THE MUTABLE WITHOUT INTRODUCING ANY CODING CHANGES,
#	ACCEPTING THE INHERENT SCALE FACTOR CHANGES IN POSITION AND VELOCITY.
#
# THE RESTRICTIONS ARE --
#	1. THE ANGLE BETWEEN ANY POSITION VECTOR AND ITS VELOCITY VECTOR MUST BE GREATER THAN 1 DEGREE 47.5 MINUTES
#	   AND LESS THAN 178 DEGREES 12.5 MINUTES.
#	2. THE PARAMETERS IN THE PROBLEM MUST NOT EXCEED THEIR SCALING LIMITS SPECIFIED IN THE GSOP.  IF THE LIMITS
#	   ARE EXCEEDED, THE RESULTING SOLUTION WILL BE MEANINGLESS.
#
#	THE AGC COMPUTATION TIME IS APPROXIMATELY .292 SECONDS.
#
# REFERENCES --
#	R-479, MISSION PROGRAMMING DEFINITION MEMO NO. 10, LUNAR LANDING MISSION GSOP-SECTION 5.5, SGA MEMO 67-8.
#
# INPUT -- ERASABLE INITIALIZATION REQUIRED
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	RVEC		+29 FOR EARTH		DP INITIAL POSITION VECTOR IN METERS
#			+27 FOR MOON
#	VVEC		+7 FOR EARTH		DP INITIAL VELOCITY VECTOR IN METERS/CENTISECOND
#			+5 FOR MOON
#	SNTH		+1			DP SINE OF TRUE-ANOMALY-DIFFERENCE THROUGH WHICH THE STATE IS TO BE UPDATED
#	CSTH		+1			DP COSINE OF THE ANGLE
#	RVSW		NONE			AN INTERPRETIVE SWITCH TO BE SET IF ONLY TIME IS TO BE AN OUTPUT, CLEAR IF THE NEW STATE
#							IS TO BE COMPUTED ALSO.
#	X1 (38D)	NONE			INDEX REGISTER TO BE SET TO -2D OR -10D ACCORDING TO WHETHER THE EARTH OR MOON,
#							RESPECTIVELY, IS THE CENTRAL BODY.
#
# SUBROUTINES CALLED --
# Page 1270
#	PARAM, GEOM, GETX, DELTIME, NEWSTATE
#
# CALLING SEQUENCE AND NORMAL EXIT MODES --
#	IF ONLY TIME IS DESIRED AS OUTPUT --
#	L	SET	CALL		# MUST BE IN INTERPRETIVE MODE BUT OVFIND ARBITRARY.
#	L+1		RVSW
#	L+2		TIMETHET	# RETURN WITH PL AT 0 AND T IN MPAC
#	L+3	...			# CONTINUE
#
#	IF THE UPDATE STATE VECTOR IS DESIRED AS WELL --
#	L	CLEAR	CALL		# MUST BE IN INTERPRETIVE MODE BUT OVFIND ARBITRARY.
#	L+1		RVSW
#	L+2		TIMETHET	# RETURNS WITH PL AT 6.  THE INITIAL POSITION VECTOR IS IN 0D OF THE PUSHLIST AND
#					# THE INITIAL VELOCITY VECTOR IN MPAC.
#	L+3	STOVL	NEWVVEC
#	L+4	STADR
#	L+5	STORE	NEWRVEC		# NEWVVEC AND NEWRVEC ARE SYMBOLIC REPRESENTATIONS OF THE USERS LOCATIONS.
#	L+6	...			# CONTINUE.
#
# ABORT EXIT MODES --
#	IF COGAFLAG AND/OR INFINFLG IS SET AT THE EXIT TO TIME-THETA, TIME-THETA WILL TRANSFER TO POODOO WITH
#	AN ALARM CODE (ORIGINALLY 00607), AND NOT RETURN TO THE CALLING PROGRAM.  (PCR 692 AND 721).
#
# OUTPUT --
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	T (30D)		+28			DP TRANSFER TIME IN CENTISECONDS
#	INFINFLG	NONE			AN INTERPRETIVE SWITCH WHICH IS SET IF THE TRANSFER ANGLE REQUIRES CLOSURE THROUGH
#							INFINITY (NO SOLUTION), CLEAR IF A PHYSICAL SOLUTION IS POSSIBLE.
#	COGAFLAG	NONE			AN INTERPRETIVE SWITCH WHICH IS SET IF RESTRICTION 1 HAS BEEN VIOLATED (NO SOLUTION),
#							CLEAR IF NOT.
#
#	IN ADDITION, IF RVSW IS CLEAR, THE FOLLOWING ARE OUTPUT --
#	MPAC - 		+7 FOR EARTH		DP TERMINAL VELOCITY VECTOR IN METERS/CENTISEC.
#	   MPAC+5	+5 FOR MOON
#	0D - 5D		+29 FOR EARTH		DP TERMINAL POSITION VECTOR IN METERS (PL AT 6D)
#			+27 FOR MOON
#
#	FOR OTHER OUTPUT WHICH MAY BE OF USE, SEE DEBRIS.
# Page 1271
#
# DEBRIS --
#	PARAMETERS WHICH MAY BE OF USE --
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	R1 (32D)	+29 FOR EARTH		DP MAGNITUDE OF INITIAL POSITION VECTOR, RVEC, IN METERS
#			+27 FOR MOON
#	R1A		+6			DP RATIO OF R1 TO SEMIMAJOR AXIS (NEG. FOR HYPERBOLIC TRAJECTORIES)
#	P		+4			DP RATIO OF SEMILATUS RECTUM TO R1
#	COGA		+5			DP COTAN OF ANGLE BETWEEN RVEC AND VVEC
#	UR1		+1			DP UNIT VECTOR OF RVEC
#	U2		+1			DP UNIT VECTOR OF VVEC
#	UN		+1			DP UNIT VECTOR OF UR1*U2
#
# PARAMETERS OF NO USE --
#	SP PARAMETERS -- RTNTT, GEOMSGN, RTNPRM, MAGVEC2=R2 (DP), PLUS PUSHLIST LOCATIONS 0-11D, 14D-21D, 24D-39D, 41D
#	ADDITIONAL INTERPRETIVE SWITCHES USED -- NORMSW, 360SW

# Page 1272
# PROGRAM DESCRIPTION -- TIME-RADIUS SUBROUTINE		DATE -- 11 OCTOBER 1967
# MOD NO. -1						LOG SECTION -- CONIC SUBROUTINES
# MOD BY KRAUSE						ASSEMBLY -- COLOSSUS REVISION 88
#
# FUNCTIONAL DESCRIPTION --
#	THIS SUBROUTINE, GIVEN AN INITIAL STATE VECTOR AND A DESIRED RADIUS TO WHICH THE
#	STATE IS TO BE UPDATED ALONG A CONIC TRAJECTORY, CALCULATES THE CORRESPONDING TIME-OF-FLIGHT AND, IN ADDITION,
#	PROVIDES THE OPTION OF COMPUTING THE NEW UPDATED STATE VECTOR.  THE RESULTING TRAJECTORY MAY BE A SECTION OF A
#	CIRCLE, ELLIPSE, PARABOLA, OR HYPERBOLA WITH RESPECT TO THE EARTH OR THE MOON.  THE USE OF THE SUBROUTINE CAN BE
#	EXTENDED USING OTHER PRIMARY BODIES BY SIMPLE ADDITIONS TO THE MUTABLE WITHOUT INTRODUCING ANY CODING CHANGES,
#	ACCEPTING THE INHERENT SCALE FACTOR CHANGES IN POSITION AND VELOCITY.
#
#	IF THE DESIRED RADIUS IS BEYOND THE RADIUS OF APOCENTER OF THE CONIC OR BELOW THE RADIUS OF PERICENTER,
#	APSESW WILL BE SET AND THE SUBROUTINE WILL RETURN THE APOCENTER OR PERICENTER SOLUTION, RESPECTIVELY.
#
# THE RESTRICTIONS ARE --
#	1. THE ANGLE BETWEEN ANY POSITION VECTOR AND ITS VELOCITY VECTOR MUST BE GREATER THAN 1 DEGREE 47.5 MINUTES
#	   AND LESS THAN 178 DEGREES 12.5 MINUTES.
#	2. THE PARAMETERS IN THE PROBLEM MUST NOT EXCEED THEIR SCALING LIMITS SPECIFIED IN THE GSOP.  IF THE LIMITS
#	   EXCEEDED, THE RESULTING SOLUTION WILL BE MEANINGLESS.
#	3. AN ACCURACY DEGRADATION OCCURS AS THE SENSITIVITIES OF TIME AND UPDATED STATE VECTOR TO CHANGES IN
#	   RDESIRED INCREASE.  THIS WILL OCCUR NEAR EITHER APSIS OF THE CONIC AND WHEN THE CONIC IS NEARLY CIRCULAR.  IN
#	   PARTICULAR, IF THE CONIC IS AN EXACT CIRCLE, THE PROBLEM IS UNDEFINED AND THE SUBROUTINE WILL ABORT.
#
#	THE AGC COMPUTATION TIME IS APPROXIMATELY .363 SECONDS.
#
# REFERENCES --
#	R-479, MISSION PROGRAMMING DEFINITION MEMO NO. 10, LUNAR LANDING MISSION GSOP-SECTION 5.5, SGA MEMO 67-8.
#
# INPUT -- ERASABLE INITIALIZATION REQUIRED.
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	RVEC		+29 FOR EARTH		DP INITIAL POSITION VECTOR IN METERS
#			+27 FOR MOON
#	VVEC		+7 FOR EARTH		DP INITIAL VELOCITY VECTOR IN METERS/CENTISECOND
#			+5 FOR MOON
#	RDESIRED	+29 FOR EARTH		DP TERMINAL RADIAL DISTANCE ON CONIC TRAJECTORY FOR WHICH TRANSFER TIME IS TO BE
#			+27 FOR MOON			COMPUTED
#	SGNRDOT		NONE			SP TAG SET TO +.5 OR -.5 ACCORDING TO WHETHER THE RADIAL VELOCITY AT RDESIRED IS TO BE
#							POSITIVE OR NEGATIVE, RESPECTIVELY.   THIS TAG REDUCES THE DOUBLE-VALUED PROBLEM TO A
# Page 1273
#							SINGLE-VALUED PROBLEM.
#	X1 (38D)	NONE			INDEX REGISTER TO BE SET TO -2D OR -10D ACCORDING TO WHETHER THE EARTH OR MOON,
#							RESPECTIVELY, IS THE CENTRAL BODY.
#	RVSW		NONE			AN INTERPRETIVE SWITCH TO BE SET IF ONLY TIME IS TO BE AN OUTPUT, CLEAR IF THE NEW STATE
#							IS TO BE COMPUTED ALSO.
#
# SUBROUTINES CALLED --
#	PARAM, GEOM, GETX, DELTIME, NEWSTATE
#
# CALLING SEQUENCE AND NORMAL EXIT MODES --
#	IF ONLY TIME IS DESIRED AS OUTPUT --
#	L	SET	CALL		# MUST BE IN INTERPRETIVE MODE BUT OVFIND ARBITRARY.
#	L+1		RVSW
#	L+2		TIMERAD		# RETURN WITH PL AT 0 AND T IN MPAC
#	L+3	...			# CONTINUE
#
#	IF THE UPDATE STATE VECTOR IS DESIRED AS WELL --
#	L	CLEAR	CALL		# MUST BE IN INTERPRETIVE MODE BUT OVFIND ARBITRARY.
#	L+1		RVSW
#	L+2		TIMERAD		# RETURNS WITH PL AT 6.  THE INITIAL POSITION VECTOR IS IN 0D OF THE PUSHLIST AND
#					# THE INITIAL VELOCITY VECTOR IN MPAC.
#	L+3	STOVL	NEWVVEC
#	L+4	STADR
#	L+5	STORE	NEWRVEC		# NEWVVEC AND NEWRVEC ARE SYMBOLIC REPRESENTATIONS OF THE USERS LOCATIONS.
#	L+6	...			# CONTINUE
#
# ABORT EXIT MODES --
#	IF SOLNSW AND/OR COGAFLAG AND/OR INFINFLG IS SET AT THE EXIT TO TIME-RADIUS, TIME-RADIUS WILL TRANSFER
#	TO POODOO WITH AN ALARM CODE (ORIGINALLY 00607), AND NOT RETURN TO THE CALLING PROGRAM.  (PCR 692 & 721).
#
# OUTPUT --
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	T (30D)		+28			DP TRANSFER TIME IN CENTISECONDS.
#	INFINFLG	NONE			AN INTERPRETIVE SWITCH WHICH IS SET IF RDESIRED AND SGNRDOT REQUIRE CLOSURE THROUGH
#							INFINITY (NO SOLUTION), CLEAR IF A PHYSICAL SOLUTION IS POSSIBLE.
#	COGAFLAG	NONE			AN INTERPRETIVE SWITCH WHICH IS SET IF RESTRICTION 1 HAS BEEN VIOLATED (NO SOLUTION),
#							CLEAR IF NOT.
#	APSESW		NONE			AN INTERPRETIVE SWITCH WHICH IS SET IF RDESIRED WAS GREATER THAN RADIUS OF APOCENTER OR
# Page 1274
#							LESS THAN RADIUS OF PERICENTER.  THE APOCENTER OR PERICENTER SOLUTION, RESPECTIVELY,
#							WILL THEN BE RETURNED.  THE SWITCH IS CLEAR IF RDESIRED WAS BETWEEN PERICENTER AND
#							APOCENTER.
#	SOLNSW		NONE			AN INTERPRETIVE SWITCH WHICH IS SET IF THE CONIC IS SO CLOSE TO A CIRCLE THAT THE TERMIN
#							POINT IS AMBIGUOUS, VIOLATING RESTRICTION 3.  IF ECCENTRICITY IS GREATER THAN 2-TO-THE-
#							MINUS-18, THE SWITCH IS CLEAR.
#
#	IN ADDITION, IF RVSW IS CLEAR, THE FOLLOWING ARE OUTPUT --
#	MPAC - 		+7 FOR EARTH		DP TERMINAL VELOCITY VECTOR IN METERS/CENTISEC.
#	   MPAC+5	+5 FOR MOON
#	0D - 5D		+29 FOR EARTH		DP TERMINAL POSITION VECTOR IN METERS (PL AT 6D)
#			+27 FOR MOON
#
#	FOR OTHER OUTPUT WHICH MAY BE OF USE, SEE DEBRIS.
#
# DEBRIS --
#	PARAMETERS WHICH MAY BE OF USE --
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	R1 (32D)	+29 FOR EARTH		DP MAGNITUDE OF INITIAL POSITION VECTOR, RVEC, IN METERS
#			+27 FOR MOON
#	R1A		+6			DP RATIO OF R1 TO SEMIMAJOR AXIS (NEG. FOR HYPERBOLIC TRAJECTORIES)
#	P		+4			DP RATIO OF SEMILATUS RECTUM TO R1
#	COGA		+5			DP COTAN OF ANGLE BETWEEN RVEC AND VVEC
#	UR1		+1			DP UNIT VECTOR OF RVEC
#	U2		+1			DP UNIT VECTOR OF VVEC
#	UN		+1			DP UNIT VECTOR OF UR1*U2
#	CSTH		+1			DP COSINE OF TRUE ANOMALY DIFFERENCE BETWEEN RVEC AND RDESIRED.
#	SNTH		+1			DP SINE OF TRUE ANOMALY DIFFERENCE.
#
# 	PARAMETERS OF NO USE --
#		SP PARAMETERS -- RTNTT, GEOMSGN, RTNPRM, MAGVEC2=R2 (DP), PLUS PUSHLIST LOCATIONS 0-11D, 14D-21D, 24D-39D, 41D
#		ADDITIONAL INTERPRETIVE SWITCHES USED -- NORMSW, 360SW
#

# Page 1275
# PROGRAM DESCRIPTION -- APSIDES SUBROUTINE		DATE -- 1 SEPTEMBER 1967
# MOD NO. -- 0						LOG SECTION -- CONIC SUBROUTINES
# MOD BY KRAUSE						ASSEMBLY -- COLOSSUS REVISION 88
#
# FUNCTIONAL DESCRIPTION --
#	THIS SUBROUTINE, GIVEN AN INITIAL STATE VECTOR, CALCULATES THE RADIUS OF PERICENTER AND OF APOCENTER AND THE
#	ECCENTRICITY OF THE RESULTING CONIC TRAJECTORY, WHICH MAY BE A STRAIGHT LINE,
#	CIRCLE, ELLIPSE, PARABOLA, OR HYPERBOLA WITH RESPECT TO THE EARTH OR THE MOON.  THE USE OF THE SUBROUTINE CAN
#	BE EXTENDED USING OTHER PRIMARY BODIES BY SIMPLE ADDITIONS TO THE MUTABLE WITHOUT INTRODUCING ANY CODING CHANGES,
#	ACCEPTING THE INHERENT SCALE FACTOR CHANGES IN POSITION AND VELOCITY.
#
# THE RESTRICTIONS ARE --
#	1. IF APOCENTER IS BEYOND THE SCALING OF POSITION, THE SCALE FACTOR LIMIT (536,870,910 METERS WITH RESPECT
#	   TO THE EARTH OR 134,217,727.5 METERS WITH RESPECT TO THE MOON) WILL BE RETURNED.
#	2. THE PARAMETERS IN THE PROBLEM MUST NOT EXCEED THEIR SCALING LIMITS SPECIFIED IN THE GSOP.  IF THE LIMITS
#	   ARE EXCEEDED, THE RESULTING SOLUTION WILL BE MEANINGLESS.
#
#	THE AGC COMPUTATION TIME IS APPROXIMATELY .103 SECONDS.
#
# REFERENCES --
#	MISSION PROGRAMMING DEFINITION MEMO NO. 10, LUNAR LANDING MISSION GSOP-SECTION 5.5.
#
# INPUT -- ERASABLE INITIALIZATION REQUIRED
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	RVEC		+29 FOR EARTH		DP INITIAL POSITION VECTOR IN METERS
#			+27 FOR MOON
#	VVEC		+7 FOR EARTH		DP INITIAL VELOCITY VECTOR IN METERS/CENTISECOND
#			+5 FOR MOON
#	X1 (38D)	NONE			INDEX REGISTER TO BE SET TO -2D OR -10D ACCORDING TO WHETHER THE EARTH OR MOON,
#							RESPECTIVELY, IS THE CENTRAL BODY.
#
# SUBROUTINES CALLED --
#	PARAM, GEOM
#
# CALLING SEQUENCE AND NORMAL EXIT MODES --
# Page 1276
#	IF ONLY TIME IS DESIRED AS OUTPUT --
#	L	CALL			# MUST BE IN INTERPRETIVE MODE BUT OVFIND ARBITRARY.
#	L+1		APSIDES		# RETURNS WITH PL AT 0, RADIUS OF APOCENTER IN MPAC AND RADIUS OF PERICENTER IN 0D
#	L+2	STODL	APOAPSE
#	L+3		0D
#	L+4	STORE	PERIAPSE	# APOAPSE AND PERIAPSE ARE SYMBOLIC REPRESENTATIONS OF THE USERS LOCATIONS
#	L+5	...			# CONTINUE
#
# OUTPUT --
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	MPAC		+29 FOR EARTH		DP RADIUS OF APOCENTER IN METERS
#			+27 FOR MOON
#	0D-1D		+29 FOR EARTH		DP RADIUS OF PERICENTER IN METERS
#			+27 FOR MOON
#	ECC		+3			DP ECCENTRICITY OF CONIC TRAJECTORY
#
#	FOR OTHER OUTPUT WHICH MAY BE OF USE, SEE DEBRIS.
#
# DEBRIS --
#	PARAMETERS WHICH MAY BE OF USE --
#			 SCALE FACTOR
#	VARIABLE	IN POWERS OF 2		DESCRIPTION AND REMARKS
#	--------	--------------		-----------------------
#	R1 (32D)	+29 FOR EARTH		DP MAGNITUDE OF INITIAL POSITION VECTOR, RVEC, IN METERS
#			+27 FOR MOON
#	R1A		+6			DP RATIO OF R1 TO SEMIMAJOR AXIS (NEG. FOR HYPERBOLIC TRAJECTORIES)
#	P		+4			DP RATIO OF SEMILATUS RECTUM TO R1
#	COGA		+5			DP COTAN OF ANGLE BETWEEN RVEC AND VVEC
#	UR1		+1			DP UNIT VECTOR OF RVEC
#	U2		+1			DP UNIT VECTOR OF VVEC
#	UN		+1			DP UNIT VECTOR OF UR1*U2
#	MAGVEC2		+7 FOR EARTH		DP MAGNITUDE OF VVEC
#			+5 FOR MOON
#
#	PARAMETERS OF NO USE --
#		SP PARAMETERS -- RTNAPSE, GOMSGN, RTNPRM, PLUS PUSHLIST LOCATIONS 0-5, 10D-11D, 14D-21D, 31D-38D.
#		ADDITIONAL INTERPRETIVE SWITCHES USED -- NORMSW

		SETLOC	CONICS
# Page 1277
		BANK

		COUNT	12/CONIC

		EBANK=	UR1
KEPLERN		SETPD	BOV
			0
			+1
		VLOAD*
			MUTABLE,1
		STOVL	14D
			RRECT
		UNIT	SSP
			ITERCTR
			20D
		STODL	URRECT
			36D
		STOVL	R1
			RRECT
		DOT	SL1R
			VRECT
		DMP	SL1R
			1/ROOTMU	# 1/ROOTMU (-17 OR -14)
		STOVL	KEPC1		# C1=R.V/ROOTMU (+17 OR +16)

			VRECT
		VSQ	DMPR
			1/MU		# 1/MU (-34 OR -28)
		DMP	SL3
			R1
		DSU	ROUND
			D1/64
		STORE	KEPC2		# C2=RV.V/MU -1 (+6)

		BDSU	SR1R
			D1/64
		DDV
			R1
		STORE	ALPHA		# ALPHA=(1-C2)/R1 (-22 OR -20)

		BPL	DLOAD		# MAXIMUM X DEPENDS ON TYPE OF CONIC
			1REV
			-50SC		# -50SC (+12)
		DDV	BOV
			ALPHA
			STOREMAX
		SQRT	GOTO
			STOREMAX
# Page 1278
1REV		SQRT	BDDV
			2PISC		# 2PISC (+6)
		BOV
			STOREMAX
STOREMAX	STORE	XMAX
		DMP	PDDL
			1/ROOTMU
			ALPHA
		NORM	PDDL
			X1
		SL*	DDV
			0 	-6,1
		BOV	BMN
			MODDONE
			MODDONE		# MPAC=PERIOD
PERIODCH	PDDL	ABS		# 0D=PERIOD
			TAU.
		DSU	BMN
			0D
			MODDONE
		SIGN
			TAU.
		STODL	TAU.
		GOTO
			PERIODCH
MODDONE		SETPD	DLOAD
			0
			XKEPNEW
		STORE	X
		SIGN	BZE
			TAU.
			BADX
		BMN	ABS
			BADX
		DSU	BPL
			XMAX
			BADX
STORBNDS	DLOAD	BPL
			TAU.
			STOREMIN
		DLOAD	DCOMP
			XMAX
		STODL	XMIN
			KEPZERO
		STCALL	XMAX
			DXCOMP
STOREMIN	DLOAD
			KEPZERO
		STORE	XMIN
DXCOMP		DLOAD	DMPR
# Page 1279
			TAU.
			BEE22
		ABS
		STODL	EPSILONT
			XPREV
XDIFF		BDSU
			X
		STORE	DELX

KEPLOOP		DLOAD	DSQ
			X		# X=XKEP
		NORM	PUSH		# 0D=XSQ (+34 OR +32 -N1)	PL AT 2
			X1
		DMP	SRR*
			ALPHA
			0 	-6,1
		STCALL	XI		# XI=ALPHA XSQ (+6)
			DELTIME
		BOV	BDSU
			TIMEOVFL	# UNLIKELY
			TAU.
		STORE	DELT		# DELT=DELINDEP
		ABS	BDSU
			EPSILONT
		BPL	DLOAD
			KEPCONVG
			T
		DSU	NORM
			TC
			X1
		PDDL	NORM
			DELX
			X2
		XSU,1	DMP
			X2
			DELT
		SLR*	DDV
			1,1
		SR1	PUSH		# 0D=TRIAL DELX		PL AT 2
		BPL	DLOAD
			POSDELX
			X
		STORE	XMAX		# MOVE MAX BOUND IN
		BDSU	DSU		#			PL AT 0
			XMIN
		BOV	BPL
			NDXCHNGE
			NDXCHNGE
		DLOAD	GOTO
# Page 1280
			0D
			NEWDELX

NDXCHNGE	DLOAD	DSU
			XMIN
			X
		DMPR	GOTO		# TO FORCE MPAC +2 TO ZERO
			DP9/10
			NEWDELX

POSDELX		DLOAD
			X
		STORE	XMIN		# MOVE MIN BOUND IN
		BDSU	DSU		#			PL AT 0
			XMAX
		BOV	BMN
			PDXCHNGE
			PDXCHNGE
		DLOAD
			0D
NEWDELX		STORE	DELX
		BZE	DAD
			KEPCONVG
			X
		STODL	X
			T
		STORE	TC
BRNCHCTR	RTB	BHIZ
			CHECKCTR
			KEPCONVG
		GOTO
			KEPLOOP		# ITERATE

PDXCHNGE	DLOAD	DSU
			XMAX
			X
		DMPR	GOTO		# TO FORCE MPAC +2 TO ZERO
			DP9/10
			NEWDELX

BADX		DLOAD	SR1
			XMAX
		SIGN
			TAU.
		STCALL	X
			STORBNDS
# Page 1281
TIMEOVFL	DLOAD	BMN		# X WAS TOO BIG
			X
			NEGTOVFL
		STORE	XMAX
CMNTOVFL	DLOAD	SR1
			DELX
		STORE	DELX
		BZE	BDSU
			KEPRTN
			X
		STODL	X
			TC
		STCALL	T
			BRNCHCTR
NEGTOVFL	STCALL	XMIN
			CMNTOVFL
KEPCONVG	DLOAD	SR4R
			R1
		DSU	VXSC
			XSQC(XI)
			URRECT
		VSL1	PDDL		# 0D=(R1-XSQC(XI))URRECT (+33 OR +31)
			X
		DSQ	NORM
			X1
		DMPR	DMPR
			1/ROOTMU
			X
		DMP	SRR*
			S(XI)
			0 	-7,1
		BDSU
			T
		SL1	VXSC
			VRECT
		VSL1	VAD		#				PL AT 0
		VSL4
		STORE	RCV		# RCV (+29 OR +27)

		ABVAL	NORM
			X2
		STODL	RCNORM
			XI
		DMPR	DSU
			S(XI)
			D1/128
		DMP	SL1R
			ROOTMU
		DMP	SLR*
# Page 1282
			X
			0 	-3,2
		DDV	VXSC
			RCNORM
			URRECT
		VSL1	PDDL		# 0D=URRECT(XI S(XI)-1)X ROOTMU/RCV (+15
			XSQC(XI)	# OR +13)			PL AT 6
		SLR*	DDV
			0 	-4,2
			RCNORM
		BDSU	VXSC
			D1/256
			VRECT
		VAD	VSL8
		STADR			#				PL AT 0
		STODL	VCV		# VCV (+7 OR +5)
			T
		STODL	TC
			X
		STCALL	XPREV
			KEPRTN

# Page 1283
DELTIME		EXIT			# MPAC=XI (+6), 0D=XSQ (+34 OR +32 -N1)
		TC	POLY
		DEC	8
		2DEC	.083333334

		2DEC	-.266666684

		2DEC	.406349155

		2DEC	-.361198675

		2DEC	.210153242

		2DEC	-.086221951

		2DEC	.026268812

		2DEC	-.006163316

		2DEC	.001177342

		2DEC	-.000199055

		TC	INTPRET
		STODL	S(XI)
			XI
		EXIT
		TC	POLY
		DEC	8
		2DEC	.031250001

		2DEC	-.166666719

		2DEC	.355555413

		2DEC	-.406347410

		2DEC	.288962094

		2DEC	-.140117894

		2DEC	.049247387

		2DEC	-.013081923

		2DEC	.002806389

		2DEC	-.000529414

		TC	INTPRET
# Page 1284
		DMP	SRR*		#				PL AT 0
			0D
			0 	-5,1
		STORE	XSQC(XI)	# XSQC(XI) (+33 OR +31)
		DMP	SL1
			KEPC1
		RTB	PDDL		# XCH WITH PL. 0D=C1 XSQ C(XI) (+49 OR +46)
			TPMODE		#				PL AT 0,3
		DMP	SRR*
			S(XI)
			0 	-5,1
		DMP	SL1
			KEPC2
		RTB	PDDL		# 3D=C2 XSQ S(XI) (+35 OR +33) 	PL AT 6
			TPMODE
			R1
		SR	TAD		#				PL AT 3
			6
		NORM	DMP		# TO PRESERVE SIGNIF.
			X1
			X
		SR*	TAD		# X(C2 XSQ S(XI) +R1) (+49 OR +46)  PL AT 0
			0 	-3,1
		SL4R	DMPR
			1/ROOTMU
		STORE	T
		RVQ

# Page 1285
ITERATOR	BONCLR	DLOAD
			SLOPESW
			FIRSTIME
			DEP
		DSU	NORM
			DEPREV
			X1
		PDDL	NORM
			DELINDEP
			X2
		XSU,1	DMP
			X2
			DELDEP
		SLR*	DDV		#				PL UP 2
			1,1
		SR1	BOFF
			ORDERSW
			SGNCHECK
		ABS	SIGN		# IN CASE 2ND DERIV. CHANGED SIGN, MUST
			DELDEP		# DISREGARD IT TO FIND MIN.

SGNCHECK	PUSH	BPL		# TRIAL DELINDEP		PL DOWN 2
			POSDEL
		DLOAD	BON
			INDEP
			ORDERSW
			MINCHECK
		STORE	MAX		# IF NOT 2ND ORDER, CAN MOVE MAX BOUND IN.

MINCHECK	BDSU	DSU
			MIN
		BOV	BPL
			MODNGDEL
			MODNGDEL
		GOTO
			DELOK

MODNGDEL	DLOAD	DSU		# TRIAL DELINDEP WOULD EXCEED MIN BOUND
			MIN
			INDEP
		DMP	GOTO
			DP9/10
			NEWDEL

FIRSTIME	DLOAD	DMP
			MIN
			TWEEKIT		# DLOAD TWEEKIT(40D) SENSITIVE TO CHANGE.
		PDDL	DMP		# S2(41D) SHOULDNT CONTAIN HI ORDER ONES
# Page 1286
			MAX
			TWEEKIT
		DSU
		SIGN	GOTO
			DELDEP
			SGNCHECK

POSDEL		DLOAD	BON
			INDEP
			ORDERSW
			MAXCHECK
		STORE	MIN		# IF NOT 2ND ORDER, CAN MOVE MIN BOUND IN.

MAXCHECK	BDSU	DSU
			MAX
		BOV	BMN
			MODPSDEL
			MODPSDEL
DELOK		DLOAD
			0D
NEWDEL		STORE	DELINDEP
		RVQ

MODPSDEL	DLOAD	DSU
			MAX
			INDEP
		DMP	GOTO
			DP9/10
			NEWDEL

CHECKCTR	CS	ONE
		INDEX	FIXLOC
		AD	ITERCTR
		INDEX	FIXLOC
		TS	ITERCTR
		TS	MPAC
		TC	DANZIG

# Page 1287
NEWSTATE	DLOAD	SR4R
			R1
		DSU	VXSC
			XSQC(XI)
			UR1
		VSL1	PDDL		# 0D=(R1-XSQC(XI))UR1 (+33 OR 31) PL AT 6
			X
		DSQ	NORM
			X1
		DMPR	DMPR
			1/ROOTMU
			X
		DMP	SRR*
			S(XI)
			0 	-7,1
		BDSU
			T
		SL1	VXSC
			VVEC
		VSL1	VAD		#				PL AT 0
		VSL4	PUSH
		ABVAL
LAMENTER	NORM
			X1
		STODL	R2
			XI
		DMP	DSU
			S(XI)
			D1/128
		DMP	SL1R
			ROOTMU
		DMP	SLR*
			X
			0 -3,1
		DDV	VXSC
			R2
			UR1
		VSL1	PDDL		# 6D=V2VEC PART (+15 OR 13)	PL AT 12
			XSQC(XI)
		SLR*	DDV
			0 -4,1
			R2
		BDSU
			D1/256
		VXSC	VAD		#				PL AT 6
			VVEC
		VSL8	RVQ

# Page 1288
		SETLOC	CONICS1
		BANK

		COUNT	04/CONIC
# DO NOT DISTURB THE ORDER OF THESE CDS, OVERLAYS HAVE BEEN MADE.
BEE17		DEC	0		# KEEP WITH D1/8 2DEC 1.0B-17 (**********)
D1/8		2DEC	1.0 B-3

D1/128		2DEC	1.0 B-7

D1/64		2DEC	1.0 B-6

D1/4		2DEC	1.0 B-2

D1/16		2DEC	1.0 B-4

D1/32		2DEC	1.0 B-5

D1/1024		2DEC	1.0 B-10

D1/256		2DEC	1.0 B-8

DP9/10		2DEC	.9

KEPZERO		EQUALS	LO6ZEROS
-50SC		2DEC	-50.0 B-12

2PISC		2DEC	6.******** B-6

BEE19		EQUALS	D1/32 -1	# 2DEC 1.0 B-19 (00000 01000)
BEE22		EQUALS	D1/256 -1	# 2DEC 1.0 B-22 (00000 00100)
ONEBIT		2DEC	1.0 B-28

COGUPLIM	2DEC	.*********

COGLOLIM	2DEC	-.*********

# Page 1289
		SETLOC	CONICS
		BANK

		COUNT	12/CONIC

TIMETHET	STQ	SETPD		#				PL AT 0
			RTNTT
			0
		BOV
			+1
		VLOAD	PDVL		# SETUP FOR PARAM CALL		PL AT 6
			RVEC
			VVEC
		CALL
			PARAM
		BOV	CALL		#				PL AT 0
			COGAOVFL
			GETX
COMMNOUT	DLOAD	BON
			XI
			INFINFLG
			ABTCONIC
		CLEAR	CALL
			COGAFLAG
			DELTIME
		BON	CALL
			RVSW
			RTNTT
			NEWSTATE
		GOTO
			RTNTT

COGAOVFL	SETGO
			COGAFLAG
			ABTCONIC
		BANK	4
		SETLOC	CONICS1
		BANK
		COUNT*	$$/CONIC
PARAM		STQ	CLEAR		# MPAC=V1VEC, 0D=R1VEC		PL AT 6
			RTNPRM
			NORMSW
		CLEAR
			COGAFLAG
		SSP	CALL
			GEOMSGN
			37777		# GAMMA ALWAYS LESS THAN 180DEG
			GEOM		# MPAC=SNGA (+1), 0D=CSGA (+1)	PL AT 2
		STODL	36D		# 36D=SIN GAMMA (+1)		PL AT 0
# Page 1290
		SR	DDV
			5

			36D
		STOVL*	COGA
			MUTABLE,1
		STODL	1/MU
			MAGVEC2
		DSQ	NORM
			X1
		DMPR	DMP
			1/MU
			R1
		SRR*
			0 	-3,1
		PUSH	BDSU		# 0D=R1 V1SQ/MU (+6)		PL AT 2
			D1/32
		STODL	R1A		# R1A (+6)			PL AT 0

		DMP	NORM
			36D
			X1
		DMP	SR*
			36D
			0 	-4,1
		STCALL	P		# P (+4)
			RTNPRM

# Page 1291
GEOM		UNIT			# MPAC=V2VEC, 0D=R1VEC		PL AT 6
		STODL	U2		# U2 (+1)
			36D
		STOVL	MAGVEC2		#				PL AT 0
		UNIT
		STORE	UR1		# UR1 (+1)
		DOT	SL1
			U2
		PDDL			# 0D=CSTH (+1)			PL AT 2
			36D
		STOVL	R1		# R1 (+29 OR +27)
			UR1
		VXV	VSL1
			U2
		BON	SIGN
			NORMSW
			HAVENORM
			GEOMSGN
		UNIT	BOV
			COLINEAR
UNITNORM	STODL	UN		# UN (+1)
			36D
		SIGN	RVQ		# MPAC=SNTH (+1), 34D=SNTH.SNTH (+2)
			GEOMSGN

COLINEAR	VSR1	GOTO
			UNITNORM

HAVENORM	ABVAL	SIGN
			GEOMSGN
		RVQ			# MPAC=SNTH (+1), 34D=SNTH.SNTH (+2)

# Page 1292
		BANK	12
		SETLOC	CONICS
		BANK

		COUNT	12/CONIC

GETX		AXT,2	SSP		# ASSUMES P (+4) IN MPAC
			3
			S2
			1
		CLEAR
			360SW
		SQRT	PDDL		# 0D=SQRT(P)			PL AT 2
			CSTH
		SR1	BDSU
			D1/4
		PDDL	SRR		#				PL AT 4D
			SNTH
			6
		DDV			#				PL AT 2
		BOV
			360CHECK
		DSU	DMP
			COGA		#				PL AT 0
		SL2R	BOV
			360CHECK
WLOOP		PUSH	DSQ		# 0D=W (+5)			PL AT 2
		TLOAD	PDDL		# 2D=WSQ (+10)			PL AT 5
			MPAC
			R1A
		SR4	TAD		#				PL AT 2
		BMN	SQRT
			INFINITY
		ROUND	DAD		#				PL AT 0D
		BOV	TIX,2
			RESETX2
			WLOOP

		BDDV	BOV
			D1/128
			INFINITY
POLYCOEF	BMN	PUSH		# 0D=1/W (+2) OR 16/W (+6)	PL AT 2
			INFINITY
		DSQ
		NORM	DMP
			X1
			R1A
		SRR*	EXIT
			0 	-10D,1
# Page 1293
		TC	POLY
		DEC	5
		2DEC	.5

		2DEC	-.*********

		2DEC	.*********

		2DEC	-.*********

		2DEC	.*********

		2DEC	-.*********

		2DEC	.*********

		TC	INTPRET
		DMP	SL1R		#				PL AT 0D
		PUSH	BON
			360SW
			TRUE360X
XCOMMON		DSQ	NORM
			X1
		DMP	SRR*
			R1A
			0 	-12D,1
		STODL	XI		# XI (+6)

			R1
		SR1	SQRT
		ROUND	DMP
		SL4R			#				PL AT 0
		STORE	X		# X (+17 OR +16)

		DSQ	NORM
			X1
		PDDL	DMP		# 0D=XSQ (+34 OR +32 -N1)	PL AT 2
			P
			R1
		SL3	SQRT
		DMP	SL3R
			COGA
		STODL	KEPC1
			R1A
		BDSU	CLEAR
			D1/64
			INFINFLG
		STORE	KEPC2
# Page 1294
		RVQ

RESETX2		AXT,2
			3

360CHECK	SETPD	BPL
			0D
			INVRSEQN
		SET
			360SW

INVRSEQN	DLOAD	SQRT
			P
		PDDL	DMP		# 0D=SQRT(P) (+2)		PL AT 2
			SNTH
			COGA
		SL1	PDDL		# 2D=SNTH COGA (+5)		PL AT 4
			CSTH
		SR4	DAD
			D1/32
		DSU	DMP		#				PL AT 2,0
		NORM	BDDV
			X1
			SNTH
		SLR*	ABS		# NOTE: NEAR 360 CASE TREATED DIFFERENTLY
			0 	-5,1
		PUSH	DSQ		# 0D=1/W (-1)			PL AT 2
		STODL	34D
			D1/16
1/WLOOP		PUSH	DSQ		# 2D=G (+4)			PL AT 4
		RTB	PDDL		#				PL AT 7
			TPMODE
			R1A
		DMP	SR4
			34D
		TAD			#				PL AT 4
		BMN	SQRT
			INFINITY
		DAD			#				PL AT 2
		TIX,2	NORM
			1/WLOOP
			X1
		BDDV			#				PL AT 0
		SLR*	GOTO
			0 	-7,1
			POLYCOEF

# Page 1295
TRUE360X	DLOAD	BMN
			R1A
			INFINITY
		SQRT	NORM
			X1
		BDDV	SL*
			2PISC
			0 	-3,1
		DSU	PUSH		# 0D=2PI/SQRT(R1A) -X		PL AT 0,2
		GOTO
			XCOMMON
INFINITY	SETPD	BOV		# NO SOLUTION EXISTS SINCE CLOSURE THROUGH
			0		# INFINITY IS REQUIRED
			OVFLCLR
OVFLCLR		SET	RVQ
			INFINFLG

# Page 1296
LAMBERT		STQ	SETPD
			RTNLAMB
			0D
		BOV
			+1
		SSP	VLOAD*
			ITERCTR
			20D
			MUTABLE,1
		STODL	1/MU
			TDESIRED
		DMPR
			BEE19
		STORE	EPSILONL
		SET	VLOAD
			SLOPESW
			R1VEC
		PDVL	CALL		# 0D=R1VEC (+29 OR +27)		PL AT 6
			R2VEC		# MPAC=R2VEC (+29 OR +27)
			GEOM
		STODL	SNTH		# 0D=CSTH (+1)			PL AT 2
			MAGVEC2
		NORM	PDDL		#				PL AT 4
			X1
			R1
		SR1	DDV		#				PL AT 2
		SL*	PDDL		# DXCH WITH 0D, 0D=R1/R2 (+7)	PL AT 0,2
			0 	-6,1
		STADR
		STORE	CSTH		# CSTH (+1)
		SR1	BDSU
			D1/4
		STORE	1-CSTH		# 1-CSTH (+2)

		ROUND	BZE
			360LAMB
		NORM	PDDL		#				PL AT 4
			X1
			0D
		SR1	DDV		#				PL AT 2
		SL*	SQRT
			0 	-3,1
		PDDL	SR		# 2D=SQRT(2R1/R2(1-CSTH)) (+5) 	PL AT 4
			SNTH
			6
		DDV	DAD		#				PL AT 2
			1-CSTH
		STADR
		STORE	COGAMAX
		BOV	BMN		# IF OVFL, COGAMAX=COGUPLIM
# Page 1297
			UPLIM		# IF NEG, USE EVEN IF LT COGLOLIM, SINCE
			MAXCOGA		# 	THIS WOULD BE RESET IN LAMBLOOP
		DSU	BMN		# IF COGAMAX GT COGUPLIM, COGAMAX=COGUPLIM
			COGUPLIM
			MAXCOGA		# OTHERWISE OK, SO GO TO MAXCOGA
UPLIM		DLOAD
			COGUPLIM	# COGUPLIM=.********* = MAX VALUE OF COGA
		STORE	COGAMAX		#	NOT CAUSING OVFL IN R1A CALCULATION
MAXCOGA		DLOAD
			CSTH
		SR	DSU		#				PL AT 0
			6
		STADR
		STODL	CSTH-RHO
			GEOMSGN
		BMN	DLOAD
			LOLIM
			CSTH-RHO
		SL1	DDV
			SNTH
		BOV
			LOLIM
MINCOGA		STORE	COGAMIN		# COGAMIN (+5)
		BON	SSP
			GUESSW
			NOGUESS
			TWEEKIT
			00001
		DLOAD
			COGA

LAMBLOOP	DMP
			SNTH
		SR1	DSU
			CSTH-RHO
		NORM	PDDL		# 0D=SNTH COGA-(CSTH-RHO) (+7+C(XI)) PL=2
			X1
			1-CSTH
		SL*	DDV		# 1-CSTH (+2)			PL AT 0
			0 -9D,1
		BMN	BZE
			NEGP
			NEGP
		STODL	P		# P=(1-CSTH)/(SNTH COGA-(CSTH-RHO)) (+4)
			COGA
		DSQ	DAD
			D1/1024
		NORM	DMP
			X1
# Page 1298
			P
		SR*	BDSU
			0 	-8D,1
			D1/32
		STODL	R1A		# R1A=2-P(1+COGA COGA) (+6)

			P
		BOV	CALL
			HIENERGY
			GETX
		DLOAD
			T
		STODL	TPREV
			XI
		BON	CALL
			INFINFLG
			NEGP		# HAVE EXCEEDED THEORETICAL BOUNDS
			DELTIME
		BOV	BDSU
			BIGTIME
			TDESIRED
		STORE	TERRLAMB
		ABS	BDSU
			EPSILONL
		BPL	RTB
			INITV
			CHECKCTR
		BHIZ	CALL
			SUFFCHEK
			ITERATOR
		DLOAD	BZE
			MPAC
			SUFFCHEK
		DAD
			COGA
		STCALL	COGA
			LAMBLOOP

NEGP		DLOAD	BPL		# IMPOSSIBLE TRAJECTORY DUE TO INACCURATE
			DCOGA		# BOUND CALCULATION.  TRY NEW COGA.
			LOENERGY

HIENERGY	SETPD	DLOAD		# HIGH ENERGY TRAJECTORY RESULTED.
			0
			COGA		# IN OVFL OF P OR R1A, OR XI EXCEEDING 50.
		STORE	COGAMIN		# THIS IS THE NEW BOUND.
COMMONLM	DLOAD	SR1
			DCOGA
# Page 1299
		STORE	DCOGA		# USE DCOGA/2 AS DECREMENT
		BZE	BDSU
			SUFFCHEK
			COGA
		STCALL 	COGA
			LAMBLOOP

BIGTIME		DLOAD
			TPREV
		STORE	T

LOENERGY	SETPD	DLOAD		# LOW ENERGY TRAJECTORY RESULTED
			0
			COGA		# IN OVERFLOW OF TIME.
		STCALL	COGAMAX		# THIS IS THE NEW BOUND.
			COMMONLM

SUFFCHEK	DLOAD	ABS
			TERRLAMB
		PDDL	DMP		#				PL AT 2D
			TDESIRED
			D1/4
		DAD	DSU		#				PL AT 0D
			ONEBIT
		BPL	SETGO
			INITV
			SOLNSW
			RTNLAMB

360LAMB		SETPD	SETGO		# LAMBERT CANNOT HANDLE CSTH=1
			0
			SOLNSW
			RTNLAMB

NOGUESS		SSP	DLOAD
			TWEEKIT
			20000
			COGAMIN
		SR1	PDDL		#				PL AT 2
			COGAMAX
		SR1	DAD
		STADR			#				PL AT 0
		STORE	COGA
		STCALL	DCOGA
			LAMBLOOP
# Page 1300
LOLIM		DLOAD	GOTO
			COGLOLIM	# COGLOLIM=-.*********
			MINCOGA

INITV		DLOAD	NORM
			R1
			X1
		PDDL	SR1		#				PL AT 2
			P
		DDV			#				PL AT 0
		SL*	SQRT
			0 	-4,1
		DMP	SL1
			ROOTMU
		PUSH	DMP		# 0D=VTAN (+7)			PL AT 2
			COGA
		SL	VXSC
			5
			UR1
		PDDL			# XCH WITH 0D			PL AT 0,6
		VXSC	VSL1
			UN
		VXV	VAD		#				PL AT 0
			UR1
		VSL1	CLEAR
			SOLNSW
		STORE	VVEC
		SLOAD	BZE
			VTARGTAG
			TARGETV
		GOTO
			RTNLAMB

TARGETV		DLOAD	CALL
			MAGVEC2
			LAMENTER
		STCALL	VTARGET
			RTNLAMB

# Page 1301
TIMERAD		STQ	SETPD		#				PL AT 0
			RTNTR
			0
		BOV
			+1
		VLOAD	PDVL		#				PL AT 6
			RVEC
			VVEC
		CALL
			PARAM
		BOV	DLOAD		#				PL AT 0
			COGAOVFL
			D1/32
		DSU	DMP
			R1A
			P
		SQRT	DMP
			COGA
		SL4	VXSC
			U2
		PDDL	DSU		#				PL AT 6
			D1/64
			R1A
		VXSC	VSU		#				PL AT 0
			UR1
		VSL4	UNIT
		BOV
			CIRCULAR
		PDDL	NORM		# 0D=UNIT(ECC) (+3)		PL AT 6
			RDESIRED	# 36D=ECC (+3)
			X1
		PDDL	DMP		#				PL AT 8
			R1
			P
		SL*	DDV		#				PL AT 6
			0,1
		DSU	DDV
			D1/16
			36D		# 36D=ECC (+3)
		STORE	COSF
		BOV	DSQ
			BADR2
		BDSU	BMN
			D1/4
			BADR2
		SQRT	SIGN
			SGNRDOT
		CLEAR
			APSESW

# Page 1302
TERMNVEC	VXSC	VSL1
			UN
		VXV	PDVL		# VXCH WITH 0D		PL AT 0,6
			0D
		VXSC	VAD		#			PL AT 0
			COSF
		VSL1	PUSH		# 0D=U2			PL AT 6

		DOT	DDV		# LIMITS RESULT TO POSMAX OR NEGMAX
			UR1
			DP1/4
		SR1	BOV		# SCALE BACK DOWN TO NORMAL
			+1		# CLEAR OVFIND IF SET
		STOVL	CSTH		# CSTH (+1)
			UR1
		VXV	VSL1
		DOT	SL1
			UN
		STODL	SNTH		# SNTH (+1)
			P
		CALL
			GETX
		CLRGO
			SOLNSW
			COMMNOUT

CIRCULAR	SETPD	SETGO
			0
			SOLNSW
			ABTCONIC

BADR2		DLOAD	SIGN
			LODPHALF
			COSF
		STODL	COSF
			KEPZERO
		SETGO
			APSESW
			TERMNVEC

# Page 1303
APSIDES		STQ	SETPD		#			PL AT 0
			RTNAPSE
			0D
		BOV
			+1
		VLOAD	PDVL		#			PL AT 6
			RVEC
			VVEC
		CALL
			PARAM
		BOV			#			PL AT 0
			GETECC
GETECC		DMP	SL4
			R1A
		BDSU	SQRT
			D1/64
		STORE	ECC
		DAD	PDDL		#			PL AT 2
			D1/8
			R1
		DMP	SL1
			P
		DDV			#			PL AT 0
		PDDL	NORM		# 0D=RP (+29 OR +27)	PL AT 2
			R1A
			X1
		PDDL	SL*		#			PL AT 4
			R1
			0 	-5,1
		DDV	DSU		#			PL AT 2,0
		BOV	BMN
			INFINAPO
			INFINAPO
		GOTO
			RTNAPSE
INFINAPO	DLOAD	GOTO		# RETURNS WITH APOAPSIS IN MPAC, PERIAPSIS
			LDPOSMAX
			RTNAPSE		# THAT PL IS AT 0.

# Page 1304
ABTCONIC	EXIT
		TC	POODOO
		OCT	00607

# Page 1305
		SETLOC	CONICS1
		BANK

		COUNT	04/CONIC

MUTABLE		2DEC*	3.986032 E10 B-36*	# MUE

		2DEC*	.******** E-10 B+34*	# 1/MUE

		2DEC*	1.******** E5 B-18*	# SQRT(MUE)

		2DEC*	.******** E-5 B+17*	# 1/SQRT(MUE)

		2DEC	4.902778 E8 B-30	# MUM

		2DEC	.203966 E-8 B+28	# 1/MUM

		2DEC*	2.******** E4 B-15*	# SQRT(MUM)

		2DEC*	.******** E-4 B+14*	# 1/SQRT(MUM)

LDPOSMAX	EQUALS 	LODPMAX			# DPPOSMAX IN LOW MEMORY.

# ERASABLE ASSIGNMENTS

# KEPLER SUBROUTINE

# INPUT --
# RRECT		ERASE 	+5
# VRECT		ERASE	+5
# TAU.		ERASE	+1
# XKEP		ERASE	+1
# TC		ERASE	+1
# XPREV		ERASE	+1
1/MU		EQUALS	14D
ROOTMU		EQUALS	16D
1/ROOTMU	EQUALS	18D

# OUTPUT --
# RCV		ERASE	+5
# VCV		ERASE	+5
# RC		ERASE	+1
# XPREV		ERASE	+1

# DEBRIS --
ALPHA		EQUALS	8D
XMAX		EQUALS	10D
# Page 1306
XMIN		EQUALS	12D
X		EQUALS	20D
XI		EQUALS	24D
S(XI)		EQUALS	26D
XSQC(XI)	EQUALS	28D
T		EQUALS	30D
R1		EQUALS	32D
KEPC1		EQUALS	34D
KEPC2		EQUALS	36D

# DELX		ERASE	+1
# DELT		ERASE	+1
# URRECT	ERASE	+5
# RCNORM	ERASE	+1
# XPREV		EQUALS	XKEP


# LAMBERT SUBROUTINE
#
# INPUT --
# R1VEC 	ERASE	+5
# R2VEC		ERASE 	+5
# TDESIRED	ERASE	+1
# GEOMSGN	ERASE	+0
# GUESSW			# 0 IF COGA GUESS AVAILABLE, 1 IF NOT
# COGA		ERASE	+1	# INPUT ONLY IF GUESSW IS ZERO.
# NORMSW			# 0 IF UN TO BE COMPUTED, 1 IF UN INPUT
# UN		ERASE	+5	# ONLY USED IF NORMSW IS 1
# VTARGTAG	ERASE	+0
# TWEEKIT	EQUALS	40D	# ONLY USED IF GUESSW IS 0

# OUTPUT --
# VTARGET	ERASE	+5	# AVAILABLE ONLY IF VTARGTAG IS ZERO.
# V1VEC		EQUALS	MPAC

# DEBRIS --
# RTNLAMB	ERASE	+0
# U2		ERASE	+5
# MAGVEC2	ERASE	+1
# UR1		ERASE	+5
# R1		EQUALS	31D
# UN		ERASE	+5
# SNTH		ERASE	+1
# CSTH		ERASE	+1
# 1-CSTH	ERASE	+1
# CSTH-RHO	ERASE	+1

COGAMAX		EQUALS	14D	# CLOBBERS 1/MU
COGAMIN		EQUALS	8D
DCOGA		EQUALS	12D

# TWEEKIT	EQUALS	40D
# P		ERASE	+1
# Page 1307
# COGA		ERASE	+1
# R1A		ERASE	+1
# X		EQUALS	20D
# XSQ		EQUALS	22D
# XI		EQUALS	24D
# S(XI)		EQUALS	26D
# XSQC(XI)	EQUALS	28D
# T		EQUALS	30D
# KEPC1		EQUALS	34D
# KEPC2		EQUALS	36D
# SLOPESW
# SOLNSW

# OTHERS --
# RVEC		EQUALS	R1VEC
# VVEC		ERASE	+5
# COGAFLAG
# RVSW
# INFINFLG
# APSESW
# 360SW
# RTNTT		EQUALS	RTNLAMB
# ECC		ERASE	+1
# RTNTR		EQUALS	RTNLAMB
# RTNAPSE	EQUALS	RTNLAMB
# R2		EQUALS	MAGVEC2

COSF		EQUALS	24D

# RTNPRM	ERASE	+0
# SGNRDOT	ERASE	+0
# RDESIRED	ERASE	+1


# ITERATOR SUBROUTINE

# ORDERSW
MAX		EQUALS	14D		# CLOBBERS 1/MU
MIN		EQUALS	8D

# INDEP		ERASE	+1

DELINDEP	EQUALS	12D
ITERCTR		EQUALS	22D
DEP		EQUALS	30D

# DELDEP	ERASE	+1
# DEPREV	ERASE	+1

TWEEKIT		EQUALS	40D


# MORE KEPLER

# EPSILONT	ERASE	+1

# Page 1308
# MORE LAMBERT

# TERRLAMB	EQUALS	DELDEP
# TPREV		EQUALS	DEPREV

# EPSILONL	EQUALS	EPSILONT +2	# DOUBLE PRECISION WORD



# Copyright:	Public domain.
# Filename:	ERASABLE_ASSIGNMENTS.agc
# Purpose:	Part of the source code for Colossus 2A, AKA Comanche 055.
#		It is part of the source code for the Command Module's (CM)
#		Apollo Guidance Computer (AGC), for Apollo 11.
# Assembler:	yaYUL
# Contact:	<PERSON> <<EMAIL>>.
# Website:	www.ibiblio.org/apollo.
# Mod history:	2009-05-06 RSB	Started adapting from the Colossus249/ file
#				of the same name, using Comanche055 page
#				images. Only through page 51 so far.
#		2009-05-07 RSB	Through page 92 so far.
#		2009-05-07 RSB	(Again!) First draft completed.
#		2009-05-20 RSB	Fixed some bugs uncovered in trial assemblies:
#				EMDOT, STATEXIT, VGDISP, DVPREV, POSTCDH,
#				RETROFLG not defined correctly, changed the
#				typing of labels 9X9LOC1 and 9X9LOC2,
#				R32FLBIT -> R31FLBIT.
#		2009-05-21 RSB	Corrected definition of DELBRTMP, which
#				chained to quite a lot of off-by-one errors.
#				Changed a +8 to a +8D.
#
# This source code has been transcribed or otherwise adapted from digitized
# images of a hardcopy from the MIT Museum.  The digitization was performed
# by <PERSON>, and arranged for by <PERSON> of the Museum.  Many
# thanks to both.  The images (with suitable reduction in storage size and
# consequent reduction in image quality as well) are available online at
# www.ibiblio.org/apollo.  If for some reason you find that the images are
# illegible, contact <NAME_EMAIL> about getting access to the
# (much) higher-quality images which Paul actually created.
#
# Notations on the hardcopy document read, in part:
#
#	Assemble revision 055 of AGC program Comanche by NASA
#	2021113-051.  10:28 APR. 1, 1969
#
#	This AGC program shall also be referred to as
#			Colossus 2A


# Page 37
# CONVENTIONS AND NOTATIONS UTILIZED FOR ERASABLE ASSIGNMENTS.

#	EQUALS	IS USED IN TWO WAYS.  IT IS OFTEN USED TO CHAIN A GROUP
#		OF ASSIGNMENTS SO THAT THE GROUP MAY BE MOVED WITH THE
#		CHANGING OF ONLY ONE CARD.  EXAMPLE:
#
#			X	EQUALS	START
#			Y	EQUALS	X	+SIZE.X
#			Z	EQUALS	Y	+SIZE.Y
#
#		(X, Y, AND Z ARE CONSECUTIVE AND BEGIN AT START.
#		SIZE.X AND SIZE.Y ARE THE RESPECTIVE SIZES OF X AND Y.
#		USUALLY NUMERIC, IE. 1, 2, 6, 18D, ETC.)

#	EQUALS	OFTEN IMPLIES THE SHARING OF REGISTERS (DIFFERENT NAMES
#		AND DIFFERENT DATA).  EXAMPLE:
#
#			X	EQUALS	Y

#	=	MEANS THAT MULTIPLE NAMES HAVE BEEN GIVEN TO THE SAME DATA.
#		(THIS IS LOGICAL EQUIVALENCE, NOT SHARING.)  EXAMPLE:
#
#			X	=	Y

#	THE SIE AND UTILIZATION OF AN ERASABLE ARE OFTEN INCLUDED IN
#	THE COMMENTS IN THE FOLLOWING FORM:  M(SIZE)N.
#
#		M	REFERS TO THE MOBILITY OF THE ASSIGNMENT.
#			B	MEANS THAT THE SYMBOL IS REFERENCED BY BASIC
#				INSTRUCTIONS AND THUS IS E-BANK SENSITIVE.
#			I	MEANS THAT THE SYMBOL IS REFERENCED ONLY BY
#				INTERPRETIVE INSTRUCTIONS, AND IS THUS E-BANK
#				INSENSITIVE AND MAY APPEAR IN ANY E-BANK.
#
#		SIZE	IS THE NUMBER OF REGISTERS INCLUDED BY THE SYMBOL.
#
#		N	INDICATES THE NATURE OF PERMANENCE OF THE CONTENTS.
#			PL	MEANS THAT THE CONTENTS ARE PAD LOADED.
#			DSP	MEANS THAT THE REGISTER IS USED FOR A DISPLAY.
#			PRM	MEANS THAT THE REGISTER IS PERMANENT.  IE., IT
#				IS USED DURING THE ENTIRE MISSION FOR ONE
#				PURPOSE AND CANNOT BE SHARED.
#			TMP	MEANS THAT THE REGISTER IS USED TEMPORARILY OR
#				IS A SCRATCH REGISTER FOR THE ROUTINE TO WHICH
#				IT IS ASSIGNED.  THAT IS, IT NEED NOT BE SET
#				PRIOR TO INVOCATION OF THE ROUTINE NOR DOES IT
#				CONTAIN USEFUL OUTPUT TO ANOTHER ROUTINE.  THUS
# Page 38
#				IT MAY BE SHARED WITH ANY OTHER ROUTINE WHICH
#				IS NOT ACTIVE IN PARALLEL
#			IN	MEANS INPUT TO THE ROUTINE AND IT IS PROBABLY
#				TEMPORARY FOR A HIGHER-LEVEL ROUTINE/PROGRAM.
#			OUT	MEANS OUTPUT FROM THE ROUTINE, PROBABLY
#				TEMPORARY FOR A HIGHER-LEVEL ROUTINE/PROGRAM.

# Page 39

# SPECIAL REGISTERS.

A		EQUALS	0
L		EQUALS	1		# L AND Q ARE BOTH CHANNELS AND REGISTERS
Q		EQUALS	2
EBANK		EQUALS	3
FBANK		EQUALS	4
Z		EQUALS	5		# ADJACENT TO FBANK AND BBANK FOR DXCH Z
BBANK		EQUALS	6		# (DTCB) AND DXCH FBANK (DTCF).
					# REGISTER 7 IS A ZERO-SOURCE, USED BY ZL.

ARUPT		EQUALS	10		# INTERRUPT STORAGE
LRUPT		EQUALS	11
QRUPT		EQUALS	12
SAMPTIME	EQUALS	13		# SAMPLED TIME 1 & 2.
ZRUPT		EQUALS	15		# (13 AND 14 ARE SPARES.)
BANKRUPT	EQUALS	16		# USUALLY HOLDS FBANK OR BBANK.
BRUPT		EQUALS	17		# RESUME ADDRESS AS WELL.

CYR		EQUALS	20
SR		EQUALS	21
CYL		EQUALS	22
EDOP		EQUALS	23		# EDITS INTERPRETIVE OPERATION CODE PAIRS.

TIME2		EQUALS	24
TIME1		EQUALS	25
TIME3		EQUALS	26
TIME4		EQUALS	27
TIME5		EQUALS	30
TIME6		EQUALS	31
CDUX		EQUALS	32
CDUY		EQUALS	33
CDUZ		EQUALS	34
CDUT		EQUALS	35		# OPTICS TRUNNION CDU (WAS OPTY).
OPTY		=	CDUT
CDUS		EQUALS	36		# OPTICS SHAFT CDU (WAS OPTX).
OPTX		=	CDUS
PIPAX		EQUALS	37
PIPAY		EQUALS	40
PIPAZ		EQUALS	41
BMAGX		EQUALS	42
BMAGY		EQUALS	43
BMAGZ		EQUALS	44
INLINK		EQUALS	45
RNRAD		EQUALS	46
GYROCTR		EQUALS	47
GYROCMD		EQUALS	47
CDUXCMD		EQUALS	50
CDUYCMD		EQUALS	51

# Page 40

CDUZCMD		EQUALS	52
CDUTCMD		EQUALS	53		# OPTICS TRUNNION COMMAND (WAS OPTYCMD)
OPTYCMD		=	CDUTCMD
TVCYAW		EQUALS	CDUTCMD		# SPS YAW COMMAND IN TVC MODE
CDUSCMD		EQUALS	54		# OPTICS SHAFT COMMAND (WAS OPTXCMD).
TVCPITCH	EQUALS	CDUSCMD		# SPS PITCH COMMAND IN TVC MODE
OPTXCMD		=	CDUSCMD
EMSD		EQUALS	55
THRUST		EQUALS	55
LEMONM		EQUALS	56
LOCALARM	EQUALS	57
BANKALRM	EQUALS	60

# INTERPRETIVE REGISTERS ADDRESSED RELATIVE TO VAC AREA.

LVSQUARE	EQUALS	34D		# SQUARE OF VECTOR INPUT TO ABVAL AND UNIT
LV		EQUALS	36D		# LENGTH OF VECTOR INPUT TO UNIT.
X1		EQUALS	38D		# INTERPRETIVE SPECIAL REGISTER RELATIVE
X2		EQUALS	39D		# TO THE WORK AREA.
S1		EQUALS	40D
S2		EQUALS	41D
QPRET		EQUALS	42D

# Page 41

# INPUT/OUTPUT CHANNELS

# *** CHANNEL ZERO IS TO BE USED IN AN INDEXED OPERATION ONLY. ***
LCHAN		EQUALS	L
QCHAN		EQUALS	Q
HISCALAR	EQUALS	3
LOSCALAR	EQUALS	4
PYJETS		EQUALS	5
ROLLJETS	EQUALS	6
SUPERBNK	EQUALS	7
OUT0		EQUALS	10
DSALMOUT	EQUALS	11
CHAN12		EQUALS	12
CHAN13		EQUALS	13
CHAN14		EQUALS	14
MNKEYIN		EQUALS	15
NAVKEYIN	EQUALS	16
CHAN30		EQUALS	30
CHAN31		EQUALS	31
CHAN32		EQUALS	32
CHAN33		EQUALS	33
DNTM1		EQUALS	34
DNTM2		EQUALS	35

# END OF CHANNEL ASSIGNMENTS

# Page 42

# FLAGWORDS
#
# FLAGWRD0	STATE +0	(000-014)
# FLAGWRD1	STATE +1	(015-029)
# FLAGWRD2	STATE +2	(030-044)
# FLAGWRD3	STATE +3	(045-059)
# FLAGWRD4	STATE +4	(060-074)
# FLAGWRD5	STATE +5	(075-089)
# FLAGWRD6	STATE +6	(090-104)
# FLAGWRD7	STATE +7	(105-119)
# FLAGWRD8	STATE +8D	(120-134)
# FLAGWRD9	STATE +9D	(135-149)

#	SORTED LIST OF
#	INTERPRETIVE SWITCH BIT ASSIGNMENTS

# INTERPRETIVE SWITCH BIT ASSIGNMENTS
#
# FLAGWORD	DEC NUM		BIT & FLAG	EQUIVALENT FLAGWORDS
#
# 22DSPFLG	032D		BIT 13 FLAG 2
# 360SW		134D		BIT 1 FLAG 8
# 3AXISFLG	084D		BIT 6 FLAG 5
# ADVTRK	125D		BIT 10 FLAG 8
# AMOONFLG	13D		BIT 2 FLAG 0
# APSESW	130D		BIT 5 FLAG 8
# ASTNFLAG	108D		BIT 12 FLAG 7
# ATTCHFLG	118D		BIT 2 FLAG 7
# AVEGFLAG	029D		BIT 1 FLAG 1
# AVEMIDSW	149D		BIT 1 FLAG 9
# AVFLAG	040D		BIT 5 FLAG 2
# CALCMAN2	043D		BIT 2 FLAG 2
# CMDAPARM	093D		BIT 12 FLAG 6
# CMOONFLG	123D		BIT 12 FLAG 8
# CM/DSTBY	103D		BIT 2 FLAG 6
# COGAFLAG	131D		BIT 4 FLAG 8
# COMPUTER	082D		BIT 8 FLAG 5
# CPHIFLAG	000D		BIT 15 FLAG 0
# CULTFLAG	053D		BIT 7 FLAG 3
# D6OR9FLG	058D		BIT 2 FLAG 3
# DAPBIT1	090D		BIT 15 FLAG 6
# DAPBIT2	091D		BIT 14 FLAG 6
# DIM0FLAG	059D		BIT 1 FLAG 3
# DMENFLAG	081D		BIT 9 FLAG 5
# DRIFTFLG	030D		BIT 15 FLAG 2
# DSKYFLAG	075D		BIT 15 FLAG 5
# EGSW		097D		BIT 8 FLAG 6	KNOTNFLG R57FLAG

# Page 43

# ENG1FLAG	018D		BIT 12 FLAG 1
# ENG2FLAG	019D		BIT 11 FLAG 1
# ENGONFLG	083D		BIT 7 FLAG 5
# ERADFLAG	017D		BIT 13 FLAG 1
# ETPIFLAG	038D		BIT 7 FLAG 2	FIRSTFLG OPTNSW
# F2RTE		010D		BIT 5 FLAG 0
# FINALFLG	039D		BIT 6 FLAG 2
# FIRSTFLG	038D		BIT 7 FLAG 2	ETPIFLAG OPTNSW
# FREEFLAG	012D		BIT 3 FLAG 0
# GAMDIFSW	094D		BIT 11 FLAG 6
# GLOKFAIL	046D		BIT 14 FLAG 3
# GONEBY	112D		BIT 8 FLAG 7
# GONEPAST	095D		BIT 10 FLAG 6
# GRRBKFLG	085D		BIT 5 FLAG 5
# GUESSW	028D		BIT 2 FLAG 1
# GYMDIFSW	104D		BIT 1 FLAG 6
# .05GSW	102D		BIT 3 FLAG 6
# HIND		099D		BIT 6 FLAG 6
# IDLEFAIL	024D		BIT 6 FLAG 1
# IDLEFLAG	113D		BIT 7 FLAG 7
# IGNFLAG	107D		BIT 13 FLAG 7
# IMPULSW	036D		BIT 9 FLAG 2
# IMUSE		007D		BIT 8 FLAG 0
# INCORFLG	079D		BIT 11 FLAG 5
# INFINFLG	128D		BIT 7 FLAG 8
# INRLSW	100D		BIT 5 FLAG 6
# INTFLAG	151D		BIT 14 FLAG 10
# INTYPFLG	056D		BIT 4 FLAG 3
# ITSWICH	106D		BIT 14 FLAG 7
# KFLAG		014D		BIT 1 FLAG 0
# KNOWNFLG	097D		BIT 8 FLAG 6	EGSW R57FLAG
# LATSW		101D		BIT 4 FLAG 6
# LMOONFLG	124D		BIT 11 FLAG 8
# LUNAFLAG	048D		BIT 12 FLAG 3
# MAXDBFLG	138D		BIT 12 FLAG 9
# MGLVFLAG	088D		BIT 2 FLAG 5
# MID1FLAG	147D		BIT 3 FLAG 9
# MIDAVFLG	148D		BIT 2 FLAG 9
# MIDFLAG	002D		BIT 13 FLAG 0
# MKOVFLAG	072D		BIT 3 FLAG 4
# MOONFLAG	003D		BIT 12 FLAG 0
# MRKIDFLG	060D		BIT 15 FLAG 4
# MRKNVFLG	066D		BIT 9 FLAG 4
# MRUPTFLG	070D		BIT 5 FLAG 4
# MWAITFLG	064D		BIT 11 FLAG 4
# N22ORN17	144D		BIT 6 FLAG 9
# NEEDLFLG	006D		BIT 9 FLAG 0
# NEWIFLG	122D		BIT 13 FLAG 8
# NJETSFLG	015D		BIT 15 FLAG 1
# NODOFLAG	044D		BIT 1 FLAG 2

# Page 44

# NODOP01	018D		BIT 12 FLAG 1
# NORFHOR	004D		BIT 11 FLAG 0
# NORMSW	110D		BIT 10 FLAG 7
# NOSWITCH	098D		BIT 7 FLAG 6
# NRMIDFLG	062D		BIT 13 FLAG 4
# NRMNVFLG	067D		BIT 8 FLAG 4
# NRUPTFLG	071D		BIT 4 FLAG 4
# NWAITFLG	065D		BIT 10 FLAG 4
# OPTNSW	038D		BIT 7 FLAG 2	ETPIFLAG FIRSTFLG
# ORBWFLAG	054D		BIT 6 FLAG 3
# ORDERSW	129D		BIT 6 FLAG 8
# P21FLAG	033D		BIT 12 FLAG 2
# P22MKFLG	049D		BIT 11 FLAG 3
# P39/79SW	126D		BIT 9 FLAG 8
# PDSPFLAG	063D		BIT 12 FLAG 4
# PFRATFLG	041D		BIT 4 FLAG 2
# PINBRFLG	069D		BIT 6 FLAG 4
# PRECIFLG	052D		BIT 8 FLAG 3
# PRFTRKAT	060D		BIT 10 FLAG 5
# PRIODFLG	061D		BIT 14 FLAG 4
# PRONVFLG	068D		BIT 7 FLAG 4
# QUITFLAG	145D		BIT 5 FLAG 9
# R21MARK	031D		BIT 14 FLAG 2
# R22CAFLG	143D		BIT 7 FLAG 9
# R23FLG	021D		BIT 9 FLAG 1
# R31FLAG	146D		BIT 4 FLAG 9
# R53FLAG	009D		BIT 6 FLAG 0
# R57FLAG	097D		BIT 8 FLAG 6	KNOWNFLG EGSW
# R60FLAG	086D		BIT 4 FLAG 5
# REFSMFLG	047D		BIT 13 FLAG 3
# REINTFLG	158D		BIT 7 FLAG 10
# RELVELSW	096D		BIT 9 FLAG 6
# RENDWFLG	089D		BIT 1 FLAG 5
# RNDVZFLG	008D		BIT 7 FLAG 0
# RPQFLAG	120D		BIT 15 FLAG 6
# RVSW		111D		BIT 9 FLAG 7
# SAVECFLG	140D		BIT 10 FLAG 9
# SKIPVHF	035D		BIT 10 FLAG 2
# SLOPESW	027D		BIT 3 FLAG 1
# SOLNSW	087D		BIT 3 FLAG 5
# SOURCFLG	142D		BIT 8 FLAG 9
# STATEFLG	055D		BIT 5 FLAG 3
# STEERSW	034D		BIT 11 FLAG 2
# STIKFLAG	016D		BIT 14 FLAG 1
# STRULLSW	092D		BIT 13 FLAG 6
# SURFFLAG	127D		BIT 8 FLAG 8
# SWTOVER	135D		BIT 15 FLAG 9
# TARG1FLG	020D		BIT 10 FLAG 1

# Page 45

# TARG2FLG	021D		BIT 9 FLAG 1
# TERMIFLG	105D		BIT 15 FLAG 7
# TFFSW		119D		BIT 1 FLAG 7
# TIMRFLAG	109D		BIT 11 FLAG 7
# TRACKFLG	025D		BIT 5 FLAG 1
# TRM03FLG	026D		BIT 4 FLAG 1
# TRUNFLAG	011D		BIT 4 FLAG 0
# UPDATFLG	023D		BIT 7 FLAG 1
# UPLOCKFL	116D		BIT 4 FLAG 7
# V37FLAG	114D		BIT 6 FLAG 7
# V59FLAG	078D		BIT 12 FLAG 5
# V67FLAG	136D		BIT 14 FLAG 9
# V82EMFLG	137D		BIT 13 FLAG 9
# V94FLAG	139D		BIT 11 FLAG 9
# V96ONFLG	132D		BIT 3 FLAG 8
# VEHUPFLG	022D		BIT 8 FLAG 1
# VERIFLAG	117D		BIT 3 FLAG 7
# VFLAG		050D		BIT 10 FLAG 3
# VHFRFLAG	141D		BIT 9 FLAG 9
# VINTFLAG	057D		BIT 3 FLAG 3
# XDELVFLG	037D		BIT 8 FLAG 2
# XDSPFLAG	074D		BIT 1 FLAG 4

# Page 46

# INTERPRETIVE SWITCH BIT ASSIGNMENTS
FLAGWRD0	=	STATE +0	# (000-014)
					# (SET)			(RESET)
# BIT 15 FLAG 0
CPHIFLAG	=	000D		# OUTPUT OF CALCGA IS	OUTPUT OF CALCGA IS
					# CPHIX			THETAD
CPHIBIT		=	BIT15

# BIT 14 FLAG 0
JSWITCH		=	001D		# INTEGRATION OF W	INTEGRATION OF STATE
					# MATRIX		VECTOR
JSWCHBIT	=	BIT14

# BIT 13 FLAG 0
MIDFLAG		=	002D		# INTEGRATION WITH	INTEGRATION WITHOUT
					# SOLAR PERTURBATIONS	SOLAR PERTURBATIONS

MIDFLBIT	=	BIT13

# BIT 12 FLAG 0
MOONFLAG	=	003D		# MOON IS SPHERE OF 	EARTH IS SPHERE OF
					# INFLUENCE		INFLUENCE

MOONBIT		=	BIT12

# BIT 11 FLAG 0
NORFHOR		=	004D		# FAR HORIZON		NEAR HORIZON

NORFBIT		=	BIT11

# BIT 10 FLAG 0
ZMEASURE	=	005D		# MEASUREMENT PLANET	MEASUREMENT PLANET
					# AND PRIMARY PLANET	AND PRIMARY PLANET
					# DIFFERENT		SAME

ZMEASBIT	=	BIT10

# BIT 9 FLAG 0
NEEDLFLG	=	006D		# TOTAL ATTITUDE	A/P FOLLOWING ERROR
					# ERROR DISPLAYED	DISPLAYED

NEEDLBIT	=	BIT9

# BIT 8 FLAG 0
IMUSE		=	007D		# IMU IN USE		IMU NOT IN USE

# Page 47

IMUSEBIT	=	BIT8

# BIT 7 FLAG 0
RNDVZFLG	=	008D		# P20 RUNNING		P20 NOT RUNNING

RNDVZBIT	=	BIT7

# BIT 6 FLAG 0
R53FLAG		=	009D		# V51 INITIATED		V51 NOT INITIATED

R53FLBIT	= 	BIT8

# BIT 5 FLAG 0
F2RTE		=	010D		# IN TIME CRITICAL	NOT IN TIME CRITICAL
					# MODE			MODE

F2RTEBIT	=	BIT5

# BIT 4 FLAG 0
TRUNFLAG	=	011D		# DRIVING OF TRUNNION	DRIVING OF TRUNNION
					# ALLOWED		NOT ALLOWED

TRUNBIT		=	BIT4

# BIT 3 FLAG 0
FREEFLAG	=	012D		# (TEMPORARY FLAG USED IN MANY ROUTINES)

FREEFBIT	=	BIT3

# BIT 2 FLAG 0
AMOONFLG	=	13D		# STATE VECTOR IN	STATE VECTOR IN
AMOONBIT	=	BIT2		# LUNAR SPHERE AT	EARTH SPHERE AT
					# MIDTOAVE		MIDTOAVE

# BIT 1 FLAG 0
KFLAG		=	014D		# SEARCH SECTOR MORE	SEARCH SECTOR LESS
					# THAN 180 DEGREES	THAN 180 DEGREES

KBIT		=	BIT1

FLAGWRD1	=	STATE +1	# (015-029)
					# (SET)			(RESET)

# BIT 15 FLAG 1
NJETSFLG	=	015D		# TWO JET RCS BURN	FOUR JET RCS BURN

NJETSBIT	=	BIT15

# Page 48

# BIT 14 FLAG 1
STIKFLAG	=	016D		# RHC CONTROL		CMC CONTROL
STIKBIT		=	BIT14

# BIT 13 FLAG 1
ERADFLAG	=	017D		# EARTH, COMPUTE	EARTH, USED FIXED
					#   FISCHER ELLIPSOID	  RADIUS
					#   RADIUS
					# MOON, USE FIXED	MOON, USE RLS FOR
					#   RADIUS		  LUNAR RADIUS

ERADFBIT	=	BIT13

# BIT 12 FLAG 1
NODOP01		=	018D		# P01 NOT ALLOWED	P01 ALLOWED
NOP01BIT	=	BIT12

# BIT 11 FLAG 1
ENG2FLAG	=	019D		# RCS BURN		SPS BURN

ENG2BIT		=	BIT11

# BIT 10 FLAG 1
TARG1FLG	=	020D		# SIGHTING LEM		NOT SIGHTING LEM

TARG1BIT	=	BIT10

# BIT 9 FLAG 1
TARG2FLG	= 	021D		# SIGHTING LANDMARK	SIGHTING STAR

TARG2BIT	= 	BIT9

# BIT 9 FLAG 1
R23FLG		=	021D		# R23 MARKING

R23BIT		=	BIT9

# BIT 8 FLAG 1
VEHUPFLG	=	022D		# CSM STATE VECTOR	LEM STATE VECTOR
					# BEING UPDATED		BEING UPDATED

VEHUPBIT	=	BIT8

# BIT 7 FLAG 1
UPDATFLG	=	023D		# UPDATING BY MARKS	UPDATING BY MARKS
					# ALLOWED		NOT ALLOWED
# Page 49

UPDATBIT	=	BIT7

# BIT 6 FLAG 1
IDLEFAIL	=	024D		# INHIBIT R41		ENABLE R41 (ENGFAIL)

IDLEBIT		=	BIT6

# BIT 5 FLAG 1
TRACKFLG	=	025D		# TRACKING ALLOWED	TRACKING NOT ALLOWED

TRACKBIT	=	BIT5

# BIT 4 FLAG 1
TRM03FLG	=	026D		# REQUEST TO		NO REQUEST TO
					# TERMINATE P03 HAS	TERMINATE P03 HAS
					# BEEN ENTERED		BEEN ENTERED

TRM03BIT	=	BIT4

# BIT 3 FLAG 1
SLOPESW		= 	027D		# ITERATE WITH BIAS	ITERATE WITH REGULA
					# METHOD IN ITERATOR	FALSI METHOD IN
					#			ITERATOR

SLOPEBIT	=	BIT3

# BIT 2 FLAG 1
GUESSW		=	028D		# NO STARTING VALUE	STARTING VALUE FOR
					# FOR ITERATION		ITERATION EXISTS

GUESSBIT	=	BIT2

# BIT 1 FLAG 1
AVEGFLAG	=	029D		# AVERAGEG (SERVICER)	AVERAGEG (SERVICER)
					# TO CONTINUE		TO CEASE

AVEGBIT		=	BIT1

FLAGWRD2	=	STATE +2	# (030-044)

					# (SET)			(RESET)

# BIT 15 FLAG 2
DRIFTFLG	=	030D		# T3RUPT CALLS GYRO	T3RUPT DOES NO GYRO
					# COMPENSATION		COMPENSATION

DRFTBIT		=	BIT15

# Page 50

# BIT 14 FLAG 2
R21MARK		=	031D		# OPTION ONE FOR	OPTION TWO FOR
					# MARKRUPT		MARKRUPT

R21BIT		=	BIT14

# BIT 13 FLAG 2
22DSPFLG	=	032D		# DISPLAY DR,DV		DO NOT DISPLAY DR,DV

22DSPBIT	=	BIT13

# BIT 12 FLAG 2
P21FLAG		=	033D		# SUCCEEDING PASS	1ST PASS THRU P21,
					# THRU P21, USE BASE	CALCULATE BASE
P21BIT		=	BIT12		# VECTOR FOR CALC.	VECTOR

STEERSW		=	034D		# STEERING TO BE DONE	STEERING OMITTED

STEERBIT	=	BIT11

# BIT 10 FLAG 2
SKIPVHF		=	035D		# DISREGARD RADAR	RADAR READ TO
					# READ BECAUSE OF	PROCEED NORMALLY
SKIPVBIT	=	BIT10		# SFTWRE OR HDWRE
					# RESTART

# BIT 9 FLAG 2
IMPULSW		=	036D		# MINIMUM IMPULSE	STEERING BURN (NO
					# BURN (CUTOFF TIME	CUTOFF TIME YET
					# SPECIFIED)		AVAILABLE)

IMPULBIT	=	BIT9

# BIT 8 FLAG 2
XDELVFLG	=	037D		# EXTERNAL DELTAV VG	LAMBERT (AIMPOINT)
					# COMPUTATION		VG COMPUTATION

XDELVBIT	=	BIT8

# BIT 7 FLAG 2
ETPIFLAG	=	038D		# ELEVATION ANGLE	TPI TIME SUPPLIED
					# SUPPLIED FOR P34,74	FOR P34,74

# BIT 7 FLAG 2
FIRSTFLG	=	ETPIFLAG	# FIRST PASS		SUCCEEDING PASS THRU
					# THRU S40.9		S40.9

FIRSTBIT	=	BIT7

# BIT 7 FLAG 2
# Page 51
OPTNSW		=	ETPIFLAG	# SOI PHASE P38/P78	SOR PHASE OF P38/P78

FINALBIT	=	BIT6

# BIT 6 FLAG 2
FINALFLG	=	039D		# LAST PASS THROUGH	INTERIM PASS THROUGH
					# RENDEZVOUS PROGRAM	RENDEZVOUS PROGRAM
					# COMPUTATIONS		COMPUTATIONS

AVFLBIT		=	BIT5

# BIT 5 FLAG 2
AVFLAG		=	040D		# LEM IS ACTIVE		CSM IS ACTIVE
					# VEHICLE		VEHICLE

# BIT 4 FLAG 2
PFRATFLG	=	041D		# PREFERRED ATTITUDE	PREFERRED ATTITUDE
					# COMPUTED		NOT COMPUTED

PFRATBIT	=	BIT4

# BIT 3 FLAG 2
		=	042D

# BIT 2 FLAG 2
CALCMAN2	=	043D		# PERFORM MANEUVER	BYPASS STARTING
					# STARTING PROCEDURE	PROCEDURE

CALC2BIT	=	BIT2

# BIT 1 FLAG 2
NODOFLAG	=	044D		# V37 NOT PERMITTED 	V37 PERMITTED

NODOBIT		=	BIT1

FLAGWRD3	=	STATE +3	# (045-059)
					# (SET)			(RESET)

# BIT 15 FLAG 3
		=	045D

# BIT 14 FLAG 3
GLOKFAIL	=	046D		# GIMBAL LOCK HAS	NOT IN GIMBAL LOCK
					# OCCURRED

GLOKFBIT	=	BIT14

# Page 52

# BIT 13 FLAG 3
REFSMFLG	=	047D		# REFSMMAT GOOD		REFSMMAT NO GOOD

REFSMBIT	=	BIT13

# BIT 12 FLAG 3
LUNAFLAG	=	048D		# LUNAR LAT-LONG	EARTH LAT-LONG

LUNABIT		=	BIT12

# BIT 11 FLAG 3
P22MKFLG	=	049D		# P22 DOWNLINKED MARK	P22 DOWNLINK MARK
					# DATA WAS JUST TAKEN	DATA NOT JUST TAKEN

P22MKBIT	=	BIT11

# BIT 10 FLAG 3
VFLAG		=	050D		# LESS THAN TWO STARS	TWO STARS IN FIELD
					# IN FIELD OF VIEW	OF VIEW

VFLAGBIT	=	BIT10

# BIT 9 FLAG 3
		=	051D

# BIT 8 FLAG 3
PRECIFLG	=	052D		# CSMPREC OR LEMPREC	INTEGRV
					# OR INTEGRVS CALLED	CALLED

PRECIBIT	=	BIT8

# BIT 7 FLAG 3
CULTFLAG	=	053D		# STAR OCCULTED		STAR NOT OCCULTED

CULTBIT		=	BIT7

# BIT 6 FLAG 3
ORBWFLAG	=	054D		# W MATRIX VALID FOR	W MATRIX INVALID FOR
					# ORBITAL NAVIGATION	ORBITAL NAVIGATION

ORBWFBIT	=	BIT6

# BIT 5 FLAG 3
STATEFLG	=	055D		# PERMANENT STATE	PERMANENT STATE
					# VECTOR UPDATED	VECTOR NOT UPDATED

STATEBIT	=	BIT5

# BIT 4 FLAG 3
INTYPFLG	=	056D		# CONIC INTEGRATION	ENCKE INTEGRATION
# Page 53
INTYBIT		=	BIT4

# BIT 3 FLAG 3
VINTFLAG	=	057D		# CSM STATE VECTOR	LEM STATE VECTOR
					# BEING INTEGRATED	BEING INTEGRATED

VINTFBIT	=	BIT3

# BIT 2 FLAG 3
D6OR9FLG	=	058D		# DIMENSION OF W IS 9	DIMENSION OF W IS 6
					# FOR INTEGRATION	FOR INTEGRATION

D6OR9BIT	=	BIT2

# BIT 1 FLAG 3
DIM0FLAG	=	059D		# W MATRIX IS TO BE	W MATRIX IS NOT TO
					# USED			BE USED

FLAGWRD4	=	STATE +4	# (060-074)
					# (SET)			(RESET)

DIM0BIT		=	BIT1

# BIT 15 FLAG 4
MRKIDFLG	=	060D		# MARK DISPLAY IN 	NO MARK DISPLAY IN
					# ENDIDLE		ENDIDLE

MRKIDBIT	=	BIT15

# BIT 14 FLAG 4
PRIODFLG	=	061D		# PRIORITY DISPLAY IN	NO PRIORITY DISPLAY
					# ENDIDLE		IN ENDIDLE

PRIODBIT	=	BIT14

# BIT 13 FLAG 4
NRMIDFLG	=	062D		# NORMAL DISPLAY IN	NO NORMAL DISPLAY
					# ENDIDLE		IN ENDIDLE

NRMIDBIT	=	BIT13

# BIT 12 FLAG 4
PDSPFLAG	=	063D		# CAN'T INTERRUPT	SEE M. HAMILTON
					# PRIORITY DISPLAY

PDSPFBIT	=	BIT12

# BIT 11 FLAG 4
MWAITFLG	=	064D		# HIGHER PRIORITY	NO HIGHER PRIORITY
# Page 54
					# DISPLAY OPERATING	DISPLAY OPERATING
					# WHEN MARK DISPLAY	WHEN MARK DISPLAY
					# INITIATED		INITIATED
MWAITBIT	=	BIT11

# BIT 10 FLAG 4
NWAITFLG	=	065D		# HIGHER PRIORITY	NO HIGHER PRIORITY
					# DISPLAY OPERATING	DISPLAY OPERATING
					# WHEN NORMAL		WHEN NORMAL DISPLAY
					# DISPLAY INITIATED	INITIATED

NWAITBIT	=	BIT10

# BIT 9 FLAG 4
MRKNVFLG	=	066D		# ASTRONAUT USING	ASTRONAUT NOT USING
					# KEYBOARD WHEN MARK	KEYBOARD WHEN MARK
					# DISPLAY INITIATED	DISPLAY INITIATED

MRKNVBIT	=	BIT9

# BIT 8 FLAG 4
NRMNVFLG	=	067D		# ASTRONAUT USING	ASTRONAUT NOT USING
					# KEYBOARD WHEN		KEYBOARD WHEN
					# NORMAL DISPLAY	NORMAL DISPLAY
					# INITIATED		INITIATED

NRMNVBIT	=	BIT8

# BIT 7 FLAG 4
PRONVFLG	=	068D		# ASTRONAUT USING	ASTRONAUT NOT USING
					# KEYBOARD WHEN		KEYBOARD WHEN
					# PRIORITY DISPLAY	PRIORITY DISPLAY
					# INITIATED		INITIATED

PRONVBIT	=	BIT7

# BIT 6 FLAG 4
PINBRFLG	=	069D		# ASTRONAUT HAS		ASTRONAUT HAS NOT
					# INTERFERED WITH	INTERFERED WITH
					# EXISTING DISPLAY	EXISTING DISPLAY

PINBRBIT	=	BIT6

# BIT 5 FLAG 4
MRUPTFLG	=	070D		# MARK DISPLAY		MARK DISPLAY NOT
					# INTERRUPTED BY	INTERRUPTED BY
					# PRIORITY DISPLAY	PRIORITY DISPLAY

MRUPTBIT	=	BIT5
# Page 55

# BIT 4 FLAG 4
NRUPTFLG	=	071D		# NORMAL DISPLAY	NORMAL DISPLAY NOT
					# INTERRUPTED BY	INTERRUPTED BY
					# PRIORITY OR MARK	PRIORITY OR MARK
					# DISPLAY		DISPLAY

NRUPTBIT	=	BIT4

# BIT 3 FLAG 4
MKOVFLAG	=	072D		# MARK DISPLAY OVER	NO MARK DISPLAY OVER
					# NORMAL		NORMAL

MKOVBIT		=	BIT3

# BIT 2 FLAG 4				# DISPLAY BIT
		=	073D		# CLEARED AT INTERVALS

# BIT 1 FLAG 4
XDSPFLAG	=	074D		# MARK DISPLAY NOT TO	NO SPECIAL MARK
					# BE INTERRUPTED	INFORMATION

XDSPBIT		=	BIT1

FLAGWRD5	=	STATE +5	# (075-099)
					# (SET)			(RESET)

# BIT 15 FLAG 5
DSKYFLAG	=	075D		# DISPLAYS SENT TO	NO DISPLAYS TO DSKY
					# DSKY
DSKYBIT		=	BIT15

# BIT 14 FLAG 5
RETROFLG	=	076D		# P37 PREMANEUVER	ORBIT NOT RETROGRADE
RETROBIT	=	BIT14		# ORBIT IS RETROGRADE

# BIT 13 FLAG 5
SLOWFLG		=	077D		# P37 TRANSEARTH	SLOW DOWN IS NOT
SLOWBIT		=	BIT13		# COAST SLOW DOWN	DESIRED
					# IS DESIRED

# BIT 12 FLAG 5
V59FLAG		=	078D		# CALIBRATING FOR	NORMAL MARKING FOR
					# P23			P23

V59FLBIT	=	BIT12

# BIT 11 FLAG 5
# Page 56
INCORFLG	=	079D		# FIRST INCORPORATION	SECOND INCORPORATION

INCORBIT	=	BIT11

# BIT 10 FLAG 5
RNGSCFLG	=	080D		# ANOTHER TAG FOR PRFTRKAT

# BIT 10 FLAG 5
PRFTRKAT	=	RNGSCFLG	# PREF TRACK ATT	+K AXIS TRACK ATT

PRFTRBIT	=	BIT10

# BIT 9 FLAG 5
DMENFLG		=	081D		# DIMENSION OF W IS 9	DIMENSION OF W IS 6
					# FOR INCORPORATION	FOR INCORPORATION

DMENFBIT	=	BIT9

# BIT 8 FLAG 5
COMPUTER	=	082D		# COMPUTER IS CMC	COMPUTER IS LGC

COMPTBIT	=	BIT8

# BIT 7 FLAG 5
ENGONFLG	=	083D		# ENGINE TURNED ON	ENGINE TURNED OFF

ENGONBIT	=	BIT7

# BIT 6 FLAG 5
3AXISFLG	=	084D		# MANEUVER SPECIFIED	MANEUVER SPECIFIED
					# BY THREE AXES		BY ONE AXIS

3AXISBIT	=	BIT6

# BIT 5 FLAG 5
GRRBKFLG	=	085D		# BACKUP GRR RECEIVED	BACKUP GRR NOT
					#			RECEIVED

GRRBKBIT	=	BIT5

# BIT 4 FLAG 5
R60FLAG		=	086D		# R61 MUST USE R60	NORMAL R61

R60FLBIT	=	BIT4

# BIT 3 FLAG 5
SOLNSW		=	087D		# LAMBERT DOES NOT	LAMBERT CONVERGES OR
# Page 57
					# CONVERGE, OR TIME-	TIME-RADIUS NON
					# RADIUS NEARLY CIRC.	CIRCULAR.
SOLNSBIT	=	BIT3

# BIT 2 FLAG 5
MGLVFLAG	=	088D		# LOCAL VERTICAL	MIDDLE GIMBAL ANGLE
					# COORDINATES 		COMPUTED
					# COMPUTED

MGLVFBIT	=	BIT2

# BIT 1 FLAG 5
RENDWFLG	=	089D		# W MATRIX VALID	W MATRIX INVALID
					# FOR RENDEZVOUS	FOR RENDEZVOUS
					# NAVIGATION		NAVIGATION

RENDWBIT	=	BIT1

FLAGWRD6	=	STATE +6	# (090-104)
					# (SET)			(RESET)

# BIT 15 FLAG 6
DAPBIT1		=	090D		# 1 SATURN 1 TVC	0 RCS	0 NO

DAP1BIT		=	BIT15

# BIT 14 FLAG 6
DAPBIT2		=	091D		# 1 A/P    0 A/P	1 A/P	0 A/P

DAP2BIT		=	BIT14

# BIT 13 FLAG 6
STRULLSW	=	092D		# DO STEERULL		DO ULAGEOFF ONLY

STRULBIT	=	BIT13

# BIT 13 FLAG 6
ENTRYDSP	=	STRULLSW	# DO ENTRY DISPLAY	OMIT ENTRY DISPLAY
					# VIA ENTRYVN.

ENDSPBIT	=	BIT13

# BIT 12 FLAG 6
CMDAPARM	=	093D		# ALLOW ENTRY FIRINGS	INHIBIT ENTRY FIRING
					# AND CALCULATIONS	AND CONTROL FUNCTION
# Page 58
CMDARMBIT	=	BIT12

# BIT 11 FLAG 6
GAMDIFSW	=	094D		# CALCULATE GAMDOT	GAMDOT NOT TO BE
					#			CALCULATED

GMDIFBIT	=	BIT11

# BIT 10 FLAG 6
GONEPAST	=	095D		# LATERAL CONTROL	LATERAL CONTROL
					# CALCULATIONS TO BE	CALCULATIONS TO BE
					# OMITTED		DONE

GONEBIT		=	BIT10

# BIT 9 FLAG 6
RELVELSW	=	096D		# TARGETING USES	TARGETING USES
					# EARTH-RELATIVE	INERTIAL VELOCITY
					# VELOCITY.

RELVBIT		=	BIT9

# BIT 8 FLAG 6
EGSW		=	097D		# IN FINAL PHASE	NOT IN FINAL PHASE

EGFLGBIT	=	BIT8

# BIT 8 FLAG 6
KNOWNFLG	=	EGSW		# LANDMARK KNOWN	LANDMARK UNKNOWN

KNOWNBIT	=	BIT8

# BIT 8 FLAG 6
R57FLAG		=	KNOWNFLG	# DO NOT DO R57		DO R57, TRUNION
					# TRUNION BIAS HAS	BIAS NEEDED
					# BEEN OBTAINED.

R57BIT		=	BIT8

# BIT 7 FLAG 6
NOSWITCH	=	098D		# LATERAL ROLL		LATERAL ROLL MANEUVER
					# MANEUVER INHIBITED	PERMITTED IN ENTRY
					# IN ENTRY

NOSWBIT		=	BIT7

# BIT 6 FLAG 6
HIND		=	099D		# ITERATING HUNTEST	ITERATING OF HUNTEST
					# CALCULATIONS TO BE	CALCULATIONS TO BE
					# DONE AFTER RANGE	OMITTED AFTER RANGE
					# PREDICTION		PREDICTION
# Page 59

HINDBIT		= 	BIT6

# BIT 5 FLAG 6
INRLSW		=	100D		# INITIAL ROLL		INITIAL ROLL
					# V(LV)			V(LV)

INRLBIT		=	BIT5

					# ATTITUDE NOT HELD	ATTITUDE HELD

# BIT 4 FLAG 6
LATSW		=	101D		# DOWNLIFT NOT		DOWNLIFT INHIBITED
					# INHIBITED

LATSWBIT	=	BIT4

# BIT 3 FLAG 6
.05GSW		=	102D		# DRAG OVER .05G	DRAG LESS THAN .05G

.05GBIT		=	BIT3

# BIT 3 FLAG 6
		=	102D

# BIT 2 FLAG 6
CM/DSTBY	=	103D		# ENTRY DAP ACTIVATED	ENTRY DAP NOT
					#			ACTIVATED

CM/DSBIT	=	BIT2

# BIT 1 FLAG 6
GYMDIFSW	=	104D		# CDU DIFFERENCES AND 	CDU DIFFERENCES AND
					# BODY RATES COMPUTED	BODY RATES NOT
					#			COMPUTED

GYMDIBIT	=	BIT1

FLAGWRD7	=	STATE +7	# (105-119)
					# (SET)			(RESET)

# BIT 15 FLAG 7
TERMIFLG	=	105D		# TERMINATE R52		DO NOT TERMINATE R52

TERMIBIT	=	BIT15

# BIT 14 FLAG 7
ITSWICH		=	106D		# ACCEPT NEXT LAMBERT	TEST LAMBERT ANSWER
					# TPI SEARCH SOLUTION	AGAINST LIMITS
# Page 60

ITSWBIT		=	BIT14

# BIT 13 FLAG 7
IGNFLAG		=	107D		# TIG HAS ARRIVED	TIG HAS NOT ARRIVED

IGNFLBIT	=	BIT13

# BIT 12 FLAG 7
ASTNFLAG	=	108D		# ASTRONAUT HAS		ASTRONAUT HAS NOT
					# OKAYED IGNITION	OKAYED IGNITION

ASTNBIT		=	BIT12

# BIT 11 FLAG 7
TIMRFLAG	=	109D		# CLOKTASK OPERATING	CLOKTASK INOPERATIVE

TIMRBIT		=	BIT11

# BIT 10 FLAG 7
NORMSW		=	110D		# UNIT NORMAL INPUT	LAMBERT COMPUTE ITS
					# TO LAMBERT.		OWN UNIT NORMAL.

NORMSBIT	=	BIT10

# BIT 9 FLAG 7
RVSW		=	111D		# DO NOT COMPUTE FINAL	COMPUTE FINAL STATE
					# STATE VECTOR IN	VECTOR IN TIME-THETA
					# TIME-THETA

RVSWBIT		=	BIT9

# BIT 8 FLAG 7
GONEBY		=	112D		# PASSED TARGET		APPROACHING TARGET

GONBYBIT	=	BIT8

# BIT 7 FLAG 7
		=	113D

# BIT 6 FLAG 7
V37FLAG		=	114D		# AVERAGEG (SERVICER)	AVERAGEG (SERVICER)
					# RUNNING		OFF

V37FLBIT	=	BIT6

# BIT 5 FLAG 7
		=	115D
# Page 61
		=	BIT5

# BIT 4 FLAG 7
UPLOCKFL	=	116D		# K-KBAR-K FAIL		NO K-KBAR-K FAIL

UPLOCBIT	=	BIT4

# BIT 3 FLAG 7
VERIFLAG	=	117D		# CHANGED WHEN V33E OCCURS AT END OF P27

VERIFBIT	=	BIT3

# BIT 2 FLAG 7
ATTCHFLG	=	118D		# LM,CM ATTACHED	LM,CM NOT ATTACHED

ATTCHBIT	=	BIT2

# BIT 1 FLAG 7
TFFSW		=	119D		# CALCULATE TPERIGEE	CALCULATE TFF

TFFSWBIT	=	BIT1

FLAGWRD8	=	STATE +8D	# (120-134)
					# (SET)			(RESET)

# BIT 15 FLAG 8
RPQFLAG		=	120D		# RPQ NOT COMPUTED	RPQ COMPUTED

RPQFLBIT	=	BIT15

# BIT 14 FLAG 8
		=	121D

# BIT 13 FLAG 8
NEWIFLG		=	122D		# FIRST PASS THROUGH	SUCCEEDING ITERATION
					# INTEGRATION		OF INTEGRATION

NEWIBIT		=	BIT13

# BIT 12 FLAG 8
CMOONFLG	=	123D		# PERMANENT CSM STATE	PERMANENT CSM STATE
					# IN LUNAR SPHERE	IN EARTH SPHERE

CMOONBIT	=	BIT12

# BIT 11 FLAG 8
LMOONFLG	=	124D		# PERMANENT LM STATE	PERMANENT LM STATE
					# IN LUNAR SPHERE	IN EARTH SPHERE

LMOONBIT	=	BIT11

# Page 62

# BIT 10 FLAG 8
ADVTRK		=	125D		# ADVANCE GROUND TRACK	NOT ADVANCED
					# SIGHTING WANTED	GROUND TRACK

ADVTKBIT	=	BIT10

# BIT 9 FLAG 8
P39/79SW	=	126D		# P39/79 OPERATING	P38/78 OPERATING

P39SWBIT	=	BIT9

# BIT 8 FLAG 8
SURFFLAG	=	127D		# LM ON LUNAR SURFACE	LM NOT ON LUNAR
					#			SURFACE

SURFFBIT	=	BIT8

# BIT 7 FLAG 8
INFINFLG	=	128D		# NO CONIC SOLUTION	CONIC SOLUTION
					# (CLOSURE THROUGH	EXISTS.
					# INFINITY REQUIRED).

INFINBIT	=	BIT7

# BIT 6 FLAG 8
ORDERSW		=	129D		# ITERATOR USES 2ND	ITERATOR USES 1ST
					# ORDER MINIMUM MODE	ORDER STANDARD MODE

ORDERBIT	=	BIT6

# BIT 5 FLAG 8
APSESW		=	130D		# RDESIRED OUTSIDE	RDESIRED INSIDE
					# PERICENTER-APOCENTER	PERICENTER-APOCENTER
					# RANGE IN TIME-RAD	RANGE IN TIME-RADIUS

APSESBIT	=	BIT5

# BIT 4 FLAG 8
COGAFLAG	=	131D		# NO CONIC SOLUTION 	CONIC SOLUTION
					# TOO CLOSE TO		EXISTS (COGA DOES
					# RECTILINEAR (COGA	NOT OVERFLOW).
					# OVERFLOWS).
COGAFBIT	=	BIT4

# Page 63

# BIT 3 FLAG 8
V96ONFLG	=	132D		# P00 INTEGRATION HAS	P00 INTEGRATION IS
					# BEEN INHIBITED BY	PROCEEDING REGULARLY
					# V96

# BIT 2 FLAG 8
		=	133D

# BIT 1 FLAG 8
360SW		=	134D		# TRANSFER ANGLE NEAR	TRANSFER ANGLE NOT
					# 360 DEGREES		NEAR 360 DEGREES

360SWBIT	=	BIT1

FLAGWRD9	=	STATE +9D	# (135-149)
					# (SET)			(RESET)

# BIT 15 FLAG 9
SWTOVER		=	135D		# SWITCHOVER HAS	NO SWITCHOVER YET
					# OCCURRED.

SWTOVBIT	=	BIT15

# BIT 14 FLAG 9
		=	136D

V67FLBIT	=	BIT14

# BIT 13 FLAG 9
V82EMFLG	=	137D		# MOON VICINITY		EARTH VICINITY

V82EMBIT	=	BIT13

# BIT 12 FLAG 9
MAXDBFLG	=	138D		# MAX DB SELECTED	MIN DB SELECTED

MAXDBBIT	=	BIT12

# BIT 11 FLAG 9
V94FLAG		=	139D		# V94 ALLOWED DURING	V94 NOT ALLOWED
					# P23

V94FLBIT	=	BIT11

# BIT 10 FLAG 9
SAVECFLG	=	140D		# P23 DISPLAY AND	P23 DISPLAY AND
					# DATA STORAGE AFTER	DATA STORAGE BEFORE
# Page 64
					# MARK IS DONE		MARK IS DONE

SAVECBIT	=	BIT10

# BIT 9 FLAG 9
VHFRFLAG	=	141D		# ALLOW R22 TO 		STOP ACCEPTANCE
					# ACCEPT RANGE		OF RANGE DATA
					# DATA

VHFRBIT		=	BIT9

# BIT 8 FLAG 9
SOURCFLG	=	142D		# SOURCE OF INPUT	SOURCE OF INPUT
					# DATA IS FROM		DATA IS FROM
					# VHF RADAR		OPTICS MARK

SOURCBIT	=	BIT8

# BIT 7 FLAG 9
R22CAFLG	=	143D		# R-22 CALCULATIONS	R-22 CALCULATIONS
					# ARE GOING ON		ARE NOT GOING ON

R22CABIT	=	BIT7

# BIT 6 FLAG 9
N22ORN17	=	144D		# COMPUTE TOTAL		COMPUTE TOTAL
					# ATTITUDE ERRORS	ATTITUDE ERRORS
					# W.R.T. N22 (V62)	W.R.T. N17 (V63)

N2217BIT	=	BIT6

# BIT 5 FLAG 9
QUITFLAG	=	145D		# TERMINATE AND EXIT	CONTINUE INTEGRATION
QUITBIT		=	BIT5		# FROM INTEGRATION

# BIT 4 FLAG 9
R31FLAG		=	146D		# R31 SELECTED (V63)	R34 SELECTED (V65)

R31FLBIT	=	BIT4

# BIT 3 FLAG 9
MID1FLAG	=	147D		# INTEGRATE TO TDEC	INTEGRATE TO THE
					#			THEN-PRESENT TIME

MID1FBIT	=	BIT3

# BIT 2 FLAG 9
MIDAVFLG	=	148D		# INTEGRATION ENTERED	INTEGRATION WAS
					# FROM ONE OF MIDTOAV	NOT ENTERED VIA
					# PORTALS		MIDTOAV
# Page 65

MIDAVBIT	=	BIT2

# BIT 1 FLAG 9
AVEMIDSW	=	149D		# AVETOMID CALLING	NO AVETOMID W INTEGER
					# FOR W MATRIX INTEGR	ALLOW SET UP RN,VN,
					# DON'T WRITE OVER RN,	PIPTIME
					# VN,PIPTIME

AVEMDBIT	=	BIT1

FLGWRD10	=	STATE	+10D	# (150-164)
					# (SET)			(RESET)

RASFLAG		=	STATE	+10D

# BIT 15 FLAG 10
		=	150D

# BIT 14 FLAG 10
INTFLAG		=	151D		# INTEGRATION IN	INTEGRATION NOT IN
					# PROGRESS		PROGRESS

INTFLBIT	=	BIT14

# BIT 13 FLAG 10
		=	152D

# BIT 12 FLAG 10
		=	153D

# BIT 11 FLAG 10
		=	154D

# BIT 10 FLAG 10
		=	155D

# BIT 9 FLAG 10
		=	156D

# BIT 8 FLAG 10
		=	157D
# Page 66

# BIT 7 FLAG 10
REINTFLG	=	158D		# INTEGRATION ROUTINE	INTEGRATION ROUTINE
					# TO BE RESTARTED	NOT TO BE RESTARTED
REINTBIT	=	BIT7

# BIT 6 FLAG 10
		=	159D

# BIT 5 FLAG 10
		=	160D

# BIT 4 FLAG 10
		=	161D

# BIT 3 FLAG 10
		=	162D

# BIT 2 FLAG 10
		=	163D

# BIT 1 FLAG 10
		=	164D

FLGWRD11	=	STATE	+11D	# (165-179)
					# (SET)			(RESET)

# BIT 15 FLAG 11
S32.1F1		=	165D		# DELTAN AT CSI TIME	DVT1 LESS THAN MAX
S32BIT1		=	BIT15		# ONE EXCEEDS MAX

# BIT 14 FLAG 11
S32.1F2		=	166D		# FIRST PASS OF 	REITERATION OF
S32BIT2		=	BIT14		# NEWTON INTEGRATION	NEWTON

# BIT 13 FLAG 11
S32.1F3A	=	167D		# BIT 13 AND BIT 12 FUNCTION AS AN ORDERED
S32BIT3A	=	BIT13		# PAIR (13,12) INDICATING THE POSSIBLE OC-
					# CURRENCE OF 2NEWTON ITERATIONS FOR S32.1
# BIT 12 FLAG 11			# IN THE PROGRAM IN THE FOLLOWING ORDER:
S32.1F3B	=	168D		# (0,1) (I.E. BIT 13 RESET, BIT 12 SET)
# Page 67
S3229T3B	=	BIT12		#      = FIRST NEWTON ITERATION BEING DONE
					# (0,0)= FIRST PASS OF 2ND NEWTON ITER.
					# (1,1)= 50 FPS STAGE OF 2ND NEWT ITER.
					# (1,0)= REMAINDER OF 2ND NEWT ITER.

# BIT 11 FLAG 11
		=	169D

# BIT 10 FLAG 11
		=	170D

# BIT 9 FLAG 11
		=	171D

# BIT 8 FLAG 11
		=	172D

# BIT 7 FLAG 11
		=	173D

# BIT 6 FLAG 11
		=	174D

# BIT 5 FLAG 11
		=	175D

# BIT 4 FLAG 11
		=	176D

# BIT 3 FLAG 11
		=	177D

# BIT 2 FLAG 11
		=	178D

# BIT 1 FLAG 11
		=	179D

# Page 68
# GENERAL ERASABLE ASSIGNMENTS

		SETLOC	61
# INTERRUPT TEMPORARY STORAGE POOL.	(11D)

# (ITEMP1 THROUGH RUPTREG4)

# ANY OF THESE MAY BE USED AS TEMPORARIES DURING INTERRUPT OR WITH INTERRUPT INHIBITED. THE ITEMP SERIES
# IS USED DURING CALLS TO THE EXECUTIVE AND WAITLIST -- THE RUPTREGS ARE NOT.

ITEMP1		ERASE
WAITEXIT	EQUALS	ITEMP1
EXECTEM1	EQUALS	ITEMP1

ITEMP2		ERASE
WAITBANK	EQUALS	ITEMP2
EXECTEM2	EQUALS	ITEMP2

ITEMP3		ERASE
RUPTSTOR	EQUALS	ITEMP3
WAITADR		EQUALS	ITEMP3
NEWPRIO		EQUALS	ITEMP3

ITEMP4		ERASE
LOCCTR		EQUALS	ITEMP4
WAITTEMP	EQUALS	ITEMP4

ITEMP5		ERASE
NEWLOC		EQUALS	ITEMP5

ITEMP6		ERASE
NEWLOC+1	EQUALS	ITEMP6		# DP ADDRESS.

		SETLOC	67
NEWJOB		ERASE			# MUST BE AT LOC 67 DUE TO WIRING.

RUPTREG1	ERASE
RUPTREG2	ERASE
RUPTREG3	ERASE
RUPTREG4	ERASE
KEYTEMP1	EQUALS	RUPTREG4
DSRUPTEM	EQUALS	RUPTREG4

# FLAGWORD RESERVATIONS.		(12D)

STATE		ERASE	+11D

# PAD LOAD FOR DAPS
EMDOT		ERASE			# I(1)PL (SPS FLOW RATE, SC AT B+3 KG/CS)

# Page 69

# EXIT FOR V83
STATEXIT	ERASE			# I(1) STQ ADDRESS FOR STATEXIT

# UNUSED ERASABLES ********(2)
ERASFILL	ERASE	+1

# EXEC TEMPORARIES WHICH MAY BE USED BETWEEN CCS NEWJOBS
# (INTB15+ THROUGH RUPTMXTM)		(32D)
INTB15+		ERASE			# REFLECTS 15TH BIT OF INDEXABLE ADDRESSES
DSEXIT		EQUALS	INTB15+		# RETURN FOR DSPIN
EXITEM		EQUALS	INTB15+		# RETURN FOR SCALE FACTOR ROUTINE SELECT
BLANKRET	EQUALS	INTB15+		# RETURN FOR 2BLANK

INTBIT15	ERASE			# SIMILAR TO ABOVE.
WRDRET		EQUALS	INTBIT15	# RETURN FOR 5BLANK.
WDRET		EQUALS	INTBIT15	# RETURN FOR DSPWD
DECRET		EQUALS	INTBIT15	# RETURN FOR PUTCOM(DEC LOAD)
21/22REG	EQUALS	INTBIT15	# TEMP FOR CHARIN

# THE REGISTERS BETWEEN ADDRWD AND PRIORITY MUST STAY IN THE FOLLOWING ORDER FOR INTERPRETIVE TRACE.

ADDRWD		ERASE			# 12 BIT INTERPRETIVE OPERAND SUB-ADDRESS.
POLISH		ERASE			# HOLDS CADR MADE FROM POLISH ADDRESSE.
UPDATRET	EQUALS	POLISH		# RETURN FOR UPDATNN, UPDATVB
CHAR		EQUALS	POLISH		# TEMP FOR CHARIN
ERCNT		EQUALS	POLISH		# COUNTER FOR ERROR LIGHT RESET
DECOUNT		EQUALS	POLISH		# COUNTER FOR SCALING AND DISPLAY (DEC)

FIXLOC		ERASE			# WORK AREA ADDRESS
OVFIND		ERASE			# SET NON-ZERO ON OVERFLOW.

VBUF		ERASE	+5		# TEMPORARY STORAGE USED FOR VECTORS.
SGNON		EQUALS	VBUF		# TEMP FOR +,- ON
NOUNTEM		EQUALS	VBUF		# COUNTER FOR MIXNOUN FETCH
DISTEM		EQUALS	VBUF		# COUNTER FOR OCTAL DISPLAY VERB
DECTEM		EQUALS	VBUF		# COUNTER FOR FETCH (DEC DISPLAY VERBS)

SGNOFF		EQUALS	VBUF +1		# TEMP FOR +,- ON
NVTEMP		EQUALS	VBUF +1		# TEMP FOR NVSUB
SFTEMP1		EQUALS	VBUF +1		# STORAGE FOR SF CONST HI PART (=SFTEMP2-1)
HITEMIN		EQUALS	VBUF +1		# TEMP FOR LOAD OF HRS,MIN,SEC
					# MUST = LOTEMIN-1.

CODE		EQUALS	VBUF +2		# FOR DSPIN
SFTEMP2		EQUALS	VBUF +2		# STORAGE FOR SF CONST LO PART (=SFTEMP1+1)
LOTEMIN		EQUALS	VBUF +2		# TEMP FOR LOAD OF HRS,MIN,SEC
# Page 70
					# 	MUST = HITEMIN+1
MIXTEMP		EQUALS	VBUF +3		# FOR MIXNOUN DATA
SIGNRET		EQUALS	VBUF +3		# RETURN FOR +,- ON

# ALSO MIXTEMP+1 = VBUF+4, MIXTEMP+2 = VBUF+5

BUF		ERASE	+2		# TEMPORARY SCALAR STORAGE
BUF2		ERASE	+1
INDEXLOC	EQUALS	BUF		# CONTAINS ADDRESS OF SPECIFIED INDEX.
SWWORD		EQUALS	BUF		# ADDRESS OF SWITCH WORD
SWBIT		EQUALS	BUF +1		# SWITCH BIT WITHIN THE SWITCH WORD
MPTEMP		ERASE			# TEMPORARY USED IN MULTIPLY AND SHIFT
DMPNTEMP	EQUALS	MPTEMP		# DMPSUB TEMPORARY
DOTINC		ERASE			# COMPONENT INCREMENT FOR DOT SUBROUTINE
DVSIGN		EQUALS	DOTINC		# DETERMINES SIGN OF DDV RESULT
ESCAPE		EQUALS	DOTINC		# USED IN ARCSIN/ARCCOS.
ENTRET		EQUALS	DOTINC		# EXIT FROM ENTER

DOTRET		ERASE			# RETURN FROM DOT SUBROUTINE
DVNORMCT	EQUALS	DOTRET		# DIVIDEND NORMALIZATION COUNT IN DDV.
ESCAPE2		EQUALS	DOTRET		# ALTERNATE ARCSIN/ARCCOS SWITCH
WDCNT		EQUALS	DOTRET		# CHAR COUNTER FOR DSPWD
INREL		EQUALS	DOTRET		# INPUT BUFFER SELECTOR ( X,Y,Z, REG )

MATINC		ERASE			# VECTOR INCREMENT IN MXV AND VXM
MAXDVSW		EQUALS	MATINC		# +0 IF DP QUOTIENT IS NEAR ONE -- ELSE -1.
POLYCNT		EQUALS	MATINC		# POLYNOMIAL LOOP COUNTER
DSPMMTEM	EQUALS	MATINC		# DSPCOUNT SAVE FOR DSPMM
MIXBR		EQUALS	MATINC		# INDICATOR FOR MIXED OR NORMAL NOUN

TEM1		ERASE			# EXEC TEMP
POLYRET		EQUALS	TEM1
DSREL		EQUALS	TEM1		# REL ADDRESS FOR DSPIN

TEM2		ERASE			# EXEC TEMP
DSMAG		EQUALS	TEM2		# MAGNITUDE STORE FOR DSPIN
IDADDTEM	EQUALS	TEM2		# MIXNOUN INDIRECT ADDRESS STORAGE

TEM3		ERASE			# EXEC TEMP
COUNT		EQUALS	TEM3		# FOR DSPIN

TEM4		ERASE			# EXEC TEMP
LSTPTR		EQUALS	TEM4		# LIST POINTER FOR GRABUSY
RELRET		EQUALS	TEM4		# RETURN FOR RELDSP
FREERET		EQUALS	TEM4		# RETURN FOR FREEDSP
DSPWDRET	EQUALS	TEM4		# RETURN FOR DSPSIGN
SEPSCRET	EQUALS	TEM4		# RETURN FOR SEPSEC
SEPMNRET	EQUALS	TEM4		# RETURN FOR SEPMIN

TEM5		ERASE			# EXEC TEMP
# Page 71
NOUNADD		EQUALS	TEM5		# TEMP STORAGE FOR NOUN ADDRESS

NNADTEM		ERASE			# TEMP FOR NOUN ADDRESS TABLE ENTRY
NNTYPTEM	ERASE			# TEMP FOR NOUN TYPE TABLE ENTRY
IDAD1TEM	ERASE			# TEMP FOR INDIR ADDRESS TABLE ENTRY (MIXNN)
					# MUST = IDAD2TEM-1, = IDAD3TEM-2
IDAD2TEM	ERASE			# TEMP FOR INDIR ADDRESS TABLE ENTRY (MIXNN)
					# MUST = IDAD1TEM+1, IDAD3TEM-1.
IDAD3TEM	ERASE			# TEMP FOR INDIR ADDRESS TABLE ENTRY (MIXNN)
					# MUST = IDAD1TEM+2, IDAD2TEM+1.
RUTMXTEM	ERASE			# TEMP FOR SF ROUT TABLE ENTRY (MIXNN ONLY)

# AX*SR*T STORAGE.			(6D)
DEXDEX		EQUALS	TEM2		# B(1)TMP
DEX1		EQUALS	TEM3		# B(1)TMP
DEX2		EQUALS	TEM4		# B(1)TMP
RTNSAVER	EQUALS	TEM5		# B(1)TMP
TERM1TMP	EQUALS	BUF2		# B(2)TMP

DEXI		=	DEX1

# Page 72
# DYNAMICALLY ALLOCATED CORE SETS FOR JOBS	(84D)

MPAC		ERASE	+6		# MULTI-PURPOSE ACCUMULATOR.
MODE		ERASE			# +1 FOR TP, +0 FOR DP, OR -1 FOR VECTOR.
LOC		ERASE			# LOCATION ASSOCIATED WITH JOB.
BANKSET		ERASE			# USUALLY CONTAINS BBANK SETTING.
PUSHLOC		ERASE			# WORD OF PACKED INTERPRETIVE PARAMETERS.
PRIORITY	ERASE			# PRIORITY OF PRESENT JOB AND WORK AREA.

		ERASE	+71D		# SEVEN SETS OF 12 REGISTERS EACH

# SPECIAL DOWNLINK BUFFER. -- OVERLAYED BY P27 STORAGE --

# P27 (UPDATE PROGRAM) STORAGE. -- OVERLAYS SPEC DNLNK BUFF -- (24D)

COMPNUMB	ERASE	+23D		# B(1)TMP NUMBER OF ITEMS TO BE UPLINKED.
UPOLDMOD	EQUALS	COMPNUMB +1	# B(1)TMP HOLDS INTERRUPTED PROGRAM NUMBER
UPVERB		EQUALS	UPOLDMOD +1	# B(1)TMP VERB NUMBER
UPCOUNT		EQUALS	UPVERB +1	# B(1)TMP UPBUFF INDEX
UPBUFF		EQUALS	UPCOUNT +1	# B(20D)

# MORE P27 STORAGE.			(2D)

UPTEMP		ERASE			# B(1)TMP SCRATCH
UPVERBSV	ERASE			# B(1)TMP
INTWAK1Q	EQUALS	UPTEMP		# (06D)
# (20 REGISTERS OF ENTRY DOWNLINK WILL GO HERE.)

# THE FOLLOWING ARE INDEXED FOR TM. IN ENTRY DAP.

CMTMTIME	=	UPBUFF		# B(1) (VEHICLE BODY RATE INFO IS
SW/NDX		=	CMTMTIME +1	# B(1)  TELEMETERED EACH 0.2 SEC. DURING
ENDBUF		=	CMTMTIME +16D	# B(1)  ENTRY.)

V1		=	ENDBUF +1	# I(2) REENTRY, P64-P65
A0		=	V1 +2		# I(2) REENTRY, P64-P65
					# HI-ORDER WORD ONLY ON DNLNK.

# ALIGNMENT STORAGE.			(5D)
# (CANNOT SHARE WITH PRECISION INTEGRATION OR KEPLER STORAGE.)

QMAJ		EQUALS	COMPNUMB	# B(1)TMP
MARKINDX	EQUALS	QMAJ	+1	# B(1)TMP
BESTI		EQUALS	MARKINDX +1	# I(1)TMP
BESTJ		EQUALS	BESTI	+1	# I(1)TMP
STARIND		EQUALS	BESTJ	+1	# I(1)TMP

# Page 73
# ALIGNMENT/S40.2,3 COMMON STORAGE.	(18D)

XSMD		EQUALS	UPBUFF	+2	# I(6)TMP
YSMD		EQUALS	XSMD	+6	# I(6)TMP
ZSMD		EQUALS	YSMD	+6	# I(6)TMP

XSCREF		=	XSMD		# SPACE CRAFT AXES IN REF COORDS.
YSCREF		=	YSMD
ZSCREF		=	ZSMD
ZPRIME		=	22D
PDA		=	22D
COSTH		=	16D
SINTH		=	18D
THETA		=	20D
STARM		=	32D

# DOWNLINK STORAGE			(18D)

DNLSTADR	EQUALS	DNLSTCOD	# CONTENTS NO LONGER AN ADDR BUT A CODE

DNLSTCOD	ERASE			# B(1)PRM ID CODE OF DOWNLIST
DUMPCNT		ERASE			# B(1)PRM
LDATALST	ERASE			# B(1)
DNTMGOTO	ERASE			# B(1)
TMINDEX		ERASE			# B(1)
DUMPLOC		EQUALS	TMINDEX		# CONTAINS ECADR OF AGC DP WORD BEING DUMPED
					# AND COUNT OF COMPLETE DUMPS ALREADY
					# SENT.
DNQ		ERASE			# B(1)
DNTMBUFF	ERASE	+11D		# B(12)PRM DOWNLINK SNAPSHOT BUFFER

# OPTICS MARKING, UNSHARED.		(8D)

MKNDX		ERASE
MKT2T1		ERASE	+1
MKCDUY		ERASE
MKCDUS		ERASE
MKCDUZ		ERASE
MKCDUT		ERASE
MKCDUX		ERASE

# FOR EXCLUSIVE USE OF SYS TEST STANDARD LEAD INS	(2)
EBUF2		ERASE	+1		# B(2) UNSHARED

# Page 74
# UNSWITCHED FOR DISPLAY INTERFACE ROUTINES.	(10D)

RESTREG		ERASE			# B(1)PRM FOR DISPLAY RESTARTS.
NVWORD		ERASE
MARKNV		ERASE
NVSAVE		ERASE
# (RETAIN THE ORDER OF CADRFLSH TO FAILREG +2 FOR DOWNLINK PURPOSES)
CADRFLSH	ERASE			# B(1)TMP
CADRMARK	ERASE			# B(1)TMP
TEMPFLSH	ERASE			# B(1)TMP
FAILREG		ERASE	+2		# B(3)PRM 3 ALARM CODE REGISTERS

		SETLOC	400

# VAC AREAS. -- BE CAREFUL OF PLACEMENT --	(220D)

VAC1USE		ERASE			# B(1)PRM
VAC1		ERASE	+42D		# B(43)PRM
VAC2USE		ERASE			# B(1)PRM
VAC2		ERASE	+42D		# B(43)PRM
VAC3USE		ERASE			# B(1)PRM
VAC3		ERASE	+42D		# B(43)PRM
VAC4USE		ERASE			# B(1)PRM
VAC4		ERASE	+42D		# B(43)PRM
VAC5USE		ERASE			# B(1)PRM
VAC5		ERASE	+42D		# B(43)PRM

# WAITLIST REPEAT FLAG.			(1D)
RUPTAGN		ERASE			# B(1)PRM
KEYTEMP2	=	RUPTAGN

# STARALIGN ERASABLES.				(13D)

STARCODE	ERASE			# B(1)DSP NOUN 70 FOR P22,51 AND R52,53
STARALGN	ERASE	+11D
SINCDU		=	STARALGN
COSCDU		=	STARALGN +6

SINCDUX		=	SINCDU +4
SINCDUY		=	SINCDU
SINCDUZ		=	SINCDU +2
COSCDUX		=	COSCDU +4
COSCDUY		=	COSCDU
COSCDUZ		=	COSCDU +2

# PHASE TABLE AND RESTART COUNTERS		(12D)
# Page 75

-PHASE1		ERASE			# B(1)PRM
PHASE1		ERASE			# B(1)PRM
-PHASE2		ERASE			# B(1)PRM
PHASE2		ERASE			# B(1)PRM
-PHASE3		ERASE			# B(1)PRM
PHASE3		ERASE			# B(1)PRM
-PHASE4		ERASE			# B(1)PRM
PHASE4		ERASE			# B(1)PRM
-PHASE5		ERASE			# B(1)PRM
PHASE5		ERASE			# B(1)PRM
-PHASE6		ERASE			# B(1)PRM
PHASE6		ERASE			# B(1)PRM

# A**SR*T STORAGE			(6D)

CDUSPOT		ERASE	+5		# B(6)

CDUSPOTY	=	CDUSPOT
CDUSPOTZ	=	CDUSPOT +2
CDUSPOTX	=	CDUSPOT +4

# VERB 37 STORAGE			(2D)

MINDEX		ERASE			# B(1)TMP INDEX FOR MAJOR MODE
MMNUMBER	ERASE			# B(1)TMP MAJOR MODE REQUESTED BY V37

# PINBALL INTERRUPT ACTION		(1D)

DSPCNT		ERASE			# B(1)PRM COUNTER FOR DSPOUT

# PINBALL EXECUTIVE ACTION		(44D)

DSPCOUNT	ERASE			# DISPLAY POSITION INDICATOR
DECBRNCH	ERASE			# +DEC, -DEC, OCT INDICATOR
VERBREG		ERASE			# VERB CODE
NOUNREG		ERASE			# NOUN CODE
XREG		ERASE			# R1 INPUT BUFFER
YREG		ERASE			# R2 INPUT BUFFER
ZREG		ERASE			# R3 INPUT BUFFER
XREGLP		ERASE			# LO PART OF XREG (FOR DEC CONV ONLY)
YREGLP		ERASE			# LO PART OF YREG (FOR DEC CONV ONLY)
HITEMOUT	=	YREGLP		# TEMP FOR DISPLAY OF HRS,MIN,SEC
					# 	MUST = LOTEMOUT-1.
ZREGLP		ERASE			# LO PART OF ZREG (FOR DEC CONV ONLY)
LOTEMOUT	=	ZREGLP		# TEMP FOR DISPLAY OF HRS,MIN,SEC
					# 	MUST = HITEMOUT+1
# Page 76
MODREG		ERASE			# MODE CODE
DSPLOCK		ERASE			# KEYBOARD/SUBROUTINE CALL INTERLOCK
REQRET		ERASE			# RETURN REGISTER FOR LOAD
LOADSTAT	ERASE			# STATUS INDICATOR FOR LOADTST
CLPASS		ERASE			# PASS INDICATOR FOR CLEAR
NOUT		ERASE			# ACTIVITY COUNTER FOR DSPTAB
NOUNCADR	ERASE			# MACHINE CADR FOR NOUN
MONSAVE		ERASE			# N/V CODE FOR MONITOR. (= MONSAVE1-1)
MONSAVE1	ERASE			# NOUNCADR FOR MONITOR (MATBS) = MONSAVE+1
MONSAVE2	ERASE			# B(1)PRM NVMONOPT OPTIONS
DSPTAB		ERASE	+11D		# 0-100, DISPLAY PANEL BUFF.  11D, C/S LTS.
NVQTEM		ERASE			# NVSUB STORAGE FOR CALLING ADDRESS
					# MUST = NVBNKTEM-1.
NVBNKTEM	ERASE			# NVSUB STORAGE FOR CALLING BANK
					# MUST = NVQTEM+1
VERBSAVE	ERASE			# NEEDED FOR RECYCLE
CADRSTOR	ERASE			# ENDIDLE STORAGE
DSPLIST		ERASE			# WAITING REG FOR DSP SYST INTERNAL USE
EXTVBACT	ERASE			# EXTENDED VERB ACTIVITY INTERLOCK
DSPTEM1		ERASE	+2		# BUFFER STORAGE AREA 1 (MOSTLY FOR TIME)
DSPTEM2		ERASE	+2		# BUFFER STORAGE AREA 2 (MOSTLY FOR DEG)

DSPTEMX		EQUALS	DSPTEM2 +1	# B(2) S-S DISPLAY BUFFER FOR EXT. VERBS
NORMTEM1	EQUALS	DSPTEM1		# B(3)DSP NORMAL DISPLAY REGISTERS.

# DISPLAY FOR EXTENDED VERBS		(2D)

OPTIONX		EQUALS	DSPTEMX		# B(2) EXTENDED VERB OPTION CODE  N12(V82)

# TBASE'S AND PHSPRDT'S.		(12D)

TBASE1		ERASE			# B(1)PRM
PHSPRDT1	ERASE			# B(1)PRM
TBASE2		ERASE			# B(1)PRM
PHSPRDT2	ERASE			# B(1)PRM
TBASE3		ERASE			# B(1)PRM
PHSPRDT3	ERASE			# B(1)PRM
TBASE4		ERASE			# B(1)PRM
PHSPRDT4	ERASE			# B(1)PRM
TBASE5		ERASE			# B(1)PRM
PHSPRDT5	ERASE			# B(1)PRM
TBASE6		ERASE			# B(1)PRM
PHSPRDT6	ERASE			# B(1)PRM

# UNSWITCHED FOR DISPLAY INTERFACE ROUTINES.	(5D)
# Page 77
EBANKSAV	ERASE
MARKEBAN	ERASE
EBANKTEM	ERASE
MARK2PAC	ERASE
R1SAVE		ERASE

# IMU COMPENSATION UNSWITCHED ERASABLE.	(1D)

1/PIPADT	ERASE			# B(1)PRM
OLDBT1		=	1/PIPADT

# SINGLE PRECISION SUBROUTINE TEMPORARIES	(3D)
					# SPSIN, SPCOS, SPROOT VARIABLES.
					# DO NOT SHARE.  THESE ARE USED BY DAPS IN INTERRUPT
					# AND CURRENTLY ARE NOT PROTECTED.  IF OTHER USERS
					# MATERIALIZE, THEN THIS CAN BE CHANGED.

HALFY		ERASE
ROOTRET		ERASE
SQRARG		ERASE
TEMK		EQUALS	HALFY
SQ		EQUALS	ROOTRET

# Page 78
# UNSWITCHED FOR ORBIT INTEGRATION	(21D)

TDEC		ERASE	+20D		# I(2)
COLREG		EQUALS	TDEC +2		# I(1)
LAT		EQUALS	COLREG +1	# I(2)DSP NOUN 43,67 FOR P20,22,51 R52,53.
LANDLAT		=	LAT		#	  NOUN 89    FOR P22.
LONG		EQUALS	LAT +2		# I(2)DSP NOUN 43,67 FOR P20,22,51 R52,53
ALT		EQUALS	LONG +2		# I(2)DSP NOUN 43    FOR P20,22,51 R52,53.
YV		EQUALS	ALT +2		# I(6)
ZV		EQUALS	YV +6		# I(6)

# MARK STORAGE.				(2)

VHFCNT		ERASE			# B(1)PRM NO. OF VHF MARKS (P20 (R22)).
TRKMKCNT	ERASE			# B(1)PRM NO. OF VHF MARKS (P20 (R22)).

MARKCTR		=	TRKMKCNT	# B(1) MARK COUNTER USED BY R32

# MISCELLANEOUS UNSWITCHED.		(16D)

IRETURN1	ERASE			# B(1) RET ADDR USED BY MIDTOAV1 AND 2
					# CALLED BY P40,P41,P42, P61,P62
RATEINDX	ERASE			#  (1) USED BY KALCMANU
OPTION1		ERASE			# B(1) NOUN 06 USES THIS.
OPTION2		ERASE			# B(1) NOUN 06 USES THIS.
LONGCADR	ERASE 	+1		# B(2) LONGCALL REGISTER.
LONGBASE	ERASE 	+1		# B(2) LONGCALL REGISTER.
LONGTIME	ERASE 	+1		# B(2) LONGCALL REGISTER.
DELAYLOC	ERASE 	+3
NVWORD1		ERASE			# B(1)
TEMPR60		ERASE			# B(1)
PRIOTIME	ERASE			# B(1)
P30/RET		EQUALS	IRETURN1

# MISC. INCLUDING RESTART COUNTER, GIMBAL ANGLE SAVE AND
# STANDBY VERB ERASABLES.  REDOCTR BEFORE THETAD (DWNLNK)	(16D)

TIME2SAV	ERASE	+1		# B(2)TMP
SCALSAVE	ERASE	+1		# B(2)TMP
REDOCTR		ERASE			# B(1)PRM CONTAINS NUMBER OF RESTARTS
THETAD		ERASE	+2		# B(3)PRM DESIRED GIM ANGLES FOR MANEUVER
CPHI		=	THETAD		#	(OUTER)
CTHETA		=	THETAD +1	#	(INNER)
CPSI		=	THETAD +2	#	(MIDDLE)
# Page 79
# ENTRY VARIABLES SHARED FOR TM.
RDOTREF		=	THETAD		# I(2)	P65
VREF		=	RDOTREF +2	# I(2)	P65	HI-ORDER WORD ONLY DNLNK'D
DESOPTT		ERASE			# B(1)DSP NOUN 92 FOR P20,22,52, R52.
DESOPTS		ERASE			# B(1)DSP NOUN 92 FOR P20,22,52, R52.

DELV		ERASE	+5		# I(6)
DELVX		=	DELV
DELVY		=	DELV	+2
DELVZ		=	DELV	+4

# P20, CONICS	(SHARING WITH TIME 2 SAV AND SCAL SAV ONLY)	(3D)
POINTEX		EQUALS	TIME2SAV	# I(1) POINT AXS EXIT
VHFTIME		EQUALS	POINTEX	+1	# I(2) DOWNLINK OF VHF RANGE TIME +1M

# PERM STATE VECTORS FOR BOOST AND DOWNLINK -- WHOLE MISSION --	(14D)

RN		ERASE	+5		# B(6)PRM
VN		ERASE	+5		# B(6)PRM
PIPTIME		ERASE	+1		# B(2)PRM (MUST BE FOLLOWED BY GDT/2)

# SERVICER STORAGE.				(45D)

# (SERVICER STORAGE AND P11 STORAGE IN UNSWITCHED SHOULD NOT
# OVERLAY EACH OTHER AND THE TOTAL ERASABLE REQUIRED SHOULD NOT
# EXCEED THE ERASABLE STORAGE REQUIRED BY RENDEZVOUS GUIDANCE.)

GDT/2		EQUALS	PIPTIME	+2	# B(6)TMP	** MUST FOLLOW PIPTIME **
GOBL/2		EQUALS	GDT/2	+6	# B(6)TMP
AVEGEXIT	EQUALS	GOBL/2	+6	# B(2)TMP
AVGEXIT		=	AVEGEXIT
TEMX		EQUALS	AVEGEXIT +2	# B(1)TMP
TEMY		EQUALS	TEMX +1		# B(1)TMP
TEMZ		EQUALS	TEMY +1		# B(1)TMP
PIPCTR		EQUALS	TEMZ +1		# B(1)TMP
PIPAGE		EQUALS	PIPCTR +1	# B(1)TMP
RN1		EQUALS	PIPAGE +1	# B(6)TMP
VN1		EQUALS	RN1 +6		# B(6)TMP
PIPTIME1	EQUALS	VN1 +6		# B(2)TMP
GDT1/2		EQUALS	PIPTIME1 +2	# B(6)TMP
GOBL1/2		EQUALS	GDT1/2 +6	# B(6)TMP

# Page 80
# ENTRY STORAGE				(1D)
ENTRYVN		EQUALS	GOBL1/2 +6	# B(1)TMP VN CODE FOR ENTRY DISPLAYS P60'S.

# P11 STORAGE.				(9D)
PADLONG		EQUALS	ENTRYVN		# (2)PL	LONGITUDE OF LAUNCH PAD.
LIFTTEMP	EQUALS	PADLONG +2	# (2)TMP
TEPHEM1		EQUALS	LIFTTEMP +2	# (3)TMP
PGNCSALT	EQUALS	TEPHEM1 +3	# (2)PL	ALTITUDE

# RENDEZVOUS NAVIGATION STORAGE.  (SEE COMMENT IN SERVICER STORAGE)	(58D)
CSMPOS		ERASE	+57D		# I(6)TMP
LEMPOS		EQUALS	CSMPOS	+6	# I(6)TMP
RCL		EQUALS	LEMPOS	+6	# I(2)TMP
MARKTIME	EQUALS	RCL	+2	# B(2)TMP
VTEMP		EQUALS	MARKTIME +2	# B(6)TMP
UM		EQUALS	VTEMP	+6	# I(6)TMP
MARKDATA	EQUALS	UM	+6	# B(2)TMP
USTAR		EQUALS	MARKDATA +2	# I(6)TMP
WIXA		EQUALS	USTAR	+6	# B(1)TMP
WIXB		EQUALS	WIXA	+1	# B(1)TMP
ZIXA		EQUALS	WIXB	+1	# B(1)TMP
ZIXB		EQUALS	ZIXA	+1	# B(1)TMP
DELTAX		EQUALS	ZIXB	+1	# I(18)TMP

VHFRANGE	EQUALS	DELTAX		# (2)
UCL		EQUALS	DELTAX	+12D	# (6)	LM-CSM LINE OF SIGHT 1/2 UNIT V

# **** CONICSEX (MEAS INC) ****
TRIPA		EQUALS	DELTAX
TEMPVAR		EQUALS	DELTAX	+3

TEMPOR1		ERASE	+1		# B(2)TMP

# T4RUPT ERASABLE			(6D)

DSRUPTSW	ERASE
OPTIND		ERASE
LGYRO		ERASE
COMMANDO	ERASE	+1

# Page 81
ZONE		ERASE			# B(1)PRM USED IN SHAFT STOP MONITOR
LASTYCMD	=	OPTY		# DUMMY TO MAKE RR BENCH TEST ASSEMBLE
LASTXCMD	=	OPTY		# DUMMY TO MAKE RR BENCH TEST ASSEMBLE

# UNSWITCHED DAP ERASABLE.		(4D)
T6LOC		ERASE
T6ADR		ERASE
T5LOC		ERASE	+1

# MODE SWITCHING ERASABLE		(14D)
SWSAMPLE	ERASE			# B(1)PRM
DESOPMOD	ERASE			# B(1)PRM
WTOPTION	ERASE			# B(1)PRM
ZOPTCNT		ERASE			# B(1)PRM
IMODES30	ERASE			# B(1)PRM
IMODES33	ERASE			# B(1)PRM
MODECADR	ERASE	+2		# B(3)TMP
IMUCADR		=	MODECADR
OPTCADR		=	MODECADR +1
RADCADR		=	MODECADR +2
ATTCADR		ERASE	+2		# B(3)PRM
ATTPRIO		=	ATTCADR +2
MARKSTAT	ERASE			# B(1)PRM
OPTMODES	ERASE			# B(1)PRM

# RCSDAP ERASABLE			(1D)
HOLDFLAG	ERASE			# B(1)PRM

# CRS61.1 STORAGE.  -- USED IN R63 (VERB 89) --	(5D)
CPHIX		ERASE	+2		# B(3)DSP NOUN 95 CALCULATED BY CRS61.1

TEVENT		ERASE	+1		# B(2) TIME OF EVENT FOR DOWNLIST
TLIFTOFF	=	TEVENT

# Page 82
# P34-P35 STORAGE			(1D)
NORMEX		ERASE

# SELF-CHECK ASSIGNMENTS		(17D)

SELFERAS	ERASE 	1357 - 1377	# *** MUST NOT BE MOVED *** #
SFAIL		EQUALS	SELFERAS	# B(1)
ERESTORE	EQUALS	SFAIL +1	# B(1)
SELFRET		EQUALS	ERESTORE +1	# B(1) RETURN
SMODE		EQUALS	SELFRET +1	# B(1)
ALMCADR		EQUALS	SMODE +1	# B(2) ALARM ABORD USER'S 2CADR
ERCOUNT		EQUALS	ALMCADR +2	# B(1)
SCOUNT		EQUALS	ERCOUNT +1	# B(3)
SKEEP1		EQUALS	SCOUNT +3	# B(1)
SKEEP2		EQUALS	SKEEP1 +1	# B(1)
SKEEP3		EQUALS	SKEEP2 +1	# B(1)
SKEEP4		EQUALS	SKEEP3 +1	# B(1)
SKEEP5		EQUALS	SKEEP4 +1	# B(1)
SKEEP6		EQUALS	SKEEP5 +1	# B(1)
SKEEP7		EQUALS	SKEEP6 +1	# B(1)

# USED BY P30 ROUTINES TO WRITE ONLY NEVER READ IN COLOSSUS

DISPDEX		EQUALS	A

# ERASABLE FOR SXTMARK CDU CHECK DELAY.  -- PAD LOADED --	(1D)

CDUCHKWD	ERASE			# B(1)PL

# R57 STORAGE. -- MUST BE UNSHARED EXCEPT IN BOOST OR ENTRY --	(1D)

TRUNBIAS	ERASE			# B(1)PRM RESULT OF R57 CALIBR OF TRUNION

# KEPLER STORAGE				(6D)

XMODULO		ERASE	+1		# I(2) GREATER 2PI KEPLER
TMODULO		ERASE	+1		# I(2) GREATER 2 KEPLER
EPSILONT	ERASE	+1		# I(2)TMP

# Page 83
# P37	** RETURN TO EARTH (PAD LOAD ****	(2D)

RTED1		ERASE	+1		# I(2)PL VGAMMA POLY COEF		B-3

# P40	*** STEERING ROUTINE *** PAD LOAD	(1D)

DVTHRESH	ERASE			# I(1)PL DELTA VTHRESHOLD FOR LOW THRUST
					#        ROUTINE			B-2

# P23	*** PAD LOAD ****		(2D)

HORIZALT	ERASE	+1		# I(2)PL HORIZON ALTITUDE	     M B-29

# P20	ALTERNATE LOS VARIANCE	PAD LOAD ****	(1D)
									  -16
ALTVAR		ERASE			# I(2)PL MILLARD, SQUARED SCALED 2
END-UE		EQUALS	SELFERAS +16D	# LAST USED UNSWITCHED ERASABLE

# Page 84
# EBANK-3 ASSIGNMENTS

		SETLOC	1400

# WAITLIST TASK LISTS.			(26D)

LST1		ERASE	+7		# B(8D)PRM DELTA T'S.
LST2		ERASE	+17D		# B(18D)PRM TASK 2CADR ADDRESSES.

# RESTART STORAGE.			(2D)

RSBBQ		ERASE	+1		# B(2)PRM SAVE BB AND Q FOR RESTARTS

# MORE LONGCALL STORAGE.  (MUST BE IN LST1'S BANK.	(2D)

LONGEXIT	ERASE	+1		# B(2)TMP MAY BE SELDOM OVERLAYED

# PHASE-CHANGE LISTS PART II.		(12D)


PHSNAME1	ERASE			# B(1)PRM
PHSBB1		ERASE			# B(1)PRM
PHSNAME2	ERASE			# B(1)PRM
PHSBB2		ERASE			# B(1)PRM
PHSNAME3	ERASE			# B(1)PRM
PHSBB3		ERASE			# B(1)PRM
PHSNAME4	ERASE			# B(1)PRM
PHSBB4		ERASE			# B(1)PRM
PHSNAME5	ERASE			# B(1)PRM
PHSBB5		ERASE			# B(1)PRM
PHSNAME6	ERASE			# B(1)PRM
PHSBB6		ERASE			# B(1)PRM

# IMU COMPENSATION PARAMETERS		(22D)

PBIASX		ERASE			# B(1)	PIPA BIAS, PIPA SCALE FACTOR TERMS
PIPABIAS	=	PBIASX		#	INTERMIXED.
PIPASCFX	ERASE
PIPASCF		=	PIPASCFX
PBIASY		ERASE
PIPASCFY	ERASE
PBIASZ		ERASE
PIPASCFZ	ERASE

NBDX		ERASE			# GYRO BIAS DRIFT
GBIASX		=	NBDX
NBDY		ERASE
# Page 85
NBDZ		ERASE

ADIAX		ERASE			# ACCELERATION SENSITIVE DRIFT ALONG THE
ADIAY		ERASE			# INPUT AXIS
ADIAZ		ERASE

ADSRAX		ERASE			# ACCELERATION SENSITIVE DRIFT ALONG THE
ADSRAY		ERASE			# SPIN REFERENCE AXIS
ADSRAZ		ERASE

GCOMP		ERASE	+5		# CONTAINS COMPENSATING TORQUES

GCOMPSW		ERASE
COMMAND		EQUALS	GCOMP
CDUIND		EQUALS	GCOMP	+3

# STATE VECTORS FOR ORBIT INTEGRATION.	(44D)

#		(DIFEQCNT THUR XKEP MUST BE IN THE SAME
#		EBANK AS RRECTCSM, RRECTLEM ETC
#		BECAUSE THE COPY CYCLES (ATOPCSM,
#		PTOACSM ETC) ARE EXECUTED IN BASIC.
#		ALL OTHER REFERENCES TO THIS GROUP
#		ARE BY INTERPRETIVE INSTRUCTIONS.)
#

DIFEQCNT	ERASE	+43D		# B(1)TMP
# (UPSVFLAG...XKEP MUST BE KEPT IN ORDER).

UPSVFLAG	EQUALS	DIFEQCNT +1	# B(1)PRM UPDATE FLAG
RRECT		EQUALS	UPSVFLAG +1	# B(6)TMP POS AT RECT		KM*2(-14)
VRECT		EQUALS	RRECT 	+6	# B(6)TMP VEL AT RECT		KM(-1/2)*2(6)
TET		EQUALS	VRECT 	+6	# B(2)TMP TIME OF STATE VECT	CSPCS*2(-28)
TDELTAV		EQUALS	TET 	+2	# B(6)TMP POSITION DEVIATION	KM*2(14)
TNUV		EQUALS	TDELTAV +6	# B(6)TMP VEL DEVIATION		KM(-1/2)*2(14)
RCV		EQUALS	TNUV 	+6	# B(6)TMP CONIC POSITION	KM*2(-14)
VCV		EQUALS	RCV 	+6	# B(6)TMP CONIC VELOCITY	KM(-1/2)*2(6)
TC		EQUALS	VCV 	+6	# B(2)TMP TIME SINCE RECTIFICATION
XKEP		EQUALS	TC 	+2	# B(2)TMP ROOT OF KEPLER EQ	KM(1/2)*2(-10)

# **** TEMP -- IN VAC AREA ****
RRECT1		EQUALS	18D
VRECT1		EQUALS	24D
TET1		EQUALS	30D

# PERMANENT STATE VECTORS AND TIMES.	(101D)

# (DO NOT OVERLAY WITH ANYTHING AFTER BOOST)

# Page 86
# (RRECTCSM...XKEPCSM MUST BE KEPT IN THIS ORDER)

RRECTCSM	ERASE	+5		# B(6)PRM CSM VARIABLES
RRECTHIS	=	RRECTCSM
VRECTCSM	ERASE	+5		# B(6)PRM
TETCSM		ERASE	+1		# B(2)PRM
TETTHIS		=	TETCSM
DELTACSM	ERASE	+5		# B(6)PRM
NUVCSM		ERASE	+5		# B(6)PRM
RCVCSM		ERASE	+5		# B(6)PRM
VCVCSM		ERASE	+5		# B(6)PRM
TCCSM		ERASE	+1		# B(2)PRM
XKEPCSM		ERASE	+1		# B(2)PRM

# (RRECTLEM...XKEPLEM MUST BE KEPT IN THIS ORDER)

RRECTLEM	ERASE	+5		# B(6)PRM LEM VARIABLES
RRECTOTH	=	RRECTLEM
VRECTLEM	ERASE	+5		# B(6)PRM
TETLEM		ERASE	+1		# B(2)PRM
TETOTHER	=	TETLEM
DELTALEM	ERASE	+5		# B(6)PRM
NUVLEM		ERASE	+5		# B(6)PRM
RCVLEM		ERASE	+5		# B(6)PRM
VCVLEM		ERASE	+5		# B(6)PRM
TCLEM		ERASE	+1		# B(2)PRM
XKEPLEM		ERASE	+1		# B(2)PRM

X789		ERASE	+5
TEPHEM		ERASE	+2
AZO		ERASE	+1
UNITW		ERASE	+5
-AYO		EQUALS	UNITW		# (2)
AXO		EQUALS	UNITW +2	# (2)

# STATE VECTORS FOR DOWNLINK		(12D)

R-OTHER		ERASE	+5		# B(6)PRM POS VECT (OTHER VECH) FOR DNLINK
V-OTHER		ERASE	+5		# B(6)PRM VEL VECT (OTHER VECH) FOR DNLINK

T-OTHER		=	TETLEM		# 	      TIME (OTHER VECH) FOR DNLINK

# REFSMMAT.				(18D)

REFSMMAT	ERASE	+17D		# I(18D)PRM

# Page 87
# AVERAGEG INTEGRATOR STORAGE.		(8D)
UNITR		ERASE	+5
RMAG		ERASE	+1

# P40 PAD LOADS				(6D)
EK1VAL		ERASE	+1		# I(2)PL 1-SEC SPS IMPULSE NEWTSEC/100/B23
EK2VAL		ERASE	+1		# I(2)PL  B+23 NEWTON-SEC/E+2
EK3VAL		ERASE			# I(1)PL  B+09 NEWTONS/E+4
FANG		ERASE			# I(1)PL SPS THRUST USED BY IMPULSIVE BURN

# **********LUNAR MODULE CHANGE  ***********
E3J22R2M	EQUALS	FANG	+2
E32C31RM	EQUALS	E3J22R2M +1

# **** CONICSEX (PLANETARY INERT. ORIEN.) ****
TIMSUBO		EQUALS	TEPHEM		# CSEC B-14 (TRIPLE PREC)
END-E3		EQUALS	E32C31RM	# NEXT UNUSED E3 ADDRESS

# Page 88
# EBANK-4 ASSIGNMENTS

		SETLOC	2000

# P20 STORAGE.  -- PAD LOADED --	(4D)

WRENDPOS	ERASE			# B(1)PL	M B-14
WRENDVEL	ERASE			# B(1)PL	M/CSECB0
RMAX		ERASE			# B(1)PL	METERS*2(-19)
VMAX		ERASE			# B(1)PL	M/CSEC*2(-7)

# P22 STORAGE.  -- PAD LOADED --	(5D)
WORBPOS		ERASE			# B(1)PL	M B-14
WORBVEL		ERASE			# B(1)PL	M/CSECB0
S22WSUBL	ERASE			# B(1)PL	M B-14
RPVAR		ERASE	+1		# B(2)PL

# CONISEX STORAGE.  -- PAD LOADED --	(6D)

504LM		ERASE	+5		# I(6) MOON LIBRATION VECTOR

# ENTRY STORAGE	    -- PAD LOADED --	(2D)
EMSALT		ERASE	+1		# I(2)PL

# P35 CONSTANTS.    -- PAD LOADED --	(4D)
ATIGINC		ERASE	+1		# B(2)PL
PTIGINC		ERASE	+1		# B(2)PL

# LUNAR LANDING SIGHT DATA.  -- PAD LOADED --	(6D)
# (USED BY INTEGRATION INITIALIZATION, LAT-LONG SUBROUTINES, P30'S)

RLS		ERASE	+5		# I(6) LANDING SIGHT VECTOR

# CONISEX (LUNAR AND SOLAR EPHEM) STORAGE.  -- PAD LOADED --	(77D)
TIMEMO		ERASE	+76D
VECOEM		EQUALS	TIMEMO 	+3
RESO		EQUALS	VECOEM 	+60D
# Page 89
VESO		EQUALS	RESO 	+6
OMEGAES		EQUALS	VESO 	+6

# INTEGRATION STORAGE.			(95D)

PBODY		ERASE			# I(1)
ALPHAV		EQUALS	PBODY 	+1	# I(6)TMP
BETAV		EQUALS	ALPHAV 	+6	# I(6)TMP
PHIV		EQUALS	BETAV 	+6	# I(6)TMP
PSIV		EQUALS	PHIV 	+6	# I(6)TMP
FV		EQUALS	PSIV 	+6	# I(6)TMP
BETAM		EQUALS	FV 	+6	# I(6)TMP
H		EQUALS	BETAM 	+2	# I(2)TMP
GMODE		EQUALS	H 	+2		# I(1)TMP
IRETURN		EQUALS	GMODE 	+1	# I(1)TMP
NORMGAM		EQUALS	IRETURN +1	# I(1)TMP
VECTAB		EQUALS	NORMGAM +1	# I(36)TMP
RPQV		EQUALS	VECTAB 	+36D	#  (6)TMP VECTOR PRIMARY TO SECONDARY BODY
ORIGEX		EQUALS	RPQV	+6	# B(1)TMP QSAVE FOR COORD. SWITCH ROUTINE
KEPRTN		EQUALS	ORIGEX		# 	  QSAVE FOR KEPLER
RQVV		EQUALS	ORIGEX 	+1	#  (6)    SEC. BODY TO VEH.VETOR (USED P23)
RPSV		EQUALS	RQVV 	+6		#  (6)TMP SUN TO PRIMARY BODY VECTOR
XKEPNEW		EQUALS	RPSV 	+6		#  (2)TMP ROOT OF KEPLER'S EQU FOR TIME TAU

# THESE PROBABLY CAN SHARE INTEGRATION VARIABLES	(9D)

VACX		EQUALS	VECTAB 	+6	# I(2)TMP
VACY		EQUALS	VACX 	+2	# I(2)TMP
VACZ		EQUALS	VACY 	+2	# I(2)TMP

ERADM		EQUALS	VECTAB	+18D	# I(2)TMP
INCORPEX	EQUALS	ERADM	+2	# I(1)TMP

# R31 (V83) STORAGE. -- SHARES WITH INTEGRATION STORAGE --	(24D)

BASEOTP		EQUALS	VECTAB +6	# I(6) BASE POS VECTOR OTHER VEH
BASEOTV		EQUALS	VECTAB +18D	# I(6) BASE VEL VECTOR OTHER VEH
BASETHP		EQUALS	VECTAB +30D	# I(6) BASE POS VECTOR THIS VEH
BASETHV		EQUALS	RPQV		# I(6) BASE VEL VECTOR THIS VEH

# CONIC INTEGRATION STORAGE. -- MAY NOT SHARE WITH SERVICER --	(6D)
ALPHAM		EQUALS	XKEPNEW	+2	# I(2)TMP
TAU.		EQUALS	ALPHAM 	+2	# I(2)TMP
DT/2		EQUALS	TAU.	+2	# I(2)TMP

# Page 90

# P21, R61 STORAGE.			(2D)
P21TIME		EQUALS	DT/2	+2	# B(2)TMP

# INTEGRATION STORAGE			(1D)
EGRESS		EQUALS	P21TIME	+2	# I(1)TMP SAVES RETURNS.

# VERB 83 STORAGE.			(20D)

RANGE		EQUALS	EGRESS 	+1	# I(2)DSP NOUN 54 DISTANCE TO OPTICAL SUBJ
RRATE		EQUALS	RANGE	+2	# I(2)DSP NOUN 54 RATE OF APPROACH
RTHETA		EQUALS	RRATE 	+2	# I(2)DSP NOUN 54.
RONE		EQUALS	RTHETA 	+2	# I(6)TMP VECTOR STORAGE.  (SCRATCH)
VONE		EQUALS	RONE 	+6	# I(6)TMP VECTOR STORAGE.  (SCRATCH)
BASETIME	EQUALS	VONE	+6	# I(2)    BASE TIME ASSOC WITH BASE VECS

# S-BAND ANTENNA GIMBAL ANGLES.  DISPLAYED BY R05 (V64).	(4D)
#			(OPERATES DURING P00 ONLY)
RHOSB		EQUALS	RANGE		# B(2)DSP NOUN 51. PITCH ANGLE
GAMMASB		EQUALS	RHOSB	+2	# B(2)DSP NOUN 51. YAWANGLE

# R36 SCRATCHPAD STORAGE		(13D)
RPASS36		EQUALS	RONE		# I(6) S-S
UNP36		EQUALS	RPASS36 +6	# I(6) S-S
OPTIONY		EQUALS	UNP36	+6	# I(1)TMP VEHICLE CODE

# EXTENDED VERB 82 STORAGE.		(6D)

HPERMIN		EQUALS	RANGE		# I(2) SET TO 300KFT OR 35KFT FOR SR30.1
RPADTEM		EQUALS	HPERMIN +2	# I(2) PAD OR LANDING RADIUS FOR SR30.1
TSTART82	EQUALS	RPADTEM +2	# I(2) TEMP TIME STORAGE VOR V82.

# MORE VERB 82 NOT SHARING WITH VERB 83	(9D)
V82FLAGS	EQUALS	VONE	+6	#  (1) FOR V 82 BITS
TFF		EQUALS	V82FLAGS +1	# I(2)DSP NOUN 50,44
-TPER		EQUALS	TFF	+2	# I(2)DSP NOUN 32
THETA(1)	EQUALS	-TPER	+2	# I(2)TMP SET AT END OF V82
# Page 91
RSP-RREC	EQUALS	AOPTIME		# DSP NOUN 50 FOR V82 DURING P00 AND P11

# REENTRY CONICS			(6D)
URONE		EQUALS	V82FLAGS	# I(6) SAVE ACTUAL FOR CALCULATIONS

# V82 DISPLAY				(4D)
HAPOX		EQUALS	THETA(1) +2	# I(2)DSP NOUN 44
HPERX		EQUALS	HAPOX	+2	# I(2)DSP NOUN 44

# P22 DISPLAY REGISTERS			(06D)
AOPTIME		EQUALS	HPERX	+2	# I(2)TMP FOR SR52.1.ADVTRACK
LANDLONG	EQUALS	AOPTIME	+2	# I(2)DSP NOUN 89 FOR P22
LANDALT		EQUALS	LANDLONG +2	# I(2)DSP NOUN 89 FOR P22

# S34/35.5,P34-P35 STORAGE.		(6D)
KT		EQUALS	LANDALT	+2	# B(2)
VERBNOUN	EQUALS	KT	+2	# B(1)TMP
QSAVED		EQUALS	VERBNOUN +1	# B(1)TMP HOLDS RETURN
RTRN		EQUALS	QSAVED	+1	# B(1) RETURN
SUBEXIT		EQUALS	RTRN	+1	# B(1)TMP
					# RGEXIT CAN'T SHARE WITH HPER,HAPO
RGEXIT		EQUALS	SUBEXIT		# I(1)TMP Q SAVE MODE 1 AND 2 TO RTRN MAIN

# P30 DISPLAY				(4D)
HAPO		EQUALS	KT		# I(2)DSP NOUN 42, FOR P30.
HPER		EQUALS	HAPO	+2	# I(2)DSP NOUN 42, FOR P30.

# SOME P34 STORAGE.	(OVERLAYS P35.1 STORAGE)	(2D)
NOMTPI		EQUALS	KT		# I(2)TMP NOMINAL TPI TIME FOR RECYCLE.

# THE FOLLOWING ARE ERASABLES USED BY THE SYSTEM TESTS.  205 USES TRANSM1.  G'S ARE NOT USED IN 205 NOR ARE THEY
# WHILE 504 USES TRANSM1 AND ALFDK.
# Page 92
# RSB 2009.  The definition of TRANSM1 was previously just "TRANSM1 EQUALS 2000",
# this messes up the label typing system in yaYUL.
		SETLOC	2000
TRANSM1		EQUALS			# (18) INITIALIZATION FOR IMU TESTS
ALFDK		=	TRANSM1 +18D	# (144) ERASABLE LOAD IN 504

# END OF PERF. TEST ERASABLE IN BANK 4

# *-*-* V82 *-*-*			(6D)

VONE'		EQUALS	RGEXIT	+1	# I(6)TMP NORMAL VELOCITY VONE/ SQ RT MU

# PAD LOAD INTEGRATION ERROR INCLUDED IN VARIANCE BY P20	(1D)

INTVAR		EQUALS	VONE'	+6	# I(1)PL	SQUARE OF EXPECTED INTEGRATION
					#		POSITION EXTRAPOLATION ERROR.
					#		SCALED METERS(2) 2(15)
END-E4		EQUALS	INTVAR		# LAST USED ERASABLE IN E4.

# Page 93
# EBANK-5 ASSIGNMENTS

		SETLOC	2400

# *-*-*-*- OVERLAY 1 IN EBANK 5 -*-*-*-*

# W-MATRIX STORAGE.			(162D)

# RSB 2009.  The following 3 lines have been replaced to be consistent with yaYUL's
# label-typing system.  They *were* "W EQUALS 2400", "9X9LOC1 EQUALS 2444",
# "9X9LOC2 EQUALS 2532".
W		EQUALS			# B(162)
9X9LOC1		EQUALS	W	+44
9X9LOC2		EQUALS	9X9LOC1	+66

EMATRIX		=	W	+120D	# B(42) USED TO CONVERT W TO 6X6
END-W		EQUALS	W 	+162D	# **NEXT AVAILABLE LOC AFTER W MATRIX**

# AUTO-OPTICS STORAGE -R52-

# DO NOT MOVE FROM E5,1554.  A DELICATE BALANCE EXISTS BETWEEN THIS AND P03
XNB1		EQUALS	W	+108D	# B(6D)TMP
YNB1		EQUALS	XNB1	+6	# B(6)TMP
ZNB1		EQUALS	YNB1	+6	# B(6)TMP
SAVQR52		EQUALS	ZNB1	+6	# I(2)TMP
PLANVEC		EQUALS	SAVQR52	+2	# B(6) S-S SIGHTING VECTOR IN REF. COOR.
TSIGHT		EQUALS	PLANVEC	+6	# B(2) S-S TIME OF SIGHTING

# RENDEZVOUS -P34-35			(26D)
DVLOS		EQUALS	TSIGHT	+2	# I(6) S-S DELTA VELOCITY, LOS COORD-DISPLAY
DELTAR		EQUALS	DVLOS		# I(2)
TINTSOI		EQUALS	DELTAR		# I(2) INTERCEPT TIME FOR SOI MANEUVER
DELTTIME	EQUALS	DVLOS	+2	# I(2)
TARGTIME	EQUALS	DVLOS	+4	# I(2)
UNRM		EQUALS	DVLOS	+6	# I(6) S-S
ULOS		EQUALS	UNRM	+6	# I(6) S-S UNIT LINE OF SIGHT VECTOR
ACTCENT		EQUALS	ULOS	+6	# I(2) S-S CENTRAL ANGLE BETWEEN ACTIVE
					#      VEH AT TPI IGNITION TIME AND
					#      TARGET VECTOR.
DELVTPI		EQUALS	ACTCENT	+2	# I(2) NOUN 58 FOR P34
DELVTPF		EQUALS	DELVTPI	+2	# I(2) NOUN 58,59 FOR P34,35
POSTTPI		EQUALS	DELVTPF	+2	# I(2) NOUN 58 FOR P34.
TDEC2		EQUALS	DELVTPI		#  (2)

# ALIGNMENT				(12D)
# Page 94
STARSAV1	EQUALS	DVLOS		# I(6)TMP RESTART STAR SAVE.
STARSAV2	EQUALS	STARSAV1 +6	# I(6)TMP RESTART STAR SAVE.
US		=	STARSAV2	# 	  (CISLUNAR TAG FOR STARSAV2).

# TPI SEARCH				(26D)
IT		EQUALS	DVLOS		#  (6)
THETZERO	EQUALS	IT	+6	#  (2)
TFI		EQUALS	THETZERO +2	#  (2)
DELVEE		EQUALS	TFI	+2	#  (2)
HP		EQUALS	DELVEE	+2	#  (2)
TFO		EQUALS	HP	+2	#  (2)
HPO		EQUALS	TFO	+2	#  (2)
DELVEO		EQUALS	HPO	+2	#  (2)
MAGVTPI		EQUALS	DELVEO	+2	# I(2)TMP MAG OF DELTAVTPI OR VMID
RELDELV		EQUALS	MAGVTPI	+2	# I(2)TMP MAG OF DELTAVTPF
T3TOT4		EQUALS	RELDELV	+2	# I(2)DSP NOUN 39 FOR P34,35.  TPI TO TINT
					# (CANNOT SHARE WITH RETURN TO EARTH)
# Page 95
# ALIGNMENT/SYSTEST/CALCSMSC/CRS61.1 COMMON STORAGE	(36D)
# (CALCSMSC IS A SUBSET OF S41.1 AT LEAST)
# (CRS61.1 IS A SUBSET OF P20)

XSM		EQUALS	END-W	+23D	# B(6)
YSM		EQUALS	XSM	+6	# B(6)TMP
ZSM		EQUALS	YSM	+6	# B(6)TMP

XDC		EQUALS	ZSM	+6	# B(6)TMP
YDC		EQUALS	XDC	+6	# B(6)TMP
ZDC		EQUALS	YDC	+6	# B(6)TMP

XNB		=	XDC
YNB		=	YDC
ZNB		=	ZDC

# OVERLAYS WITHIN ALIGNMENT/SYSTEST/CALCSMSC COMMON STORAGE

-COSB		EQUALS	XSM 	+2	# (2)TMP
SINB		EQUALS	-COSB 	+2	# (2)TMP

# ALIGNMENT/SYSTEST COMMON STORAGE	(18D)

STARAD		EQUALS	ZDC 	+6	# I(18D)TMP

# ALIGNMENT/SYSTEST/AUTO OPTICS COMMON STORAGE.	(17D)

OGC		EQUALS	STARAD 	+18D	# I(2)TMP
IGC		EQUALS	OGC	+2	# I(2)TMP
MGC		EQUALS	IGC 	+2	# I(2)TMP
STAR		EQUALS	MGC 	+2	# I(6)TMP
SAC		EQUALS	STAR 	+6	# I(2)TMP
PAC		EQUALS	SAC 	+2	# I(2)TMP
QMIN		EQUALS	PAC 	+2	# B(1)TMP

# **** COLP50'S ****			(1D)
CULTRIX		EQUALS	VEARTH		# VEARTH, VSUN, VMOON

# OVERLAYS WITHIN ALIGNMENT/SYSTEST COMMON STORAGE	(24D)

VEARTH		EQUALS	STARAD		# (6)TMP
VSUN		EQUALS	VEARTH 	+6	# (6)TMP
VMOON		EQUALS	VSUN 	+6	# (6)TMP
SAX		EQUALS	VMOON 	+6	# (6)TMP

# Page 96
# *-*-*-*- OVERLAY NUMBER 2 IN EBANK 5 -*-*-*-*

# CONICS ROUTINE STORAGE.			(87D)

DELX		EQUALS	END-W		# I(2)TMP
DELT		EQUALS	DELX 	+2	# I(2)TMP
URRECT		EQUALS	DELT 	+2	# I(6)TMP
RCNORM		EQUALS	URRECT 	+6	# I(2)TMP
XPREV		EQUALS	XKEP		# I(2)TMP
R1VEC		EQUALS	RCNORM 	+2	# I(6)TMP
R2VEC		EQUALS	R1VEC 	+6	# I(6)TMP
TDESIRED	EQUALS	R2VEC 	+6	# I(2)TMP
GEOMSGN		EQUALS	TDESIRED +2	# I(1)TMP
UN		EQUALS	GEOMSGN +1	# I(6)TMP
VTARGTAG	EQUALS	UN 	+6	# I(1)TMP
VTARGET		EQUALS	VTARGTAG +1	# I(6)TMP
RTNLAMB		EQUALS	VTARGET +6	# I(1)TMP
U2		EQUALS	RTNLAMB +1	# I(6)TMP
MAGVEC2		EQUALS	U2 	+6	# I(2)TMP
UR1		EQUALS	MAGVEC2 +2	# I(6)TMP
SNTH		EQUALS	UR1 	+6	# I(2)TMP
CSTH		EQUALS	SNTH 	+2	# I(2)TMP
1-CSTH		EQUALS	CSTH 	+2	# I(2)TMP
CSTH-RHO	EQUALS	1-CSTH 	+2	# I(2)TMP
P		EQUALS 	CSTH-RHO +2	# I(2)TMP
R1A		EQUALS 	P 	+2	# I(2)TMP
RVEC		EQUALS	R1VEC		# I(6)TMP
VVEC		EQUALS	R1A 	+2	# I(6)TMP
RTNTT		EQUALS	RTNLAMB		# I(1)TMP
ECC		EQUALS	VVEC 	+6	# I(2)TMP
RTNTR		EQUALS	RTNLAMB		# I(1)TMP
RTNAPSE		EQUALS	RTNLAMB		# I(1)TMP
R2		EQUALS	MAGVEC2		# I(2)TMP
RTNPRM		EQUALS	ECC 	+2	# I(1)TMP
SGNRDOT		EQUALS	RTNPRM +1	# I(1)TMP
RDESIRED	EQUALS	SGNRDOT +1	# I(2)TMP
DELDEP		EQUALS	RDESIRED +2	# I(2)TMP
DEPREV		EQUALS	DELDEP 	+2	# I(2)TMP
TERRLAMB	EQUALS	DELDEP		# I(2)TMP
TPREV		EQUALS	DEPREV		# I(2)TMP

# Page 97
# *-*-*-*- OVERLAY NUMBER 3 IN EBANK 5 -*-*-*-*

# MEASUREMENT INCORPORATION STORAGE.	(66D)
# (CALLED BY P20, P22, P23)

OMEGAM1		EQUALS	END-W		# I(6)TMP
OMEGAM2		EQUALS	OMEGAM1	+6	# I(6)TMP
OMEGAM3		EQUALS	OMEGAM2 +6	# I(6)TMP
HOLDW		EQUALS	OMEGAM3 +6	# I(18)TMP
TDPOS		EQUALS	HOLDW 	+18D	# I(6)TMP
TDVEL		EQUALS	TDPOS 	+6	# I(6)TMP

ZI		EQUALS	TDVEL	+6	# I(18)

# P22-P23 STORAGE.			(8D)

22SUBSCL	EQUALS	ZI 	+18D	# DE OF ABCDE LANDMARK ID NO.
CXOFF		EQUALS	22SUBSCL +1	# B OF ABCDE OFFSET INDICATOR
8KK		EQUALS	CXOFF 	+1	# B(1)TMP INDEX OF PRESENT MARK
8NN		EQUALS	8KK 	+1	# B(1)TMP
S22LOC		EQUALS	8NN 	+1	# I(1)TMP MARK DATA LOC
LANDMARK	EQUALS	S22LOC +1	# B(1)DSP NOUN 70 FOR P22,51, R52,53
HORIZON		EQUALS	LANDMARK +1	# B(1)DSP NOUN 70 FOR P22,51, R52,53
IDOFLMK		EQUALS	HORIZON +1	# B(1)

# ******P23***				(1D)
TRUNION		EQUALS	IDOFLMK +1	# B(1)

# Page 98

# *-*-*-*- OVERLAY NUMBER 0 IN EBANK 5 -*-*-*-*

# SYSTEM TEST STORAGE.				(174)

AZIMUTH		ERASE	+1
LATITUDE	ERASE	+1

TRUNA		EQUALS	DESOPTT
SHAFTA		EQUALS	DESOPTS

ERVECTOR	ERASE	+5
LENGTHOT	ERASE
LOSVEC		ERASE	+5

SXTOPTN		=	LOSVEC
NDXCTR		ERASE
PIPINDEX	ERASE
POSITON		ERASE
QPLAC		ERASE
QPLACE		ERASE
QPLACES		ERASE
RUN		ERASE
STOREPL		ERASE
SOUTHDR		ERASE
TARG1/2		=	SOUTHDR
TAZEL1		ERASE	+5
TEMPTIME	ERASE	+1
TMARK		ERASE	+1
GENPL		ERASE	+134D
CDUTIMEI	=	GENPL
CDUTIMEF	=	GENPL +2
IMU/OPT		=	GENPL +4
CDUREADF	=	GENPL +5
CDUREADI	=	GENPL +6
CDULIMIT	=	GENPL +7

TEMPADD		=	GENPL +4
TEMP		=	GENPL +5
NOBITS		=	GENPL +6
CHAN		=	GENPL +7

LOS1		=	GENPL +8D
LOS2		=	GENPL +14D

CALCDIR		EQUALS	GENPL +20D
CDUFLAG		EQUALS	GENPL +21D
GYTOBETQ	EQUALS	GENPL +22D
OPTNREG		EQUALS	GENPL +23D
SAVE		EQUALS	GENPL +24D	# THREE CONSEC LOC
SFCONST1	EQUALS	GENPL +27D
# Page 99
TIMER		EQUALS	GENPL 	+28D

DATAPL		EQUALS	GENPL 	+30D
RDSP		EQUALS	GENPL		# FIX LATER	POSSIBLY KEEP1
MASKREG		EQUALS	GENPL 	+64D
CDUNDX		EQUALS	GENPL 	+66D
RESULTCT	EQUALS	GENPL 	+67D
COUNTPL		EQUALS	GENPL 	+70D

CDUANG		EQUALS	GENPL 	+71D
AINLA		=	GENPL		# OPTIMUM CALIB. AND ALIGNMENT
WANGO		EQUALS	AINLA
WANGI		EQUALS	AINLA 	+2D
WANGT		EQUALS	AINLA 	+4D
TORQNDX		=	WANGT
DRIFTT		EQUALS	AINLA 	+6D
ALX1S		EQUALS	AINLA 	+8D
CMPX1		EQUALS	AINLA 	+9D
ALK		EQUALS	AINLA 	+10D
VLAUNS		EQUALS	AINLA 	+22D
THETAX		=	ALK 	+2
WPLATO		EQUALS	AINLA 	+24D
INTY		EQUALS	AINLA 	+28D
THETAN		=	THETAX 	+6
ANGZ		EQUALS	AINLA 	+30D
INTZ		EQUALS	AINLA 	+32D
ANGY		EQUALS	AINLA 	+34D
ANGX		EQUALS	AINLA 	+36D
DRIFTO		EQUALS	AINLA 	+38D
DRIFTI		EQUALS	AINLA 	+40D
VLAUN		EQUALS	AINLA 	+44D
FILDELV		=	THETAN 	+6
ACCWD		EQUALS	AINLA 	+46D
INTVEC		=	FILDELV +2
POSNV		EQUALS	AINLA 	+52D
DPIPAY		EQUALS	AINLA 	+54D
DPIPAZ		EQUALS	AINLA 	+58D
ALTIM		EQUALS	AINLA 	+60D
ALTIMS		EQUALS	AINLA 	+61D
ALDK		EQUALS	AINLA 	+62D
DELM		EQUALS	AINLA 	+76D
WPLATI		EQUALS	AINLA 	+84D
RESTARPT	=	AINLA 	+91D
GEOSAVED	=	AINLA 	+117D
PREMTRXC	=	AINLA 	+118D
LAUNCHAZ	=	AINLA 	+119D
NEWAZMTH	=	AINLA 	+121D
OLDAZMTH	=	AINLA 	+123D
# Page 100
TOLDAZMT	=	AINLA 	+125D
GEOCOMPS	=	AINLA 	+127D
1SECXT		=	AINLA 	+128D
GTSXTLST	=	AINLA 	+129D
ERECTIME	=	AINLA 	+130D
ERCOMP		=	AINLA 	+131D
ZERONDX		=	AINLA 	+137D
GTSOPNDZ	=	ZERONDX

# THE FOLLOWING TAGS ARE USED BY THE 504 IMU CALIBRATION AND ALIGNMENT PROGRAM ONLY.

THETAX1		EQUALS	ALK	+2
THETAN1		EQUALS	THETAX1	+6
FILDELV1	EQUALS	THETAN1	+6
INTVEC1		EQUALS	FILDELV1 +2
GEOSAVE1	EQUALS	AINLA	+117D
PREMTRX1	EQUALS	AINLA	+118D
LUNCHAZ1	EQUALS	AINLA	+119D
NEWAZ1		EQUALS	LUNCHAZ1 +2
OLDAZ1		EQUALS	LUNCHAZ1 +4
TOLDAZ1		EQUALS	LUNCHAZ1 +6
GEOCOMP1	EQUALS	AINLA	+127D
1SECXT1		EQUALS	AINLA	+128D
GTSWTLT1	EQUALS	AINLA	+129D
ERECTIM1	EQUALS	AINLA	+130D
ERCOMP1		EQUALS	AINLA	+131D	# I(6)
ZERONDX1	EQUALS	AINLA	+137D
PERFDLAY	EQUALS	AINLA	+138D	# B(2)..........

# END OF 504 + ALIGN ERASE.

# Page 101

# *-*-*-*-  OVERLAY 4 IN EBANK 5  -*-*-*-*
#
# P32 --- P33				(26D)
UP1		EQUALS	DVLOS		# I(6)
VPASS2		EQUALS	UP1	+6	# I(6)
RPASS2		EQUALS	VPASS2	+6	# I(6)
DIFFALT		EQUALS	RPASS2	+6	# I(2)
TCDH		EQUALS	DIFFALT	+2	# I(2)
TCSI		EQUALS	TCDH	+2	# I(2)
TTPIO		EQUALS	TCSI	+2	# I(2)

# P32,P33 STORAGE OVERLAYING 9X9 W-MATRIX LOCATIONS	(26D)
DELVEET1	EQUALS	9X9LOC1		# I(6) DELV FOR CSI
RACT2		EQUALS	DELVEET1 +6	# I(6) POS. ACTIVE VEH. AT CDH TIME
VACT2		EQUALS	9X9LOC2		# I(6) VEL. ACTIVE VEH. AT CDH TIME
RACT1		EQUALS	VACT2	+6	# I(6) POS. ACTIVE VEH. AT CSI TIME
T1TOT2		EQUALS	RACT1	+6	# I(2) TCDH - TCSI
END-E5		EQUALS	QMIN		# LAST USED E5 ADDRESS

# Page 102

# EBANK-6 ASSIGNMENTS.

		SETLOC	3000
# P23	PAD LOADS ***			(2D).
WMIDPOS		ERASE			# I(1)PL INITIAL VALUES FOR W-MATRIX IN
WMIDVEL		ERASE			# I(1)PL CISLUNAR (P23) NAVIGATION

# R22	PAD LOADS			(5D).
RVAR		ERASE	+1		# I(2)PL VHF RADAR
RVARMIN		ERASE	+2		# I(3)PL VHF RADAR

# ***** PAD LOADED ENTRY DAP STEERING VARIABLES *****	(3D)
LADPAD		ERASE			# I(1)PL FOR ENTRY. HOLDS CM NOMINAL L/D
LODPAD		ERASE			# I(1)PL FOR ENTRY. HOLDS CM NOMINAL LOD
ALFAPAD		ERASE			# B(1)PL ALFA TRIM / 180

# ***** PAD LOADED TVC DAP VARIABLES ****************	(26D)
ETDECAY		ERASE			# I(1)PL
ESTROKER	ERASE			# B(1)PL
EKPRIME		ERASE	+1		# B(2)PL
EKTLX/I		ERASE	+2		# B(3)PL
EREPFRAC	ERASE	+1		# B(2)PL
PACTOFF		ERASE			# B(1)PL, DSP N48 R01 = PTRIM, R02 = YTRIM
YACTOFF		ERASE			# B(1)PL, CONSECUTIVE WITH PACTOFF
HBN10		ERASE			# B(1)
HBN11/2		ERASE			# B(1)
HBN12		ERASE			# B(1)
HBD11/2		ERASE			# B(1)
HBD12		ERASE			# B(1)
HBN20		ERASE			# B(1)
HBN21/2		ERASE			# B(1)
HBN22		ERASE			# B(1)
HBD21/2		ERASE			# B(1)
HBD22		ERASE			# B(1)
HBN30		ERASE			# B(1)
HBN31/2		ERASE			# B(1)
HBN32		ERASE			# B(1)
# Page 103
HBD31/2		ERASE			# B(1)
HBD32		ERASE			# B(1)

# **** EXCLUSIVE TVC DAP VARIABLES. *****************	(5D)
V97VCNTR	ERASE			# B(1)
TEMPDAP		ERASE	+1		# B(2)
MRKRTMP		=	TEMPDAP		#	((B(1)))
CNTR		ERASE			# B(1)
OGAD		ERASE			# B(1)

# **** EXCLUSIVE RCS DAP VARIABLES ******************	(13D)
RWORD1		ERASE	+12D		# B(1)
RWORD2		EQUALS	RWORD1	+1	# B(1)
PWORD1		EQUALS	RWORD2	+1	# B(1)
PWORD2		EQUALS	PWORD1	+1	# B(1)
YWORD1		EQUALS	PWORD2	+1	# B(1)
YWORD2		EQUALS	YWORD1	+1	# B(1)
BLAST		EQUALS	YWORD2	+1	# B(2)
BLAST1		EQUALS	BLAST	+2	# B(2)
BLAST2		EQUALS	BLAST1	+2	# B(2)
T5PHASE		EQUALS	BLAST2	+2	# B(1)

# **** RCS/TVC DAP COMMON STORAGE. ******************	(16D)
DAPDATR1	ERASE			# B(1)DSP NOUN 46(R1)
DAPDATR2	ERASE			# B(1)DSP NOUN 46(R2)

IXX		ERASE			# B(1) CONSECUTIVE WITH IAVG, IAVG/TLX FOR
IAVG		ERASE			# B(1)				  MASSPROP
IAVG/TLX	ERASE			# B(1)

LEMMASS		ERASE			# B(1)DSP NOUN 47 (R2)
CSMMASS		ERASE			# B(1)DSP NOUN 47 (R1)
WEIGHT/G	ERASE			# B(1)
MASS		=	WEIGHT/G

AK		ERASE
AK1		ERASE
AK2		ERASE

RCSFLAGS	ERASE			# B(1) CONSECUTIVE WITH AK2 DOWNLINK
T5TEMP		ERASE			# B(1)
EDRIVEX		ERASE
EDRIVEY		ERASE
# Page 104
EDRIVEZ		ERASE

# INTEMP THRU INTEMP+14D ARE RESERVED FOR OVERLAYED TVC/RCS INTERUP TRUE TEMPORARIES
INTTEMP		ERASE	+14D		# (15)

# TVC/RCS THRU TVCRCS +11D RESERVED FOR DOWNLINKED VARIABLES
TVCRCS		ERASE	+11D		# (12)
					# RCS (WBODYS,ADOTS)
					# TVC(OMEGACS,OMEGABS)

# TVC DAP TEMPORARY VARIABLES*********************************

# TVC DAP INTERRUPT TRUE TEMPORARIES**************************

PHI333		EQUALS	INTTEMP		# B(1) TEMPORARY REGISTER
PSI333		EQUALS	PHI333 	+1	# B(1) COUNTING REGISTER
TEMP333		EQUALS	PSI333 	+1	# B(1) COUNTING REGISTER
VARST0		EQUALS	TEMP333 +1	# B(8) BREAKPOINTS AND SLOPES
VARST5		=	VARST0 	+5
LASTMASP	EQUALS	VARST0 	+9D	# LAST VARST0 WORD
TVCTMP1		EQUALS	LASTMASP +1	# B(1)

# *******REGULAR TVC TEMPORARIES*************

# TVC ZEROING STARTS HERE

OMEGAC		EQUALS	TVCRCS		# I(6)
OMEGAXC		=	OMEGAC
OMEGAYC		= 	OMEGAC 	+2
OMEGAZC		=	OMEGAC 	+4

OMEGAB		EQUALS	TVCRCS 	+6	# B(6)
OMEGAXB		=	OMEGAB
OMEGAYB		=	OMEGAB 	+2
OMEGAZB		=	OMEGAB 	+4

PTMP1		EQUALS	OMEGAC	+12D	# B(2)
PTMP2		EQUALS	PTMP1	+2	# B(2)
PTMP3		EQUALS	PTMP2	+2	# B(2)
PTMP4		EQUALS	PTMP3	+2	# B(2)
PTMP5		EQUALS	PTMP4	+2	# B(2)
# Page 105
PTMP6		EQUALS	PTMP5	+2	# B(2)

YTMP1		EQUALS	PTMP6	+2	# B(2)
YTMP2		EQUALS	YTMP1	+2	# B(2)
YTMP3		EQUALS	YTMP2	+2	# B(2)
YTMP4		EQUALS	YTMP3	+2	# B(2)
YTMP5		EQUALS	YTMP4	+2	# B(2)
YTMP6		EQUALS	YTMP5	+2	# B(2)

ROLLFIRE	EQUALS	YTMP6	+2	# B(1)
ROLLWORD	EQUALS	ROLLFIRE +1	# B(1)
TEMREG		EQUALS	ROLLWORD +1	# B(1)

STROKER		EQUALS	TEMREG	+1	# B(1)

PERRB		EQUALS	STROKER +1	# B(2)
YERRB		EQUALS	PERRB	+2	# B(2)

DELPBAR		EQUALS	YERRB	+2	# B(2)
DELYBAR		EQUALS	DELPBAR	+2	# B(2)

PDELOFF		EQUALS	DELYBAR	+2	# B(2)
YDELOFF		EQUALS	PDELOFF	+2	# B(2)

# TVC ZEROING LOOP ENDS HERE
TTMP1		EQUALS	YDELOFF	+2	# B(2)
TTMP2		EQUALS	TTMP1	+2	# B(2)
DAP1		EQUALS	TTMP2	+2	# B(2)
DAP2		EQUALS	DAP1	+2	# B(2)
DAP3		EQUALS	DAP2	+2	# B(2)

PCMD		EQUALS	DAP3	+2	# B(1)
YCMD		EQUALS	PCMD	+1	# B(1), CONSECUTIVE WITH PCMD
T5TVCDT		EQUALS	YCMD	+1	# B(1)
MDT		EQUALS	T5TVCDT	+1	# I(6)
KPRIMEDT	EQUALS	MDT	+6	# I(2)
KTLX/I		EQUALS	KPRIMEDT +2	# B(1)
TENMDOT		EQUALS	KTLX/I	+1	# B(1)
1/CONACC	EQUALS	TENMDOT	+1	# B(1)
VARK		EQUALS	1/CONACC +1	# B(1)
REPFRAC		EQUALS	VARK	+1	# B(1)
VCNTR		EQUALS	REPFRAC	+1	# B(1)
TVCPHASE	EQUALS	VCNTR	+1	# B(1)
PCDUYPST	EQUALS	TVCPHASE +1	# B(1)
PCDUZPST	EQUALS	PCDUYPST +1	# B(1)
MCDUYDOT	EQUALS	PCDUZPST +1	# B(1)
MCDUZDOT	EQUALS	MCDUYDOT +1	# B(1)
# Page 106
TVCEXPHS	EQUALS	MCDUZDOT +1	# B(1)
MASSTMP		EQUALS	TVCEXPHS +1	# B(1)	PROTECT
VCNTRTMP	EQUALS	MASSTMP	+1	# B(1)	  *PROTECT***

# STROKE TEST VARIABLES
STRKTIME	EQUALS	VCNTRTMP +1	# B(1)
CADDY		EQUALS	STRKTIME +1	# B(1)
N		EQUALS	CADDY	+1	# B(1)
BUNKER		EQUALS	N	+1	# B(1)
REVS		EQUALS	BUNKER	+1	# B(1)
CARD		EQUALS	REVS	+1	# B(1)

# TVC ROLL DAP VARIABLES
OGANOW		EQUALS	CARD	+1	# B(1)
OGAPAST		EQUALS	OGANOW	+1	# B(1)
OGA		EQUALS	OGAPAST	+1	# B(1)TMP
OGAERR		=	OGA		# (ROLL DAP USES OGA, MEANS OGAERROR)
DELOGART	EQUALS	OGA	+1	# B(1)TMP
SGNRT		EQUALS	DELOGART +1	# SIGN OF CGA RATE
DELOGA		EQUALS	SGNRT	+1	# USED IN ROLL LOGIC
I		EQUALS	DELOGA	+1	# USED IN ROLL LOGIC
IOGARATE	EQUALS	I	+1	# USED IN ROLL LOGIC

# TVC DAP RESTART TEMPORARIES.
PACTTMP		EQUALS	IOGARATE +1	# B(2)
YACTTMP		EQUALS	PACTTMP	+2	# B(2)
CNTRTMP		EQUALS	YACTTMP	+2	# B(1)
STRKTTMP	EQUALS	CNTRTMP	+1	# B(1)
DELBRTMP	EQUALS	STRKTTMP +1	# B(2)
ERRBTMP		EQUALS	DELBRTMP +2	# B(2)
CMDTMP		EQUALS	ERRBTMP	+2	# B(2)

TMP1		EQUALS	CMDTMP	+2	# B(2)
TMP2		EQUALS	TMP1	+2	# B(2)
TMP3		EQUALS	TMP2	+2	# B(2)
TMP4		EQUALS	TMP3	+2	# B(2)
TMP5		EQUALS	TMP4	+2	# B(2)
TMP6		EQUALS	TMP5	+2	# B(2)

# TVC DAP FILTER COEFFICIENTS TEMPORARIES
COEFFADR	EQUALS	TMP6	+2	# B(1)
N10		EQUALS	COEFFADR +1	# I(15)
# Page 107

# OVERLAYS WITHIN TVC DAP

OGARATE		=	OMEGAB		# B(2)

PHASETMP	=	TTMP1		# B(1) RESTART FOR CSM/LM V46 SWITCH-OVER
RTRNLOC		=	TTMP2		# B(1) RESTART FOR CSM/LM V46 SWITCH-OVER
BZERO		=	ERRBTMP
CZERO		=	ERRBTMP
JZERO		=	CMDTMP
YZERO		=	CMDTMP

# 540.9 STORAGE .............

NBRCYCLS	EQUALS	N10	+15D	# B(1) COUNTER FOR P40,41 STEERING
NBRCYCLP	EQUALS	NBRCYCLS +1	# B(1) MAINTAIN ORDER
DELVSUM		EQUALS	NBRCYCLP +1	# I(6) P40,P41
DELVSUMP	EQUALS	DELVSUM	+6	# I(6) P40,P41

# Page 108
# **** RCS DAP TEMPORARY VARIABLES. ********************	(95D)

# ** RCS INTERRUPT TRUE TEMPS ***************	(15D)
SPNDX		EQUALS	INTTEMP		# B(1)
DPNDX		EQUALS	SPNDX	+1	# B(1)TMP
KMPAC		EQUALS	DPNDX	+1	# B(2)TMP
KMPTEMP		EQUALS	KMPAC	+2	# B(1)TMP

XNDX1		EQUALS	KMPTEMP	+1	# B(1)TMP XNDX1 THRU NYJETS ARE OVERLAYED
XNDX2		EQUALS	XNDX1	+1	# B(1)TMP BY OTHER DAP ERASABLES SO
YNDX		EQUALS	XNDX2	+1	# B(1)TMP SHOULD ALWAYS BE DEFINED IN
ZNDX		EQUALS	YNDX	+1	# B(1)TMP A BLOCK
RINDEX		EQUALS	ZNDX	+1	# B(1)TMP
PINDEX		EQUALS	RINDEX	+1	# B(1)TMP
YINDEX		EQUALS	PINDEX	+1	# B(1)TMP
NRJETS		EQUALS	YINDEX	+1	# B(1)TMP
NPJETS		EQUALS	NRJETS	+1	# B(1)TMP
NYJETS		EQUALS	NPJETS	+1	# B(1)TMP

WTEMP		EQUALS	XNDX1		# B(2)TMP WTEMP THRU DELTEMPZ OVERLAY
DELTEMPX	EQUALS	WTEMP	+2	# B(2)TMP XNDX1 THRU NRJETS AND EDOT THRU
DELTEMPY	EQUALS	DELTEMPX +2	# B(2)TMP ADBVEL
DELTEMPZ	EQUALS	DELTEMPY +2	# B(2)TMP

EDOT		EQUALS	YNDX		# B(2)TMP EDOT THRU ADBVEL OVERLAY
AERR		EQUALS	EDOT	+2	# B(1)TMP YNDX THRU NPJETS AND DELTEMPX
EDOTVEL		EQUALS	AERR	+1	# B(2)TMP THRU DELTEMPZ
AERRVEL		EQUALS	EDOTVEL	+2	# B(1)TMP
ADBVEL		EQUALS	AERRVEL	+1	# B(1)TMP

# *** REGULAR RCS TEMPS *********************	(   ).

# *** RCS ZEROING LOOP STARTS HERE **********	(37)
WBODY		EQUALS	TVCRCS		# B(2)TMP
WBODY1		EQUALS	WBODY	+2	# B(2)TMP
WBODY2		EQUALS	WBODY	+4	# B(2)TMP
ADOT		EQUALS	WBODY2	+2	# B(2)TMP
ADOT1		EQUALS	ADOT	+2	# B(2)TMP
ADOT2		EQUALS	ADOT1	+2	# B(2)TMP

MERRORX		EQUALS	ADOT2	+2	#  (2)
MERRORY		EQUALS	MERRORX	+2	#  (2)
MERRORZ		EQUALS	MERRORY	+2	#  (2)
DFT		EQUALS	MERRORZ	+2	# B(1)TMP
DFT1		EQUALS	DFT	+1	# B(1)TMP
DFT2		EQUALS	DFT1	+1	# B(1)TMP
DRHO		EQUALS	DFT2	+1	# B(2)TMP
DRHO1		EQUALS	DRHO	+2	# B(2)TMP
# Page 109
DRHO2		EQUALS	DRHO1	+2	# B(2)TMP
ATTSEC		EQUALS	DRHO2	+2	# B(1)TMP
TAU		EQUALS	ATTSEC	+1	# B(1)TMP
TAU1		EQUALS	TAU	+1	# B(1)TMP
TAU2		EQUALS	TAU1	+1	# B(1)TMP
BIAS		EQUALS	TAU2	+1	# B(1)TMP
BIAS1		EQUALS	BIAS	+1	# B(1)TMP
BIAS2		EQUALS	BIAS1	+1	# B(1)TMP
ERRORX		EQUALS	BIAS2	+1	# B(1)TMP
ERRORY		EQUALS	ERRORX	+1	# B(1)TMP
ERRORZ		EQUALS	ERRORY	+1	# B(1)TMP

# RCS ZERO LOOP ENDS HERE
#	MORE RCS			(69D)
THETADX		EQUALS	ERRORZ	+1	# B(1)TMP MUST BE CONSECUTIVE WITH ERRORZ
THETADY		EQUALS	THETADX	+1	# B(1)TMP
THETADZ		EQUALS	THETADY	+1	# B(1)TMP

DELCDUX		EQUALS	THETADZ	+1	# B(2)TMP
DELCDUY		EQUALS	DELCDUX	+2	# B(2)TMP
DELCDUZ		EQUALS	DELCDUY	+2	# B(2)TMP

DCDU		EQUALS	DELCDUZ	+2	# B(6)TMP USED DURING P20
DTHETASM	EQUALS	DCDU	+6	# B(6)TMP STEER LOW OUTPUT.

ATTKALMN	EQUALS	DTHETASM +6	# B(1)TMP
KMJ		EQUALS	ATTKALMN +1	# B(1)TMP
KMJ1		EQUALS	KMJ	+1	# B(1)TMP
KMJ2		EQUALS	KMJ1	+1	# B(1)TMP
J/M		EQUALS	KMJ2	+1	# B(1)TMP
J/M1		EQUALS	J/M	+1	# B(1)TMP
J/M2		EQUALS	J/M1	+1	# B(1)TMP
RACFAIL		EQUALS	J/M2	+1	# B(1)TMP
RBDFAIL		EQUALS	RACFAIL	+1	# B(1)TMP
ACORBD		EQUALS	RBDFAIL	+1	# B(1)TMP
XTRANS		EQUALS	ACORBD	+1	# B(1)TMP
CH31TEMP	EQUALS	XTRANS	+1	# B(1)TMP
CHANTEMP	EQUALS	CH31TEMP +1	# B(1)TMP
T5TIME		EQUALS	CHANTEMP +1	# B(1)TMP
RHO		EQUALS	T5TIME	+1	# B(1)TMP
RHO1		EQUALS	RHO	+1	# B(1)TMP
RHO2		EQUALS	RHO1	+1	# B(1)TMP
AMGB1		EQUALS	RHO2	+1	# B(1)TMP
AMGB4		EQUALS	AMGB1	+1	# B(1)TMP
# Page 110
AMGB5		EQUALS	AMGB4	+1	# B(1)TMP
AMGB7		EQUALS	AMGB5	+1	# B(1)TMP
AMGB8		EQUALS	AMGB7	+1	# B(1)TMP
CAPSI		EQUALS	AMGB8	+1	# B(1)TMP
CDUXD		EQUALS	CAPSI	+1	# B(2)TMP
CDUYD		EQUALS	CDUXD	+2	# B(2)TMP
CDUZD		EQUALS	CDUYD	+2	# B(2)TMP
SLOPE		EQUALS	CDUZD	+2	# B(1)TMP
ADB		EQUALS	SLOPE	+1	# B(1)TMP
RMANNDX		EQUALS	ADB	+1	# B(1)TMP
PMANNDX		EQUALS	RMANNDX	+1	# B(1)TMP
YMANNDX		EQUALS	PMANNDX	+1	# B(1)TMP MUST BE LAST VARIABLE IN RCS

# Page 111
# ********** ENTRY DAP TEMPORARY VARIABLES. *********************	(69D)

# ANGLE REGISTERS FOR ENTRY DAPS
AOG		EQUALS	BCDU		# 1P
AIG		EQUALS	AOG	+1	# 1P
AMG		EQUALS	AIG	+1	# 1P
ROLL/180	EQUALS	AMG	+1	# 1P
ALFA/180	EQUALS	ROLL/180 +1	# 1P
BETA/180	EQUALS	ALFA/180 +1	# 1P
AOG/PIP		EQUALS	BETA/180 +1	# 1P
AIG/PIP		EQUALS	AOG/PIP	+1	# 1P
AMG/PIP		EQUALS	AIG/PIP	+1	# 1P
ROLL/PIP	EQUALS	AMG/PIP	+1	# 1P
ALFA/PIP	EQUALS	ROLL/PIP +1	# 1P
BETA/PIP	EQUALS	ALFA/PIP +1	# 1P

# GYMBAL DIFFERENCES OVER INTERNAL TCDU = .1 SEC.
-DELAOG		EQUALS	BETA/PIP +1	# 1P
-DELAIG		EQUALS	-DELAOG	+1	# 1P
-DELAMG		EQUALS	-DELAIG	+1	# 1P

# ESTIMATED BODY RATES
CMDAPMOD	EQUALS	-DELAMG	+1	# 1P GOES BEFORE PREL FOR TM.

PREL		EQUALS	CMDAPMOD +1	# 1P P TCDU/180		(ROLLDOT)
QREL		EQUALS	PREL	+1	# 1P Q TCDU/180		(PITCHDOT)
RREL		EQUALS	QREL	+1	# 1P R TCDU/180		(YAWDOT)

BETADOT		EQUALS	RREL	+1	# 1P MUST FOLLOW RREL. BETADOT TCDU/180
PHIDOT		EQUALS	BETADOT	+1	# 1P

# OLD (UNAVERAGED) BODY RATE MEASURE
OLDELP		EQUALS	PHIDOT	+1	# 1P
OLDELQ		EQUALS	OLDELP	+1	# 1P
OLDELR		EQUALS	OLDELQ	+1	# 1P

JETAG		EQUALS	OLDELR	+1	# 1P
TUSED		EQUALS	JETAG	+1	# 1P ELAPSED TIME SINCE NOMINAL UPDATE.

# FOLLOWING 3 SP WORDS IN DOWNLINK.  ROLLTM SENT EACH 1 SEC.
PAXERR1		EQUALS	TUSED	+1	# 1P INTEGRATED ROLL ERROR/360.
ROLLTM		EQUALS	PAXERR1	+1	# 1P ROLL/180 FOR TM.
ROLLC		EQUALS	ROLLTM	+1	# 2P ROLLCOM/360 FROM ENTRY (FOR TM)
					# KEEP ROLLC & ROLLHOLD ADJACENT FOR TP
# Page 112
ROLLHOLD	EQUALS	ROLLC	+2	# 1P FOR ATTITUDE HOLD IN CMDAPMOD = +1

# ENTRY DAP QUANTITIES THAT SHARE WITH RCS DAP.
ALFACOM		EQUALS	DCDU		# 1P KEEP ADJACENT TO BETACOM. <<
BETACOM		EQUALS	ALFACOM +1	# 1P

# JET LIST.  DT, JETBITS IN THIS ORDER.
TOFF		EQUALS	BETACOM +1	# 1P DP PAIR
TBITS		EQUALS	TOFF	+1	# 1P
TON2		EQUALS	TBITS	+1	# 1P DP PAIR
T2BITS		EQUALS	TON2	+1	# 1P

# MISCELLANEOUS PERMANENT ERASABLE.
OUTTAG		EQUALS	T2BITS	+1	# 1P
NUJET		EQUALS	OUTTAG	+1	# 1P

# MORE ENTRY DAP QUANTITIES THAT DO NOT SHARE WITH RCS DAP.
JETEM		EQUALS	ROLLHOLD +1	# 2P THIS DP USED IN RATEAVG
GAMA		EQUALS	JETEM	+2	# 1P
GAMDOT		EQUALS	GAMA	+1	# 1P
POSEXIT		EQUALS	GAMDOT	+1	# 1P
CM/GYMDT	EQUALS	POSEXIT	+1	# 1P
HEADSUP		EQUALS	CM/GYMDT +1	# 1P DSP NOUN 61 FOR P62,63,64,67.
P63FLAG		EQUALS	HEADSUP	+1	# 1P INTERLOCK FOR WAKEP62

					#>> SHARE BELOW WITH RCS RUPT TEMPS (< 15D) <<<
CALFA		EQUALS	SPNDX		# 1P
SALFA		EQUALS	CALFA	+1	# 1P

SINM		EQUALS	SALFA	+1	# 1P
COSM		EQUALS	SINM	+1	# 1P
SINO		EQUALS	COSM	+1	# 1P
COSO		EQUALS	SINO	+1	# 1P
SINOCOSM	EQUALS	COSO	+1	# 1P
COSOCOSM	EQUALS	SINOCOSM +1	# 1P
					#>> SHARE ABOVE WITH RCS RUPT TEMPS <<<

# THE FOLLOWING FEW REGISTERS USED ONCE EACH 2 SEC
-VT/180		EQUALS	NUJET	+1	# 1P
LCX/360		EQUALS	-VT/180	+1	# 1P
XD/360		EQUALS	LCX/360	+1	# 1P
VSQ/4API	EQUALS	XD/360	+1	# 1P
JNDX		EQUALS	VSQ/4API +1	# 1P
JNDX1		EQUALS	JNDX	+1	# 1P
# Page 113
TON1		EQUALS	JNDX1	+1	# 1P DP PAIR
T1BITS		EQUALS	TON1	+1	# 1P

# MISCELLANEOUS REGISTERS USED EACH UPDATE.
CM/SAVE		EQUALS	T1BITS	+1	# 1P
JETEM2		EQUALS	CM/SAVE	+1	# 1P TEMPORARY STORAGE

# DAP QUANTITIES SHARED WITH RCS DAP FOR TM & FLIGHT RECORDER.
VDT/180		=	ERRORX		# 1P (EDIT)
-VT/180E	=	ERRORY		# 1P (EDIT)

PAXERR		EQUALS	AK		# 1P ROLL ERROR FOR NEEDLES
QAXERR		=	THETADX		# 1P SINCE AK1 IS ZEROED IN ATM DAP.
RAXERR		=	QAXERR	+1	# 1P SINCE AK2 IS ZEROED IN TM DAP.

# *** COLMANU (R60,R62) ****
VECQTEMP	EQUALS	COFSKEW

# Page 114
# ******** KALCMANU VARIABLES. (71D) *******************************
BCDU		EQUALS	YMANNDX	+1	# B(3)TMP
KSPNDX		EQUALS	BCDU	+3	# B(1)TMP
KDPNDX		EQUALS	KSPNDX	+1	# B(1)TMP

TMIS		EQUALS	KDPNDX	+1	# I(18) MUST BE IN THE SAME BANK AS RCS DAP
COFSKEW		EQUALS	TMIS	+18D	# I(6) MUST BE IN THE SAME BANK AS RCS DAP
CAM		EQUALS	COFSKEW	+6	# I(2) MUST BE IN THE SAME BANK AS RCS DAP

MIS		EQUALS	CAM	+2	# I(18)	(THE REST MAY GO ANYWHERE)
COF		EQUALS	MIS	+18D	# I(6)TMP
SCAXIS		EQUALS	COF	+6	# I(6)TMP
POINTVSM	EQUALS	SCAXIS	+6	# I(6)TMP
AM		EQUALS	POINTVSM +6	# I(2)TMP
RAD		EQUALS	AM	+2	# I(2)TMP

# FIRST-ORDER OVERLAYS IN KALCMANU
KV1		EQUALS	TMIS		# I(6)TMP
MFISYM		EQUALS	TMIS		# I   TMP
TMFI		EQUALS	TMIS		# I   TMP
NCDU		EQUALS	TMIS		# B   TMP
NEXTIME		EQUALS	TMIS	+3	# B   TMP
TTEMP		EQUALS	TMIS	+4	# B   TMP
KV2		EQUALS	TMIS	+6	# I(6)TMP
BIASTEMP	EQUALS	TMIS	+6	# B   TMP
KV3		EQUALS	TMIS	+12D	# I(6)TMP
CGF		EQUALS	TMIS	+12D	# I   TMP

BRATE		EQUALS	COFSKEW		# B   TMP
TM		EQUALS	CAM		# B   TMP

# SECOND-ORDER OVERLAYS IN KALCMANU
P21		EQUALS	KV1		# I(2)TMP
D21		EQUALS	KV1	+2	# I(2)TMP
G21		EQUALS	KV1	+4	# I(2)TMP

# SATURN BOOST STORAGE.  SAVE TILL RCS DAP OPERATION.	(17D)
POLYNUM		EQUALS	BCDU		# B(15) PAD LOADED
POLYLOC		=	POLYNUM	+10D
SATRLRT		EQUALS	POLYNUM	+15D	# B(2)  PAD LOADED

# MORE P11 STORAGE --PAD LOADED--	(2D)
# Page 115
# (NOTE:  THIS PAD LOAD WILL NOT BE PRESERVED THROUGHOUT THE MISSION AS IT SHARES STORAGE WITH KALCMANU,
# ENTRY DAP AND TVC DAP)
RPSTART 	EQUALS	SATRLRT	+2	# B(1) PITCH ROLL START TIME
POLYSTOP	EQUALS	RPSTART	+1	# B(1) POLYCUT OFF MINUS RPSTART SEC

# STORAGE FOR VHHDOT AND ATTDSP
BODY3		EQUALS	POLYSTOP +1	# B(1)OUT
BODY2		EQUALS	BODY3	+1	# B(1)OUT
BODY1		EQUALS	BODY2	+1	# B(1)OUT
SPOLYARG	EQUALS	BODY1	+1	# B(1)TMP ARGUMENT FOR POLLY

OLDBODY1	=	EDRIVEX		# 1 PULSE = 0.0432 DEGREES
OLDBODY2	=	EDRIVEY
OLDBODY3	=	EDRIVEZ

# STORAGE FOR S11.1
VDISP		EQUALS	SPOLYARG +1	# I(2)OUT	2(7) M/CS
HDISP		EQUALS	VDISP	+2	# I(2)OUT	2(29) M
HDOTDISP	EQUALS	HDISP	+2	# I(2)OUT	2(7) M/CS
BOOSTEMP	EQUALS	HDOTDISP +2	# B(3)TEMP

# P11 SATURN I/F			(9D)
SATRATE		EQUALS	BOOSTEMP +3	# B(4)PL MANEUVER RATES FOR SATURN STICK
SATSW		EQUALS	SATRATE	+4	# B(1)TEM STATUS SW FOR BOOST TAKEOVER
BIASAK		EQUALS	SATSW	+1	# B(3)TEM STOR AKBIAS FOR BOOST TAKEOVER
SATSCALE	EQUALS	BIASAK	+3	# B(1) SCALE FACTOR FOR SATURN STEERING

# P21 STORAGE.				(1D)
GENRET		EQUALS	RAD	+2	# B(1)TMP

# R61CSM STORAGE.			(1D)
SAVBNK		EQUALS	GENRET	+1	# B(1) S-S SAVE EBANK FOR R61 SUBROUTINE

# CRS61.1 STORAGE FOR AUTOPILOT BANK.	(3D)
SAVEDCDU	EQUALS	SAVBNK	+1	# B(3)TMP

# R61 STORAGE.				(1D)
# Page 116
R61CNTR		EQUALS	SAVEDCDU +3	#  (1)TMP

# ENTRY RESTART PROTECTION STORAGE.  --KEEP TEMPS IN ORDER--	(12D)
TEMPROLL	EQUALS	GENRET		# B(1)TMP COPY CYCLE REGISTER
TEMPALFA	EQUALS	TEMPROLL +1	# B(1)TMP COPY CYCLE REGISTER
TEMPBETA	EQUALS	TEMPALFA +1	# B(1)TMP COPY CYCLE REGISTER
60GENRET	EQUALS	TEMPBETA +1	# B(1)TMP QSAVE FOR S61.1 AND ENTRY.
S61DT		EQUALS	60GENRET +1	# B(1)TMP VARIABLE DT FOR S61.1 RESTART.

# ENTRY TM SHARING FOR ACCELERATION PROFILE.
XPIPBUF		EQUALS	ADOT		# B(1) PIPA BUFFER FOR TM DURING ENTRY.
YPIPBUF		EQUALS	XPIPBUF	+1	# B(1) PIPS FILED HERE EACH .5 SEC APPEAR
ZPIPBUF		EQUALS	YPIPBUF	+1	# B(1) ON DOWNLIST ONCE PER SECOND DURING
XOLDBUF		EQUALS	ZPIPBUF +1	# B(1) ENTRY AFTER RCS DAP HAS BEEN DIS-
YOLDBUF		EQUALS	XOLDBUF +1	# B(1) ABLED.  NEWEST PIP VALUE REPLACES
ZOLDBUF		EQUALS	YOLDBUF	+1	# B(1) PIPBUF, WHICH IS MOVED INTO OLDBUF.

# REENTRY VARIABLES SHARED WITH RCS DAP FOR TM & FLIGHT RECORDER.
Q7		=	THETADZ		# I(2) HI-WORD ONLY ON DNLIST.
ASPS(TM)	=	WBODY		# I(6)DWN
					#	ASKEP, ASP1, ASPUP, ASPDN, ASP3, ASP3+1

# P37 PAD LOADS				(1)
P37RANGE	EQUALS	R61CNTR	+1	# I(1)PL	*****

END-E6		=	P37RANGE +1	# FIRST UNUSED ERASABLE LOCATION IN E6

# Page 117
# EBANK-7 ASSIGNMENTS

		SETLOC	3400

# *-*-*-*- OVERLAY NUMBER 0 IN EBANK 7 -*-*-*-*

# EXTERNAL DELTA-V UPDATE.		(21D)
# (MUST BE IN ORDER FOR UPDATE PROGRAM.  ALSO ENTRY PROGRAM PICK UP 'LAT(SPL' WITH A VLOAD.)

LAT(SPL)	ERASE	+20D		# I(2)DSP NOUN 61 FOR P62,63,64,67
LNG(SPL)	EQUALS	LAT(SPL) +2	# I(2)DSP NOUN 61 FOR P62,63,64,67

DELVSLV		EQUALS	LNG(SPL) +2	# I(6)TMP DELTA VEL VECT, LOC VER COORDS
TIG		EQUALS	DELVSLV	+6	# B(2)DSP NOUN 33 FOR X-V84(R32),P30,40.
RTARG		EQUALS	TIG	+2	# I(6)IN DESIRED VEHICLE RADIUS VECTOR
DELLT4		EQUALS	RTARG	+6	# I(2)IN TIME DIFFERENCE FOR INITVEL
ECSTEER		EQUALS	DELLT4	+2	# I(1)PL FOR P40'S
DELVLVC		=	DELVSLV
END-DELV	ERASE			# *NEXT AVAIL LOC AFTER UNSHARED E7*

# SERVICER STORAGE.			(13D)
DVTOTAL		EQUALS	END-DELV	# B(2)DSP NOUN 40,99 FOR P30,34,35,40
TGO		EQUALS	DVTOTAL	+2	# B(2)
DVCNTR		EQUALS	TGO	+2	# B(1)TMP
DELVREF		EQUALS	DVCNTR	+1	# I(6)TMP

NOMTIG		EQUALS	END-KALC	# I(2) (CANNOT SHARE WITH KALCMANU
					#       OR DELVREF)
END-SVCR	EQUALS	NOMTIG	+2	# ***NEXT AVAILABLE AFTER SERVICER

# ALIGNMENT STORAGE.			(25D)
XSCD		EQUALS	END-SVCR	# I(6)TMP
YSCD		EQUALS	XSCD	+6	# I(6)TMP
ZSCD		EQUALS	YSCD	+6	# I(6)TMP
VEL/C		EQUALS	ZSCD	+6	# I(6)TMP
R53EXIT		EQUALS	VEL/C	+6	# I(1)TMP

# ALIGNMENT MARKDATA (DOWNLINK) *******	(7D)
MARK2DWN	EQUALS	R53EXIT	+1	#  (7) USED BY ALIGNMENT P50'S

# Page 118
# *-*-*-*- OVERLAY NUMBER 1 IN EBANK 7 -*-*-*-*

# REENTRY ERASABLES.			(206D)
RTINIT		EQUALS	END-SVCR	# 6P
RTEAST		EQUALS	RTINIT	+6	# 6P
RTNORM		EQUALS	RTEAST	+6	# 6P
RT		EQUALS	RTNORM	+6	# 6P
UNI		EQUALS	RT	+6	# 6P
UNITV		EQUALS	UNI	+6	# 6P
VEL		EQUALS	UNITV	+6	# 6P

TIME/RTO	EQUALS	VEL	+6	# 2P TIME OF INITIAL TARGET, RTO.
-VREL		EQUALS	TIME/RTO +2	# 6P
OLDUYA		EQUALS	-VREL 	+6	# 6P USED BY CM/POSE	(ENTRY DAP)
UXA/2		EQUALS	OLDUYA	+6	# 6P USED BY CM/POSE	(ENTRY DAP) -UVA
URH		=	UXA/2		#	P67 DISPLAY NOUN
UYA/2		EQUALS	UXA/2	+6	# 6P USED BY CM/POSE	(ENTRY DAP) UYA
UZA/2		EQUALS 	UYA/2	+6	# 6P USED BY CM/POSE	(ENTRY DAP) UNA
UBX/2		EQUALS	UZA/2	+6	# 6P USED BY CM/POSE	(ENTRY DAP)
UBY/2		EQUALS	UBX/2	+6	# 6P USED BY CM/POSE	(ENTRY DAP)
UBZ/2		EQUALS	UBY/2	+6	# 6P USED BY CM/POSE	(ENTRY DAP)

DTEAROT		EQUALS	UBZ/2	+6	# 2P
DIFF		EQUALS	DTEAROT	+2	# 2P
DIFFOLD		EQUALS	DIFF	+2	# 2P
FACTOR		EQUALS	DIFFOLD	+2	# 2P
FACT1		EQUALS	FACTOR	+2	# 2P
FACT2		EQUALS	FACT1	+2	# 2P
#Q7		=	THETAD2		# 2P SHARED FOR TM.  P64-P66
VSQUARE		EQUALS	FACT2	+2	# 2P
LAD		EQUALS	VSQUARE	+2	# 2P
LOD		EQUALS	LAD	+2	# 2P
L/DCMINR	EQUALS	LOD	+2	# 2P
KLAT		EQUALS	L/DCMINR +2	# 2P
L/D		EQUALS	KLAT	+2	# 2P
L/D1		EQUALS	L/D	+2	# 2P
LEWD		=	VIO		# 2P SHARED FOR TM.  P64-P65
D		EQUALS	L/D1	+2	# 2P DSP NOUN 64,66,68 FOR P63,64,67
#V1		=	ENDBUF	+1	# 2P SHARED FOR TM. P64-P65
DLEWD		EQUALS	D	+2	# 2P
K2ROLL		EQUALS	DLEWD	+2	# 2P
GOTOADDR	EQUALS	K2ROLL	+2	# 1P
TEM1B		EQUALS	GOTOADDR +1	# 2P
MM		EQUALS	TEM1B	+2	# 2P
GRAD		EQUALS	MM	+1	# 2P
FX		EQUALS	GRAD	+1	# 1P OVERWRITES NEXT 5 LOCS IN P67
LEQ		EQUALS	FX	+1	# 2P
DHOOK		EQUALS	LEQ	+2	# 2P
AHOOKDV		EQUALS	DHOOK	+2	# 2P
# Page 119
DVL		EQUALS	AHOOKDV	+2	# 2P
#A0		=	ENDBUF	+3	# 2P SHARED FOR TM. (HI-WD) P84-P85
A1		EQUALS	DVL	+2	# 2P
VBARS		EQUALS	A1	+2	# 2P
COSG/2		EQUALS	VBARS	+2	# 2P
#GAMMAL		=	GAMMAEI		# 2P SHARED FOR TM.  P64
GAMMAL1		=	22D		# 2P
VS1		EQUALS	COSG/2	+2	# 2P
VL		=	VPRED		# 2P SHARED FOR TM.  P64-P65
V		EQUALS	VS1	+2	# 2P
#VREF		=	THETAD	+2	# 2P SHARED FOR TM.  P65
LATANG		EQUALS	V	+2	# 2P ADJACENT FOR TM.
RDOT		EQUALS	LATANG	+2	# 2P ADJACENT FOR TM.
THETAH		EQUALS	RDOT	+2	# 2P DSP NOUN 64,67 FOR P63,64,67
#RDOTREF	=	THETAD		# 2P SHARED FOR TM.  P65
ALP		EQUALS	THETAH	+2	# 2P

ASKEP		=	ASPS		# 2P)		     THESE ARE STORED IN
ASP1		=	ASPS	+1	# 2P)		     SEQUENCE, OVERLAPPING
ASPUP		=	ASPS	+2	# 2P)>HI-WD OF EACH< HI-WORD ONLY APPEARING
ASPDWN		=	ASPS	+3	# 2P)		     ON DOWNLIST, EXCEPT
ASP3		=	ASPS	+4	# 2P)		     ASP3 IS COMPLETE.

C/D0		EQUALS	ALP	+2	# 2P	-1/D0
D0		EQUALS	C/D0	+2	# I(2)	CONSTANT DRAG
Q2		EQUALS	D0	+2	# 2P

# ROLLC IS LOCATED IN EBANK= AOG TO AID ENTRY DAP.
RTGO		EQUALS	Q2	+2	# 2P DSP NOUN 66 FOR P64,P67
DNRNGERR	EQUALS	RTGO	+2	# 2P DSP NOUN 66 FOR P64,67
XRNGERR		=	LATANG		#	FOR DISKY DISPLAY
KAT		EQUALS	DNRNGERR +2	# 2P
GMAX		EQUALS	KAT	+2	# 1P DSP NOUN 60 FOR P61,62,63
					# GMAX IS LOADED IN DOUBLE PRECISION.
L/DCALC		=	TTE		# 2P CALCULATED L/D FOR TM: P64-P67.
GAMMAL		=	GAMMAEI		# 2P SHARED FOR TM.  P64.
PREDANG		=	GAMMAEI		#	FOR TM IN P67.
JJ		=	PREDANG	+1	#	FOR TM IN P67.
VMAGI		EQUALS	GMAX	+1	# 2P DSP NOUN 62,64,66 FOR P11,63,64.
VIO		EQUALS	VMAGI	+2	# 2P DSP NOUN 63 FOR P61.
TTE		EQUALS	VIO	+2	# 2P DSP NOUN 63 FOR P61.
ASPS		EQUALS	TTE	+2	# I(2) HI-WORD ONLY ON DNLIST FOR TEMP
TTE1		EQUALS	ASPS	+2	# I(2)TMP HOLDS UNDECREMENTED TTE VALUE

# **** P6O'S ****
RTGON64		EQUALS	RTGO		# RANGE ERRORS NEGATIVE IF FALLS SHORT
# Page 120
RTGON67		EQUALS	RTGO		# DSP NOUN 67

# REENTRY, RETURN TO EARTH COMMON DISPLAY	(4D)
VPRED		EQUALS	BETA12	+2	# DSP NOUN 60 FOR P61,62,63
GAMMAEI		EQUALS	VPRED	+2	# DSP NOUN 60 FOR P61,62,63

# DISPLAY REGISTER FOR VG		(2D)
VGDISP		EQUALS	GAMMAEI	+2	# B(2)DSP N.40,42,99 FOR P30,34,35,37,40,
					#	    41 VG DISPLAY

# SOME P11 DISPLAY REGISTERS		(6D)
ALTI		EQUALS	TTE1	+2	# 2P DSP NOUN 62 FOR P11.
HDOT		EQUALS	ALTI	+2	# 2P DSP NOUN 62 FOR P11.

# Page 121
# *-*-*-*- OVERLAY NUMBER 2 IN EBANK 7 -*-*-*-*

# KALCMANU STORAGE.				(18D)
MFS		EQUALS	END-DELV	# I(18)
MFI		EQUALS	MFS		# I    TMP
DEL		EQUALS	MFS		# I    TMP
END-KALC	EQUALS	MFS	+18D	# **NEXT AVAIL LOC AFTER KALCMANU**

# MEASUREMENT INCORPORATION STORAGE (R22) STORAGE.	(56D)

TX789		EQUALS	END-KALC	# I(6)TMP
GAMMA		EQUALS	TX789 	+6	# I(3)TMP
OMEGA		EQUALS	GAMMA 	+2	# I(18)TMP
BVECTOR		EQUALS	OMEGA 	+18D	# I(18)TMP
DELTAQ		EQUALS	BVECTOR +18D	# I(2)TMP
VARIANCE	EQUALS	DELTAQ 	+2	# I(3)TMP
RCLP		EQUALS	VARIANCE +3	# I(6)TMP
GRP2SVQ		EQUALS	RCLP 	+6	# I(1)TMP QSAVE FOR RESTARTS

# P20, P22, P23 DSP NOUN		(5D)
N49DISP		EQUALS	BVECTOR		# B(5)TMP

# S22.1 STORAGE.			(36D)
SVMRKDAT	EQUALS	GRP2SVQ	+1	# I(36)TMP 5 SETS OF MARK DATA +PAD OF ONE

# **** CISLUNAR NAV.  ERAS.  (P20'S) ****	(45D)
TRUNX		EQUALS	SVMRKDAT +36D
DATATEST	EQUALS	TRUNX		#  (1)
UBAR0		EQUALS	TRUNX	+1
UBAR1		EQUALS	UBAR0	+6
UBAR2		EQUALS	UBAR1	+6
RZC		EQUALS	UBAR2	+6
VZC		EQUALS	RZC	+6
UCLSTAR		EQUALS	VZC	+6
USSTAR		EQUALS	UCLSTAR	+6
SRRETURN	EQUALS	USSTAR	+6

# Page 122
# *-*-*-*- OVERLAY NUMBER 3 IN EBANK 7 -*-*-*-*

# RENDEZVOUS GUIDANCE STORAGE. -- P32 ... P35 --	(8D)
DELTEEO		EQUALS	END-KALC	# I(2) S-S BACK VALUES OF DELTA TIME
DELEL		EQUALS	DELTEEO	+2	# I(2) S-S
SECMAX		EQUALS	DELEL	+2	# I(2) S-S MAX STOP SIZE FOR ROUTINE
XXXALT		EQUALS	SECMAX	+2	# I(2)

# S40.9 STORAGE				(16D)
VG		EQUALS	XXXALT	+2	# I(6)TMP
VRPREV		EQUALS	VG	+6	# I(6)
TNIT		EQUALS	VRPREV	+6	# I(2)
TNITPREV	EQUALS	TNIT	+2	# I(2)

# S40.2,3 STORAGE.			(1D)
AXISCODE	EQUALS	TNITPREV +2	# I(1)IN

# P30'S-P17 COMMON STORAGE.		(24D)
RACT3		EQUALS	GRP2SVQ	+1	# I(6)TMP POSITION OF ACTIVE AT TPI TIME.
VACT3		EQUALS	RACT3	+6	# I(6)TMP VELOCITY OF ACTIVE AT TPI TIME.
RPASS3		EQUALS	VACT3	+6	# I(6)TMP POSITION OF PASSIVE AT TPI TIME.
VPASS3		EQUALS	RPASS3	+6	# I(6)TMP VELOCITY OF PASSIVE AT TPI TIME.

# P76, N84 DISPLAY			(6D)
DELVOV		EQUALS	RACT3		# I(6)DSP NOUN 84 FOR X-V84, P34-35

# INITVEL/MIDGIM STORAGE.		(34D)
#	(CALLED BY S34.1,2, S35.1,2, AND S40.9)
#	(CALLS LAMBERT, CONIC SUBROUTINES)
RINIT		EQUALS	VPASS3	+6	# I(6)IN ACTIVE VEHICLE RADIUS VECTOR
VINIT		EQUALS	RINIT	+6	# I(6)IN ACTIVE VEHICLE VELOCITY VECTOR
RTARG1		EQUALS	VINIT	+6	# I(6)TMP SHIFTED RTARG
VIPRIME		EQUALS	RTARG1	+6	# I(6)OUT NEW VEL REQ AT INITIAL RADIUS
VTPRIME		EQUALS	VIPRIME	+6	# I(6)OUT TOTAL VELOCITY AT DESIRED RADIUS
+MGA		EQUALS	VTPRIME	+6	# I(2)DSP NOUN 45 FOR P30,34,35.  +MID GIM.
COZY4		EQUALS	+MGA	+2	# I(2)TMP COSINE OF ANGLE WHEN ROT STARTS

# THE FOLLOWING OVERLAYS MEASUREMENT INCORP AND CANNOT SHARE WITH TPI
# Page 123
INTIME		EQUALS	AXISCODE +3
ITCTR		EQUALS	INTIME	+2	# I(1)TMP ITERATION COUNTER
END-IN/M	EQUALS	COZY4	+2	# ** NEXT AVAIL LOC AFTER INITVEL/MIDGIM **

# P34 AND P33 STORAGE.  (OVERLAYS INITVEL/MIDGIM)	(24D)
VAPREC		EQUALS	RINIT		# I(6) S-S PREC VEC FOR NOM TPI TIME (ACT V)
RAPREC		EQUALS	VINIT		# I(6) S-S PREC VEC FOR NOM TPI TIME (ACT V)
VPPREC		EQUALS	VIPRIME		# I(6) S-S PREC VEC FOR NOM TPI TIME (PASS)
RPPREC		EQUALS	VTPRIME		# I(6) S-S PREC VEC FOR NOM TPI TIME (PASS)

# P30, P40 INTERFACE.			(20D)
RTIG		EQUALS	END-IN/M	# I(6)TMP
VTIG		EQUALS	RTIG	+6	# I(6)TMP
DELVSIN		EQUALS	VTIG	+6	# I(6)TMP
DELVEET3	EQUALS	DELVSIN		#     TMP DELTA VEL VECT INERTIAL COORDS.
VGTEMP		EQUALS	DELVEET3
DELVSAB		EQUALS	DELVSIN	+6	# I(2)TMP

# P35-P40 INTERFACE STORAGE.  (OVERLAYS P30-P40 I/F STORAGE)	(12D)
RPASS4		EQUALS	RTIG		# I(6)TMP POSITION OF PASSIVE AT INTERCEPT
VPASS4		EQUALS	RPASS4	+6	# I(6)TMP VELOCITY OF PASSIVE AT INTERCEPT

# TPI SEARCH (P17)			(6D)
E2		EQUALS	VPASS4	+6	# I(6)TMP

# P30-P40 COMMON STORAGE.		(3D)
TPASS4		EQUALS	DELVSAB	+2	# I(2)TMP
TINT		=	TPASS4		# I(2)
QTEMP		EQUALS	TPASS4	+2	# I(1)TMP

# P30-P40 STORAGE.			(4D)
TTOGO		EQUALS	QTEMP	+1	# B(2)DSP NOUN 35,40,45,59,99
					#	  FOR P30,34,35,40,41,47, R30.
TTPI		EQUALS	TTOGO	+2	# B(2)DSP NOUN 37 FOR P34 TPI TIME, CSECS.
# Page 124
END-P30S	EQUALS	TTPI	+2	# ** NEXT AVAIL LOC AFTER P30-40 STORAGE. **

# P40 STORAGE.				(8D)
VGBODY		EQUALS	END-P30S	# B(6)DSP NOUN 85 FOR P40,41,42 VG-SC COOR
DELVCTL		=	VGBODY
P40TMP		EQUALS	VGBODY	+6	# B(2)TMP

# P47 STORAGE.
DV47TEMP	EQUALS	VG
DELVIMU		EQUALS	P40TMP	+2	# I(6)DSP NOUN 83 FOR P47 DELTAV(IMU).

# S40.1 STORAGE.			(23D)
CSTEER		EQUALS	DELVIMU	+6	# I(2)IN
BDT		EQUALS	CSTEER	+2	# I(6)IN
UT		EQUALS	BDT	+6	# I(6)OUT THRUST DIRECTION
VGTIG		EQUALS	UT	+6	# I(6)OUT
VGPREV		=	VGTIG
F		EQUALS	VGTIG	+6	# I(2)OUT S40.3 NEEDS THIS

QTEMP1		EQUALS	F	+2	# I(1)TMP HOLDS RETURN

# R41					(2D)
T-TO-ADD	EQUALS	QTEMP1	+1	# I(1D) FOR MIDTOAVE

# Page 125
# *-*-*-*- OVERLAY NUMBER 4 IN EBANK 7 -*-*-*-*

# S35.1 STORAGE.			(2D)
TSTRT		EQUALS	END-P30S	# I(2)IN MIDCOURSE START TIME

# S34.1 STORAGE.  (OVERLAYS S35.1 STORAGE)	(1)
TITER		EQUALS	TSTRT		# I(1)TMP ITERATION COUNTER

# (P30-31 Q-SAVES)			(1)
P30/31RT	EQUALS	TITER		# B(1) RETURN POINT

# P22 STORAGE.				(6D)
S22WUNL		EQUALS	TSTRT	+2	# 1	WUNL W8 UNKNOWN INIT VALUE.
S22TOFF		EQUALS	S22WUNL	+1	# 2	T SUB OFF
S22TPRIM	EQUALS	S22TOFF	+2	# 2	SAVE TF
S22EORM		EQUALS	S22TPRIM +2	# 0 = EARTH -- NON-ZERO = MOON

# DOWNLINK ERASABLES FOR P22, P20 MARK DATA.	(8D)
MARKDOWN	EQUALS	S22EORM	+1	# B(1)
RM		EQUALS	S22RTNEX	# DOWNLINK OF VHF RANGE

# S22.1					(1D)
S22RTNEX	EQUALS	MARKDOWN +7	# B(1)

# P22 STORAGE				(6D)
STARSAV3	EQUALS	S22RTNEX +1	# I(6)TMP

# CRS61.1 STORAGE.  --A SUBSET OF P20--	(14D)
Q611		EQUALS	RM	+1	# I(1)TMP QSAVE
Q6111		EQUALS	Q611	+1	# I(1)TMP QSAVE
SAVEPOS		EQUALS	Q6111	+1	# I(6)TMP LEM POSITION VECTOR
# Page 126
SAVEVEL		EQUALS	SAVEPOS	+6	# I(6)TMP LEM VELOCITY VECTOR

# ATTITUDE MANEUVER -- CALLED BY P20,R61,R63,CRS61.1	(3D)
PRAXIS		EQUALS	SAVEVEL	+6	# B(3) S-S DISP RES FOR PREF AXIS N95.

# MARK ROUTINE (R21) STORAGE.  -- IS SUBSET OF R22 --	(14D)
MRKBUF1		EQUALS	PRAXIS	+3	# B(7)TMP R21 MARK BUFFER.
MRKBUF2		EQUALS	MRKBUF1	+7	# B(7)TMP R21 MARK BUFFER.

# MORE CONICS STORAGE.			(4)
COGA		EQUALS	3774		# I(2) COTAN OF INITIAL FLIGHT PATH ANGLE
INDEP		EQUALS	COGA		# I(1) USED BY SUBROUTINE 'ITERATOR'
EPSILONL	EQUALS	COGA	+2	# I(2)TMP

# RENDEZVOUS GUIDANCE STORAGE.  -- P32...P35 --	(10D)
ELEV		EQUALS	MRKBUF2	+7	# I(2)TMP
RTX1		EQUALS	ELEV	+2	#  (1)
RTX2		EQUALS	RTX1	+1	#  (1)
RTMU		EQUALS	RTX2	+1	#  (2)
RTSR1/MU	EQUALS	RTMU	+2	#  (2)
CENTANG		EQUALS	RTSR1/MU +2	# I(2) S-S CENTRAL ANGLE COVERED (TPI-TFF)

# TPI SEARCH (S17.1, S17.2) P17 STORAGE.(10D)
DELTEE		EQUALS	MRKBUF2	+7	# I(2)
XRS		EQUALS	DELTEE	+2	# I(2)
THETL		EQUALS	XRS	+2	# I(2)
TF		EQUALS	THETL	+2	# I(2)
DELHITE		EQUALS	TF	+2	#  (2)

# Page 127
# *-*-*-*- OVERLAY NUMBER 5 IN EBANK 7 -*-*-*-*

# P17,P34				(2D)
NN1		=	NN		# I(2)DSP NOUN 55,R1

# ********* THE FOLLOWING ARE FOR FLIGHT 504 ONLY *************

# RETURN-TO-EARTH STORAGE.		(93D)
RTEDVD		EQUALS	END-IN/M	# I(2)IN DELTA VELOCITY DESIRED		M/CS B7
RTEGAM2D	EQUALS	RTEDVD	+2	# I(2)IN REENTRY ANGLE DESIRED		REVS B0
RCON		EQUALS	RTEGAM2D +2	# I(2)TMP CONIC R2 RADIUS		M B29
R(T1)/		EQUALS	RCON	+2	# I(6)TMP POSITION VECTOR AT TIG	M B29/B27
R(T1)		EQUALS	R(T1)/	+6	# I(2)TMP MAGNITUDE OF R(T1)/		M B29/B27
DT21PR		EQUALS	R(T1)	+2	# I(2)TMP PREVIOUS DT21			CS B30
MAMAX1		EQUALS	DT21PR	+2	# I(2)TMP MAJ AXIS LOW BOUND LMT	M B30
MAMAX2		EQUALS	MAMAX1	+2	# I(2)TMP MAJ AXIS UP BOUND LMT		M B30
R(T2)/		EQUALS	MAMAX2	+2	# I(6)TMP FINAL POSITION VECTOR		M B29/B27
RD		EQUALS	R(T2)/	+6	# I(2)TMP FINAL R DESIRED		M B29/B27
DRCON		EQUALS	RD	+2	# I(2)TMP RCON SLOPE ITERATOR		M B29/B27
RPRE'		EQUALS	DRCON	+2	# I(2)TMP PREVIOUS RPRE			M B29/B27
V(T1)/		EQUALS	RPRE'	+2	# I(6)TMP VEL VECTOR AT TIG		M/CS B7/B5
V2(T1)/		EQUALS	V(T1)/	+6	# I(6)TMP POST IMP VEL AT TIG		M/CS B7/B5
DV		EQUALS	V2(T1)/	+6	# I(2)TMP DELTA VELOCITY AT TIG		M/CS B7/B5
V(T2)/		EQUALS	DV	+2	# I(6)TMP FINAL VELOCITY VECTOR		M/CS B7/B5
T1		EQUALS	V(T2)/	+6	# I(2)TMP INITIAL VECTOR TIME		CS B28
PCON		EQUALS	T1	+2	# I(2)TMP SEMI-LATUS RECTUM		M B29
X(T1)		EQUALS	PCON	+2	# I(2)TMP COTANGENT GAMMA1		B5
T12		EQUALS	X(T1)	+2	# I(2)TMP INIT TO FINAL POSIT TIME	CS B28
DELTAT		EQUALS	T12	+2	# I(2)TMP DELTA T IN SAVE PERILUNE	CS B28
NN1A		EQUALS	DELTAT	+2	# I(2)TMP ITERATION COUNTER 1
NN2		EQUALS	NN1A	+2	# I(2)TMP ITERATION COUNTER 2
RTENCKEX	EQUALS	NN2	+2	# I(1)TMP RTENCK RETURN ADDRESS
CONICX1		EQUALS	RTENCKEX +1	# I(1)TMP CONICS MU TABLE INDEX
T2		EQUALS	CONICX1	+1	# I(2)TMP FINAL TIME			CS B28
UR1/		EQUALS	T2	+2	# I(6)TMP UNIT R(T1)/			B1
UV1/		EQUALS	UR1/	+6	# I(6)TMP UNIT V(T1)/			B1
BETA1		EQUALS	UV1/	+6	# I(2)TMP 1+X(T2)**2			B1
P(T1)		EQUALS	BETA1	+2	# I(1)TMP PRIMARY BODY STATE TIME 1	B14
CFPA		EQUALS	P(T1)	+1	# I(2)TMP COSINE FLIGHT PATH ANGLE	B1
PHI2		EQUALS	CFPA	+2	# I(2)TMP PERI OR APO INDICATOR		B2
SPRTEX		EQUALS	PHI2	+2	# I(1)TMP ROUTINE RETURN ADDRESS
VNSTORE		EQUALS	SPRTEX	+1	# I(1)TMP VERBNOUN STORAGE
BETA12		EQUALS	VNSTORE	+1	# I(2)TMP SIGN FOR TIMERAD

# OVERLAYS WITHIN RETURN-TO-EARTH STORAGE.
# Page 128
RPRE		EQUALS	24D		# I(2)TMP COMPUTED PREC RADIUS		M B29/B27
P/RPRE		EQUALS	26D		# I(2)TMP P/R				B4
R/APRE		EQUALS	28D		# I(2)TMP R/A				B6
X(T2)PRE	EQUALS	T12		# I(2)TMP PREC COTAN GAMMA2		B0
X(T2)		EQUALS	DELTAT		# I(2)TMP COTAN GAMMA2			B0
UH/		EQUALS	UV1/		# I(2)TMP UNIT HORIZONTAL VECTOR.	B1
SPRTETIG	EQUALS	TIG		# I(2)IN TIME OF IGNITION		CS B28

# Page 129
# *-*-*-*- OVERLAY 6 IN EBANK 7 -*-*-*-*
# P32,P33

# THE FOLLOWING OVERLAY MEAS. INCORP. ARE AND IN USE ONLY WHEN (32D)
POSTCSI		EQUALS	VG		# I(2)
DELVCSI		EQUALS	POSTCSI	+2	# I(2)
DELDV		EQUALS	DELVCSI	+2	# I(2)
GAMPREV		EQUALS	DELDV	+2	# I(2)
DVPREV		EQUALS	GAMPREV +2	# I(2)
POSTCDH		EQUALS	DVPREV	+2	# I(2)
HAFPA1		EQUALS	POSTCDH
VACT4		EQUALS	POSTCDH	+2	# I(6)
RDOTV		EQUALS	VACT4	+6	# I(2)
VACT1		EQUALS	RDOTV	+2	# I(6)
VPASS1		EQUALS	VACT1	+6	# I(6) VEL. PASSIVE VEH. AT CSI TIME

UNVEC		EQUALS	VACT3

T2TOT3		EQUALS	TPASS4		# I(2) TPI - TCDH

CSIALRM		EQUALS	TITER		# I(2) ALARM INDEX

DELVEET2	EQUALS	S22WUNL		# I(6) VACT3 - VACT2 = DVCDH REF. COORD.

# ADDITIONAL CSI - CDH STORAGE.		(10D)
RPASS1		EQUALS	CENTANG	+2	# I(6) POS. PASSIVE VEH. AT CSI TIME.
LOOPCT		EQUALS	RPASS1	+6	# I(2) ITERATION COUNTER
NN		EQUALS	LOOPCT	+2	# I(2)

# P21 STORAGE				(19D)
P21ORIG		EQUALS	TRUNX		# I(1)
P21BASER	EQUALS	P21ORIG	+1	# I(6)
P21BASEV	EQUALS	P21BASER +6	# I(6)
P21ALT		EQUALS	P21BASEV +6	# I(2) NOUN 73 R1 ALTITUDE
P21VEL		EQUALS	P21ALT	+2	# I(2) NOUN 73 R2 VELOCITY
P21GAM		EQUALS	P21VEL	+2	# I(2) NOUN 73 R3 FLIGHT PATH ANGLE

# The following two statements had been just "WHOCARES = 3777".---RSB 2009
		SETLOC	3777
WHOCARES	EQUALS			# A DUMMY FOR E-BANK INSENSITIVE 2CADRS.
END-E7		EQUALS	WHOCARES	# ***** LAST LOCATION IN E7

# Page 130 ... is empty.

